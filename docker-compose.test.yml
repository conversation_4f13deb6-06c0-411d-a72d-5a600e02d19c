version: '3.8'

services:
  test_postgres:
    labels:
      - 'infra=true'
    image: postgres:16
    environment:
      POSTGRES_USER: ${TEST_DB_USER:-payment-hub-test}
      POSTGRES_PASSWORD: ${TEST_DB_PASSWORD:-payment-hub-test}
      POSTGRES_DB: ${TEST_DB_NAME:-payment_hub_test}
    volumes:
      - payment-hub-postgres-test-data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U payment_hub_test']
      interval: 5s
      timeout: 10s
      retries: 5
    networks:
      - payment-hub-test

  test_rabbitmq:
    labels:
      - 'infra=true'
    image: rabbitmq:management
    environment:
      RABBITMQ_DEFAULT_USER: ${TEST_RABBITMQ_USER:-payment-hub-test}
      RABBITMQ_DEFAULT_PASS: ${TEST_RABBITMQ_PASSWORD:-payment-hub-test}
      RABBITMQ_DEFAULT_VHOST: payment-hub
    healthcheck:
      test: ['CMD', 'rabbitmqctl', 'status']
      interval: 10s
      timeout: 10s
      retries: 5
    networks:
      - payment-hub-test

volumes:
  payment-hub-postgres-test-data:

networks:
  payment-hub-test:
    name: payment-hub-test
    driver: bridge
