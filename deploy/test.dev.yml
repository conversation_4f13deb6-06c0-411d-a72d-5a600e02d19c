- hosts: all
  vars:
    DEPLOY_FOLDER: /home/<USER>/payment-hub

  tasks:
    - import_tasks: common-tasks.yml

    - name: Run tests
      shell: make test
      args:
        chdir: "{{ DEPLOY_FOLDER }}"
      register: result

    - debug: msg={{ result.stdout }}

    - name: Run e2e tests
      shell: make test-e2e
      args:
        chdir: "{{ DEPLOY_FOLDER }}"
      register: result_e2e

    - debug: msg={{ result_e2e.stdout }}
