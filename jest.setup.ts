import { config } from 'dotenv';
config();

import { DatabaseConfig } from './src/config/database.config';
import { AppEnv, ServerConfig } from './src/config/server.config';

if (ServerConfig.nodeEnv !== AppEnv.TEST) {
  throw new Error('NODE_ENV is not set to test');
}

const databaseUrl = DatabaseConfig.databaseUrl;

if (!databaseUrl.includes('test')) {
  throw new Error('Tests are not running against the test database.');
}
