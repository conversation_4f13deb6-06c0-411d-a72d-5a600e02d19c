import { Refund, RefundStatus, RefundType } from '@/domain/payment/refund';

import { EntityMockBuilder } from './builder';
import { money } from './money.factory';

class RefundMockBuilder extends EntityMockBuilder<Refund, Refund['props']>{
  constructor() {
    super('refund-id', {
      status: RefundStatus.PENDING,
      type: RefundType.FULL,
    });
  }

  public create(): Refund {
    return new Refund({
      id: this.entityId,
      props: this.props,
    });
  }

  public amount(amount: number): this {
    this.props.amount = money(amount);
    return this;
  }

  public issued(): this {
    this.props.status = RefundStatus.ISSUED;
    return this;
  }

  public canceled(): this {
    this.props.status = RefundStatus.CANCELED;
    return this;
  }

  public failed(): this {
    this.props.status = RefundStatus.FAILED;
    return this;
  }

  public marketplaceOrderFeeRefunded(amount: number): this {
    this.props.marketplaceOrderFeeRefunded = money(amount);
    return this;
  }

  public platformOrderFeeRefunded(amount: number): this {
    this.props.platformOrderFeeRefunded = money(amount);
    return this;
  }
}

export function refund(amount: number): RefundMockBuilder {
  return new RefundMockBuilder().amount(amount);
}
