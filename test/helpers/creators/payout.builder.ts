import { Payout } from '@/domain/payout/payout';
import { PayoutReversal } from '@/domain/payout/payout-reversal';

import { EntityMockBuilder } from './builder';
import { money } from './money.factory';

class PayoutMockBuilder extends EntityMockBuilder<Payout, Payout['props']>{
  constructor() {
    super('payout-id', {});
  }

  public create(): Payout {
    return new Payout({
      id: this.entityId,
      props: this.props,
    });
  }

  public amount(amount: number): this {
    this.props.amount = money(amount);
    return this;
  }

  public reversedAmount(reversedAmount: number): this {
    this.props.reversedAmount = money(reversedAmount);
    return this;
  }

  public reversals(reversals: PayoutReversal[]): this {
    this.props.reversals = reversals;
    return this;
  }
}

export function payout(amount: number): PayoutMockBuilder {
  return new PayoutMockBuilder().amount(amount);
}
