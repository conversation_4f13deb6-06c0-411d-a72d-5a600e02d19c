import {
  PaymentSplitRefund,
  PaymentSplitRefundStatus,
} from '@/domain/payment-split/payment-split-refund';

import { EntityMockBuilder } from './builder';
import { money } from './money.factory';

class PaymentSplitRefundMockBuilder extends EntityMockBuilder<PaymentSplitRefund, PaymentSplitRefund['props']>{
  constructor() {
    super('payment-split-refund-id', {
      status: PaymentSplitRefundStatus.PENDING,
    });
  }

  public create(): PaymentSplitRefund {
    return new PaymentSplitRefund({
      id: this.entityId,
      props: this.props,
    });
  }

  public amount(amount: number): this {
    this.props.amount = money(amount);
    return this;
  }

  public issued(): this {
    this.props.status = PaymentSplitRefundStatus.ISSUED;
    return this;
  }

  public processing(): this {
    this.props.status = PaymentSplitRefundStatus.PROCESSING;
    return this;
  }

  public canceled(): this {
    this.props.status = PaymentSplitRefundStatus.CANCELED;
    return this;
  }

  public failed(): this {
    this.props.status = PaymentSplitRefundStatus.FAILED;
    return this;
  }
}

export function paymentSplitRefund(amount: number): PaymentSplitRefundMockBuilder {
  return new PaymentSplitRefundMockBuilder().amount(amount);
}
