import { Payment, PaymentStatus } from '@/domain/payment/payment';
import { Refund } from '@/domain/payment/refund';

import { EntityMockBuilder } from './builder';
import { money } from './money.factory';

class PaymentMockBuilder extends EntityMockBuilder<Payment, Payment['props']> {
  constructor() {
    super('payment-id', {
      status: PaymentStatus.PENDING,
    });
  }

  public create(): Payment {
    return new Payment({
      id: this.entityId,
      props: this.props,
    });
  }

  public amount(amount: number): this {
    this.props.amount = money(amount);
    return this;
  }

  public refundedAmount(amount: number): this {
    this.props.refundedAmount = money(amount);
    return this;
  }

  public refunds(refunds: Refund[]): this {
    this.props.refunds = refunds;
    return this;
  }

  public settled(): this {
    this.props.status = PaymentStatus.SETTLED;
    return this;
  }

  public marketplaceOrderFee(amount: number): this {
    this.props.marketplaceOrderFee = money(amount);
    return this;
  }

  public marketplaceOrderFeeRefunded(amount: number): this {
    this.props.marketplaceOrderFeeRefunded = money(amount);
    return this;
  }

  public platformOrderFee(amount: number): this {
    this.props.platformOrderFee = money(amount);
    return this;
  }

  public platformOrderFeeRefunded(amount: number): this {
    this.props.platformOrderFeeRefunded = money(amount);
    return this;
  }
}

export function payment(amount: number): PaymentMockBuilder {
  return new PaymentMockBuilder().amount(amount);
}
