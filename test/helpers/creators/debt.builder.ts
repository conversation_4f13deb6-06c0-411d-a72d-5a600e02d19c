import { Debt, DebtStatus } from '@/domain/debt/debt';

import { EntityMockBuilder } from './builder';
import { money } from './money.factory';

class DebtMockBuilder extends EntityMockBuilder<Debt, Debt['props']>{
  constructor() {
    super('debt-id', {
      status: DebtStatus.PENDING,
    });
  }

  public create(): Debt {
    return new Debt({
      id: this.entityId,
      props: this.props,
    });
  }

  public amount(amount: number): this {
    this.props.amount = money(amount);
    return this;
  }

  public paidAmount(amount: number): this {
    this.props.paidAmount = money(amount);
    return this;
  }

  public canceled(): this {
    this.props.status = DebtStatus.CANCELED;
    return this;
  }

  public partiallyPaid(): this {
    this.props.status = DebtStatus.PARTIALLY_PAID;
    return this;
  }

  public settled(): this {
    this.props.status = DebtStatus.SETTLED;
    return this;
  }
}

export function debt(amount: number): DebtMockBuilder {
  return new DebtMockBuilder().amount(amount);
}
