import { PayoutReversal, PayoutReversalStatus } from '@/domain/payout/payout-reversal';

import { EntityMockBuilder } from './builder';
import { money } from './money.factory';

class PayoutReversalMockBuilder extends EntityMockBuilder<PayoutReversal, PayoutReversal['props']>{
  constructor() {
    super('payout-reversal-id', {
      status: PayoutReversalStatus.PENDING,
    });
  }

  public create(): PayoutReversal {
    return new PayoutReversal({
      id: this.entityId,
      props: this.props,
    });
  }

  public amount(amount: number): this {
    this.props.amount = money(amount);
    return this;
  }

  public succeeded(): this {
    this.props.status = PayoutReversalStatus.SUCCEEDED;
    return this;
  }

  public failed(): this {
    this.props.status = PayoutReversalStatus.FAILED;
    return this;
  }

  public processing(): this {
    this.props.status = PayoutReversalStatus.PROCESSING;
    return this;
  }
}

export function payoutReversal(amount: number): PayoutReversalMockBuilder {
  return new PayoutReversalMockBuilder().amount(amount);
}
