export abstract class EntityMockBuilder<E, P> {
  protected props: P;

  protected constructor(protected entityId: string, partialProps: Partial<P>) {
    this.props = partialProps as P;
  }

  public id(id: string): this {
    this.entityId = id;
    return this;
  }

  public withProps(props: Partial<P>): this {
    Object.assign(this.props, props);
    return this;
  }

  public abstract create(): E;
}
