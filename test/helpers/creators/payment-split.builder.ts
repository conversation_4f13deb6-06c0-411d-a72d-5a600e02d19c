import { PaymentSplit, PaymentSplitStatus } from '@/domain/payment-split/payment-split';
import { PaymentSplitRefund } from '@/domain/payment-split/payment-split-refund';

import { EntityMockBuilder } from './builder';
import { money } from './money.factory';

class PaymentSplitMockBuilder extends EntityMockBuilder<PaymentSplit, PaymentSplit['props']>{
  constructor() {
    super('payment-split-id', {
      status: PaymentSplitStatus.PENDING,
    });
  }

  public create(): PaymentSplit {
    return new PaymentSplit({
      id: this.entityId,
      props: this.props,
    });
  }

  public accountId(accountId: string): this {
    this.props.accountId = accountId;
    return this;
  }

  public amount(amount: number): this {
    this.props.amount = money(amount);
    return this;
  }

  public refundedAmount(refundedAmount: number): this {
    this.props.refundedAmount = money(refundedAmount);
    return this;
  }

  public refunds(refunds: PaymentSplitRefund[]): this {
    this.props.refunds = refunds;
    return this;
  }
}

export function paymentSplit(amount: number, accountId: string = 'account-id-0'): PaymentSplitMockBuilder {
  return new PaymentSplitMockBuilder().amount(amount).accountId(accountId);
}
