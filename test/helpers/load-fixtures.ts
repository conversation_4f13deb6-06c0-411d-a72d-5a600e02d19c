import * as path from 'path';

import { Loader, Resolver } from '@getbigger-io/prisma-fixtures-cli';
import { PrismaClient } from '@prisma/client';

const PROJECT_DIR = process.cwd();
const FIXTURES_PATH = path.resolve(PROJECT_DIR, 'test', 'fixtures');

export const loadFixtures = async (connection: PrismaClient, fixturesPath: string) => {
  const loader = new Loader();
  loader.load(path.resolve(FIXTURES_PATH, fixturesPath));

  const resolver = new Resolver();
  const fixtures = resolver.resolve(loader.fixtureConfigs);

  return Promise.all(
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    fixtures.map((fixture) => connection[fixture.entity].create({ data: fixture.data })),
  );
  // const builder = new Builder(connection, new Parser());

  // const entities = [];

  // for (const fixture of fixturesIterator(fixtures)) {
  //   const entity = await builder.build(fixture);
  //   entities.push(entity);
  // }

  // return entities;
};
