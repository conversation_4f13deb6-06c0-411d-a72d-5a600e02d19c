{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "verbose": true, "testTimeout": 15000, "setupFilesAfterEnv": ["<rootDir>/../jest.setup.ts"], "moduleNameMapper": {"@/libs/stripe/(.*)": "<rootDir>/../libs/stripe/src/$1", "@/libs/stripe": "<rootDir>/../libs/stripe/src", "@/(.*)": "<rootDir>/../src/$1"}}