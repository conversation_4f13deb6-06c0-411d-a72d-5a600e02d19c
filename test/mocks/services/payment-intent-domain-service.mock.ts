import { PaymentIntent } from '@/domain/payment-intent/payment-intent';
import {
  ConfirmPaymentIntentParams,
  UpdatePaymentIntentParams,
  CreatePaymentIntentParams,
} from '@/domain/payment-intent/services';

export class PaymentIntentDomainServiceMock {
  public async create(params: CreatePaymentIntentParams): Promise<PaymentIntent> {
    void params;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async update(paymentIntent: PaymentIntent, params: UpdatePaymentIntentParams): Promise<void> {
    void paymentIntent;
    void params;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async confirm(params: ConfirmPaymentIntentParams): Promise<void> {
    void params;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
