import { RefundProcessingDomainService } from '@/domain/payment/services';
import { PaymentSplit } from '@/domain/payment-split/payment-split';

type ProcessRefundFailureReturn = ReturnType<RefundProcessingDomainService['processRefundFailure']>;

export class RefundProcessingDomainServiceMock {
  public processRefundFailure(paymentSplits: PaymentSplit[], refundId: string): ProcessRefundFailureReturn {
    void paymentSplits;
    void refundId;
    throw new Error('Mock implementation not provided');
  }
}
