import { LoggerService } from '@nestjs/common';
import { Logger } from '@sw-web/nestjs-core/logger';

export class AppLoggerServiceMock implements LoggerService {
  public debug(message: string, ...optionalParams: unknown[]): void {
    void message;
    void optionalParams;
    throw new Error('Mock implementation not provided');
  }

  public info(message: string, ...optionalParams: unknown[]): void {
    void message;
    void optionalParams;
    throw new Error('Mock implementation not provided');
  }

  public warn(message: string, ...optionalParams: unknown[]): void {
    void message;
    void optionalParams;
    throw new Error('Mock implementation not provided');
  }

  public error(message: string, ...optionalParams: unknown[]): void {
    void message;
    void optionalParams;
    throw new Error('Mock implementation not provided');
  }

  public log(message: string, ...optionalParams: unknown[]): void {
    void message;
    void optionalParams;
    throw new Error('Mock implementation not provided');
  }

  public child(metadata?: Record<string, unknown>): Logger {
    void metadata;
    throw new Error('Mock implementation not provided');
  }
}
