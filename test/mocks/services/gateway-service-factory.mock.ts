import { GatewayCustomer } from '@/domain/customer/gateway-customer';
import { GatewayRefund } from '@/domain/payment/gateway-refund';
import { GatewayPaymentIntent } from '@/domain/payment-intent/payment-intent';
import {
  CreateGatewayPaymentIntentParams,
  CreateGatewayRefundParams,
  CreateInboundGatewayTransferParams,
  CreateOutboundGatewayTransferParams,
  Gateway,
  GatewayServiceFactoryPort,
  GatewayServicePort,
  GatewayType,
  Money,
  NotSupportedGatewayTypeError,
  UpdateGatewayPaymentIntentParams,
} from '@/domain/shared';
import { GatewayTransfer } from '@/domain/shared/interfaces';

export type GatewayAccountMock = {
  id: string;
  email: string | null;
};

export class MockGatewayServiceAdapter implements GatewayServicePort {
  public db: GatewayAccountMock[];

  constructor(public gateway: Gateway) {
    const type = gateway.getType();
    this.db = Array.from({ length: 3 }, (_, i) => ({
      id: `account-id-${i}-${type}`,
      email: `test-${i}@mail.com`,
    }));
  }

  public async getAccount(accountId: string): Promise<GatewayAccountMock> {
    const account = this.db.find((el) => el.id === accountId);
    if (!account) {
      throw new Error();
    }
    return Promise.resolve(account);
  }

  public async createCustomer(): Promise<GatewayCustomer> {
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async createInboundTransfer(
    data: CreateInboundGatewayTransferParams,
  ): Promise<GatewayTransfer> {
    void data;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async createOutboundTransfer(
    data: CreateOutboundGatewayTransferParams,
  ): Promise<GatewayTransfer> {
    void data;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async createPaymentIntent(
    data: CreateGatewayPaymentIntentParams,
  ): Promise<GatewayPaymentIntent> {
    void data;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async createRefund(data: CreateGatewayRefundParams): Promise<GatewayRefund> {
    void data;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async getProviderFee(gatewayPaymentIntent: GatewayPaymentIntent): Promise<Money> {
    void gatewayPaymentIntent;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public getValidatedWebhookData(req: Request, data: object): object {
    void req;
    void data;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async updatePaymentIntent(
    data: UpdateGatewayPaymentIntentParams,
  ): Promise<GatewayPaymentIntent> {
    void data;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async getFingerprintByPaymentMethodId(paymentMethodId: string): Promise<string> {
    void paymentMethodId;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}

export class GatewayServiceFactoryMock implements GatewayServiceFactoryPort {
  public getGateway(gateway: Gateway): MockGatewayServiceAdapter {
    const type = gateway.getType();
    switch (type) {
      case GatewayType.STRIPE:
      case GatewayType.BRAINTREE:
        return new MockGatewayServiceAdapter(gateway);
      default:
        throw new NotSupportedGatewayTypeError();
    }
  }
}
