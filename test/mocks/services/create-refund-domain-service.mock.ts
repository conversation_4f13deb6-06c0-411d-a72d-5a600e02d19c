import { Refund } from '@/domain/payment/refund';
import { CreateManualRefundParams, CreateRefundParams } from '@/domain/payment/services/create-refund.domain-service';

export class CreateRefundDomainServiceMock {
  public async createRefund(params: CreateRefundParams): Promise<Refund> {
    void params;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public createManualRefund(params: CreateManualRefundParams): Refund {
    void params;
    throw new Error('Mock implementation not provided');
  }
}
