import { GatewayType } from '@/domain/shared';

export class PaymentIntentApplicationServiceMock {
  public async validateAccountsExist(
    marketplaceId: string,
    gatewayType: GatewayType,
    paymentSplitsData: { accountId: string }[],
  ): Promise<void> {
    void marketplaceId;
    void gatewayType;
    void paymentSplitsData;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
