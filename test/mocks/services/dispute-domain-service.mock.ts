import { Dispute, DisputeStatus, GatewayDispute } from '@/domain/dispute/dispute';
import { DisputeDomainService, CreateProps } from '@/domain/dispute/services';
import { GatewayDisputeEntityProps } from '@/domain/dispute/stripe-dispute';

export class DisputeDomainServiceMock implements DisputeDomainService {
  public update(dispute: Dispute, data: Partial<CreateProps>): Dispute {
    void data;
    return dispute;
  }

  public create(props: CreateProps): Dispute {
    void props;
    throw new Error('Mock implementation not provided');
  }

  public createGatewayDispute(props: GatewayDisputeEntityProps): GatewayDispute {
    void props;
    throw new Error('Mock implementation not provided');
  }

  public mapToDisputeStatus(status: string): DisputeStatus.WON {
    void status;
    throw new Error('Mock implementation not provided');
  }
}
