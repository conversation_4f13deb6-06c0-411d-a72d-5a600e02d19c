class TableOperations {
  public async create(args: unknown): Promise<unknown> {
    void args;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async update(args: unknown): Promise<unknown> {
    void args;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async findFirst(args: unknown): Promise<unknown> {
    void args;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async updateMany(args: unknown): Promise<unknown> {
    void args;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}

export class DatabaseServiceMock {
  private transactionCallbacks: Array<() => Promise<unknown>> = [];

  public webhook = new TableOperations();

  constructor(public catchTransactions: boolean = true) { }

  public async $transaction<T>(cb: () => Promise<T>): Promise<T> {
    if (this.catchTransactions) {
      this.transactionCallbacks.push(cb);
    } else {
      return cb();
    }
  }

  public async invokeNextTransaction<T>(): Promise<T> {
    if (this.transactionCallbacks.length === 0) {
      throw new Error('No transaction callback captured');
    }
    const nextCallback = this.transactionCallbacks.shift();
    return await nextCallback() as T;
  }

  public async invokeAllTransactions(): Promise<void> {
    while (this.transactionCallbacks.length > 0) {
      await this.invokeNextTransaction();
    }
  }

  public clearTransactions(): void {
    this.transactionCallbacks = [];
  }

  public get transactionCount(): number {
    return this.transactionCallbacks.length;
  }

  public async $queryRaw<T>(...args: unknown[]): Promise<T> {
    void args;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
