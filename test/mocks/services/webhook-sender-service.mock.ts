import { WebhookSenderServicePort, SendWebhookResponse } from '@/domain/webhook/ports/webhook-sender-service.port';
import { Webhook } from '@/domain/webhook/webhook';

export class WebhookSenderServiceMock implements WebhookSenderServicePort {
  public async send(webhook: Webhook, secret: string): Promise<SendWebhookResponse> {
    void webhook;
    void secret;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
