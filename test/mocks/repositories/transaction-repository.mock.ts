import { AllowedCurrencies, Currency, Money } from '@/domain/shared';
import { TransactionRepositoryPort } from '@/domain/transaction/ports/transaction.repository';
import { Transaction, TransactionStatus, TransactionType } from '@/domain/transaction/transaction';

import { BaseRepositoryMock } from './repository.mock';

export class TransactionRepositoryMock extends BaseRepositoryMock<Transaction> implements TransactionRepositoryPort {
  constructor() {
    const data = Array.from({ length: 3 }).map((_, i) => new Transaction({
      id: `transaction-id-${i}`,
      props: {
        amount: new Money(1, new Currency(AllowedCurrencies.USD)),
        type: TransactionType.PAYMENT,
        status: TransactionStatus.PENDING,
      },
    }));
    super(data);
  }
}
