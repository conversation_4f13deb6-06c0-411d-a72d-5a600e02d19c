import { Debt, DebtSource, DebtStatus, DebtType } from '@/domain/debt/debt';
import { DebtRepositoryPort } from '@/domain/debt/ports';
import { AllowedCurrencies, Currency, Money } from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class DebtRepositoryMock extends BaseRepositoryMock<Debt> implements DebtRepositoryPort {
  constructor() {
    const data = Array.from({ length: 3 }).map((_, i) => new Debt({
      id: `debt-id-${i}`,
      props: {
        accountId: `account-id-${i}`,
        sourceId: `refund-split-id-${i}`,
        amount: new Money(2, new Currency(AllowedCurrencies.USD)),
        paidAmount: new Money(1, new Currency(AllowedCurrencies.USD)),
        type: DebtType.PLATFORM_TO_ACCOUNT,
        source: DebtSource.REFUND_SPLIT,
        status: DebtStatus.PENDING,
      },
    }));
    super(data);
  }
}
