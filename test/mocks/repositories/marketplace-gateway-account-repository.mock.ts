import {
  MarketplaceGatewayAccount
} from '@/domain/marketplace-gateway-account/marketplace-gateway-account';
import {
  MarketplaceGatewayAccountRepositoryPort
} from '@/domain/marketplace-gateway-account/ports';
import { Gateway, GatewayType } from '@/domain/shared';
import { BaseRepositoryMock } from '@/test/mocks/repositories/repository.mock';

export class MarketplaceGatewayAccountRepositoryMock
  extends BaseRepositoryMock<MarketplaceGatewayAccount> implements MarketplaceGatewayAccountRepositoryPort {
  constructor() {
    const gateway = new Gateway('gateway-id-0', GatewayType.STRIPE);
    const data = Array.from({ length: 3 }).map((_, i) => new MarketplaceGatewayAccount({
      id: `marketplace-gateway-account-id-${i}`,
      props: {
        name: `name-${i}`,
        gateway,
        idAtGateway: `id-at-gateway-${i}`,
        marketplaceGatewayId: `marketplace-gateway-id-${i}`,
      },
    }));
    super(data);
  }

  public async findByMarketplaceAndGatewayId(marketplaceId: string, gatewayId: string): Promise<MarketplaceGatewayAccount> {
    void marketplaceId;
    void gatewayId;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
