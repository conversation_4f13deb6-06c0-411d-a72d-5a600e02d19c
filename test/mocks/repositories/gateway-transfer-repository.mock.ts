import {
  GatewayTransferRepositoryPort,
  CreateGatewayTransferRepositoryParams,
  UpdateGatewayTransferRepositoryParams
} from '@/domain/shared';
import { GatewayTransfer, GatewayTransferSource } from '@/domain/shared/interfaces';

export class GatewayTransferRepositoryMock implements GatewayTransferRepositoryPort {
  public async findByGatewayTransferId(gatewayTransferId: string): Promise<GatewayTransfer | null> {
    void gatewayTransferId;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async findByGatewayTransferIds(gatewayTransferIds: string[]): Promise<GatewayTransfer[]> {
    void gatewayTransferIds;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async findBySource(sourceType: GatewayTransferSource, sourceId: string): Promise<GatewayTransfer | null> {
    void sourceType;
    void sourceId;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async create(params: CreateGatewayTransferRepositoryParams): Promise<GatewayTransfer> {
    void params;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async update(params: UpdateGatewayTransferRepositoryParams): Promise<void> {
    void params;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
