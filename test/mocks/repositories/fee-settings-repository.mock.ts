import { FeeSettings } from '@/domain/fee-settings/fee-settings';
import { FeeSettingsRepositoryPort } from '@/domain/fee-settings/ports';
import { AllowedCurrencies, Money } from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class FeeSettingsRepositoryMock
  extends BaseRepositoryMock<FeeSettings>
  implements FeeSettingsRepositoryPort
{
  constructor() {
    const data = Array.from({ length: 3 }).map((_, i) =>
      new FeeSettings({
        id: `fee-settings-id-${i}`,
        props: {
          platformFeeFixed: Money.from(1, AllowedCurrencies.USD),
          platformFeePercentage: 2.5,
          marketplaceFeeFixed: Money.from(0.5, AllowedCurrencies.USD),
          marketplaceFeePercentage: 1.5,
          marketplaceGatewayId: `marketplace-gateway-id-${i}`,
          isDefault: i === 0, // First one is default
        },
      }),
    );
    super(data);
  }

  async findByMarketplaceGatewayId(marketplaceGatewayId: string): Promise<FeeSettings[]> {
    const feeSettings = this.db.filter(
      (fs) => fs.getProps().marketplaceGatewayId === marketplaceGatewayId,
    );
    return Promise.resolve(feeSettings);
  }
}
