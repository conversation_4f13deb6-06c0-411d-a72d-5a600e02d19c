import { Marketplace } from '@/domain/marketplace/marketplace';
import { MarketplaceRepositoryPort } from '@/domain/marketplace/ports';
import { WebhookEndpoint } from '@/domain/marketplace/webhook-endpoint.value-object';
import { ManagedSecret } from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class MarketplaceRepositoryMock extends BaseRepositoryMock<Marketplace> implements MarketplaceRepositoryPort {
  constructor() {
    const data = Array.from({ length: 3 }).map((_, i) => new Marketplace({
      id: `marketplace-id-${i}`,
      props: {
        name: `marketplace-${i}`,
        apiKeyPair: undefined,
        webhookEndpoint: new WebhookEndpoint({
          webhookUrl: `https://webhook-url-${i}.com`,
          webhookSecret: new ManagedSecret({
            secretHash: 'hash', secretId: 'id', secretValue: 'secret'
          }),
        })
      }
    }));
    super(data);
  }

  public async findBySecretKey(secretKey: string): Promise<Marketplace | null> {
    void secretKey;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async findByPublishableKey(publishableKey: string): Promise<Marketplace | null> {
    void publishableKey;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
