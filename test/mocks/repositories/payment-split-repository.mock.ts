import { PaymentSplit, PaymentSplitStatus } from '@/domain/payment-split/payment-split';
import {
  PaymentSplitRefund,
  PaymentSplitRefundStatus,
} from '@/domain/payment-split/payment-split-refund';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports/payment-split.repository';
import { AllowedCurrencies, Currency, Gateway, GatewayType, Money } from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class PaymentSplitRepositoryMock extends BaseRepositoryMock<PaymentSplit> implements PaymentSplitRepositoryPort {
  constructor() {
    const gateway = new Gateway('gateway-id-0', GatewayType.STRIPE);
    const data = Array.from({ length: 3 }).map((_, i) => {
      const refunds = Array.from({ length: 6 }).map((a, j) => new PaymentSplitRefund({
        id: `payment-split-refund-id-${i}-${j}`,
        props: {
          refundId: `refund-id-${Math.floor(j/2)}`,
          amount: new Money((i*100)+(j*10)+1, new Currency(AllowedCurrencies.USD)),
          status: PaymentSplitRefundStatus.ISSUED,
          isManual: false,
        }
      }));
      const refundedAmount = refunds.reduce((acc, refund) =>
        acc.add(refund.getProps().amount),
        new Money(0, new Currency(AllowedCurrencies.USD))
      );
      return new PaymentSplit({
        id: `payment-split-id-${i}`,
        props: {
          accountId: `account-id-0`,
          paymentId: `payment-id-0`,
          status: PaymentSplitStatus.SETTLED,
          amount: refundedAmount.mul(2),
          refundedAmount,
          refunds,
          gateway,
        }
      });
    });
    super(data);
  }

  public async findByPaymentId(paymentId: string): Promise<PaymentSplit[]> {
    const paymentSplits = this.db.filter(item => item.getProps().paymentId === paymentId);
    return Promise.resolve(paymentSplits);
  }
}
