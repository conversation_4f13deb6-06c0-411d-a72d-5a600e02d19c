import { Account } from '@/domain/account/account';
import { GatewayAccount } from '@/domain/account/gateway-account';
import { AccountRepositoryPort } from '@/domain/account/ports';
import { Gateway, GatewayType } from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class AccountRepositoryMock extends BaseRepositoryMock<Account> implements AccountRepositoryPort {
  constructor() {
    const gateway = new Gateway('gateway-id-0', GatewayType.STRIPE);
    const data = Array.from({ length: 3 }).map((_, i) => new Account({
      id: `account-id-${i}`,
      props: {
        marketplaceId: `marketplace-id-${i}`,
        name: `account-${i}`,
        email: `test-${i}@mail.com`,
        gatewayAccounts: [
          new GatewayAccount({
            id: `gateway-account-id-${i}`,
              props: {
                idAtGateway: `gateway-account-id-${i}`,
                gateway,
              },
          })
        ],
        debt: undefined,
        balance: undefined,
      },
    }));
    super(data);
  }

  public async accountsExistInMarketplace(marketplaceId: string, accountIds: string[]): Promise<boolean> {
    void marketplaceId;
    void accountIds;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async findByMarketplaceAndAccountIds(marketplaceId: string, accountIds: string[]): Promise<Account[]> {
    void marketplaceId;
    void accountIds;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
