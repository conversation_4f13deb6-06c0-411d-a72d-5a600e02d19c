import { IRepository, Paginated, PaginatedQueryParams } from '@/core/ddd';

export class BaseRepositoryMock<T extends { getId(): string }> implements IRepository<T> {
  public db: T[];

  protected constructor(items: T[] = []) {
    this.db = [...items];
  }

  public async findById(id: string): Promise<T | null> {
    const item = this.db.find((el) => el.getId() === id);
    return Promise.resolve(item || null);
  }

  public async findAll(): Promise<T[]> {
    return Promise.resolve(this.db);
  }

  public async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<T>> {
    const data = this.db;
    return Promise.resolve({
      count: data.length,
      limit: params.limit,
      page: params.page,
      data: data.slice((params.page - 1) * params.limit, params.page * params.limit),
    });
  }

  public async create(entity: T): Promise<void> {
    void entity;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async update(entity: T): Promise<void> {
    void entity;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async delete(entity: T): Promise<boolean> {
    void entity;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async transaction<U>(handler: () => Promise<U>): Promise<U> {
    void handler;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
