import { GatewayPaymentMethod } from '@/domain/payment-method/gateway-payment-method';
import { PaymentMethod } from '@/domain/payment-method/payment-method';
import { PaymentMethodRepositoryPort } from '@/domain/payment-method/ports';
import { Gateway, GatewayType, PaymentMethodType } from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class PaymentMethodRepositoryMock
  extends BaseRepositoryMock<PaymentMethod>
  implements PaymentMethodRepositoryPort
{
  constructor() {
    const gateway = new Gateway('gateway-id-0', GatewayType.STRIPE);
    const data = Array.from({ length: 3 }).map(
      (_, i) =>
        new PaymentMethod({
          id: `payment-method-id-${i}`,
          props: {
            type: PaymentMethodType.CARD,
            cardExpiryMonth: 12,
            cardExpiryYear: 27,
            cardLast4: '4242',
            gatewayPaymentMethod: new GatewayPaymentMethod({
              id: `gateway-payment-method-id-${i}`,
              props: {
                idAtGateway: `id-at-gateway-${i}`,
                gateway,
                fingerprint: `fingerprint-${i}`,
              },
            }),
          },
        }),
    );
    super(data);
  }

  async findByFingerprint(fingerprint: string): Promise<PaymentMethod | null> {
    void fingerprint;
    throw new Error('Method not implemented.');
  }
}
