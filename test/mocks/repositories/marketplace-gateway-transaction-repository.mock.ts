import { MarketplaceGatewayTransaction } from '@/domain/marketplace-gateway-transaction/marketplace-gateway-transaction';
import { MarketplaceGatewayTransactionRepositoryPort } from '@/domain/marketplace-gateway-transaction/ports';
import { AllowedCurrencies, Money, Currency } from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class MarketplaceGatewayTransactionRepositoryMock extends BaseRepositoryMock<MarketplaceGatewayTransaction> implements MarketplaceGatewayTransactionRepositoryPort {
  constructor() {
    const data = Array.from({ length: 3 }).map((_, i) => new MarketplaceGatewayTransaction({
      id: `transaction-id-${i}`,
      props: {
        amount: new Money(1, new Currency(AllowedCurrencies.USD)),
        marketplaceGatewayId: `marketplace-gateway-id-${i}`,
      }
    }));
    super(data);
  }
}
