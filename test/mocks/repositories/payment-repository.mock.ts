import { Payment, PaymentStatus } from '@/domain/payment/payment';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { Refund, RefundStatus, RefundType } from '@/domain/payment/refund';
import {
  Money,
  Currency,
  AllowedCurrencies,
  Gateway,
  GatewayType,
  PaymentMethodType,
} from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class PaymentRepositoryMock
  extends BaseRepositoryMock<Payment>
  implements PaymentRepositoryPort
{
  constructor() {
    const gateway = new Gateway('gateway-id-0', GatewayType.STRIPE);
    const data = Array.from({ length: 3 }).map(
      (_, i) =>
        new Payment({
          id: `payment-id-${i}`,
          props: {
            marketplaceId: `marketplace-id-${i}`,
            customerId: `customer-id-${i}`,
            gateway,
            amount: new Money(10, new Currency(AllowedCurrencies.USD)),
            refundedAmount: new Money(0, new Currency(AllowedCurrencies.USD)),
            paymentIntentId: `payment-intent-id-${i}`,
            status: PaymentStatus.SETTLED,
            paymentMethodType: PaymentMethodType.CARD,
            paymentMethodId: `payment-method-id-${i}`,
            feeSettingsId: null,
            refunds: [
              new Refund({
                id: `refund-id-${i}`,
                props: {
                  amount: new Money(1, new Currency(AllowedCurrencies.USD)),
                  status: RefundStatus.ISSUED,
                  type: RefundType.PARTIAL,
                  isManual: false,
                  gatewayRefund: undefined,
                  metadata: undefined,
                  platformPaymentFeeRefunded: Money.from(0, AllowedCurrencies.USD),
                  providerPaymentFeeRefunded: Money.from(0, AllowedCurrencies.USD),
                  marketplacePaymentFeeRefunded: Money.from(0, AllowedCurrencies.USD),
                  marketplaceOrderFeeRefunded: Money.from(0, AllowedCurrencies.USD),
                  platformOrderFeeRefunded: Money.from(0, AllowedCurrencies.USD),
                },
              }),
            ],
            marketplacePaymentFeeFixed: new Money(1, new Currency(AllowedCurrencies.USD)),
            marketplacePaymentFeePercentage: 1,
            marketplacePaymentFee: new Money(1, new Currency(AllowedCurrencies.USD)),
            marketplacePaymentFeeRefunded: new Money(1, new Currency(AllowedCurrencies.USD)),
            marketplaceOrderFeeRefunded: new Money(1, new Currency(AllowedCurrencies.USD)),
            marketplaceOrderFee: new Money(1, new Currency(AllowedCurrencies.USD)),
            platformPaymentFeeFixed: new Money(2, new Currency(AllowedCurrencies.USD)),
            platformPaymentFeePercentage: 2,
            platformPaymentFee: new Money(2, new Currency(AllowedCurrencies.USD)),
            platformPaymentFeeRefunded: new Money(2, new Currency(AllowedCurrencies.USD)),
            platformOrderFee: new Money(2, new Currency(AllowedCurrencies.USD)),
            platformOrderFeeRefunded: new Money(2, new Currency(AllowedCurrencies.USD)),
            providerFeeFixed: new Money(3, new Currency(AllowedCurrencies.USD)),
            providerFeePercentage: 3,
            providerFee: new Money(3, new Currency(AllowedCurrencies.USD)),
            providerPaymentFeeRefunded: new Money(3, new Currency(AllowedCurrencies.USD)),
          },
        }),
    );
    super(data);
  }

  public async findByPaymentIntentId(paymentIntentId: string): Promise<Payment | null> {
    return Promise.resolve(
      this.db.find((payment) => payment.getProps().paymentIntentId === paymentIntentId) || null,
    );
  }

  public async findByGatewayRefundId(gatewayRefundId: string): Promise<Payment | null> {
    void gatewayRefundId;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async findByRefundId(refundId: string): Promise<Payment | null> {
    return Promise.resolve(
      this.db.find((payment) =>
        payment.getProps().refunds.some((refund) => refund.getId() === refundId),
      ) || null,
    );
  }

  public async findByPaymentSplitId(paymentSplitId: string): Promise<Payment | null> {
    void paymentSplitId;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
