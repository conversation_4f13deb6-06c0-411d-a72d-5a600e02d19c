import { Payout, PayoutStatus, PayoutType } from '@/domain/payout/payout';
import { PayoutReversal, PayoutReversalStatus } from '@/domain/payout/payout-reversal';
import { PayoutRepositoryPort } from '@/domain/payout/ports';
import { AllowedCurrencies, Currency, Gateway, GatewayType, Money } from '@/domain/shared';
import { BaseRepositoryMock } from '@/test/mocks/repositories/repository.mock';

export class PayoutRepositoryMock extends BaseRepositoryMock<Payout> implements PayoutRepositoryPort {
  constructor() {
    const gateway = new Gateway('gateway-id-0', GatewayType.STRIPE);
    const data = Array.from({ length: 3 }).map((_, i) => new Payout({
      id: `payout-id-${i}`,
      props: {
        amount: new Money(10, new Currency(AllowedCurrencies.USD)),
        reversedAmount: new Money(1, new Currency(AllowedCurrencies.USD)),
        gateway,
        marketplaceId: `marketplace-id-${i}`,
        marketplaceGatewayAccountId: `marketplace-gateway-account-id-${i}`,
        reversals: [
          new PayoutReversal({
            id: `reversal-id-${i}`,
            props: {
              amount: new Money(5, new Currency(AllowedCurrencies.USD)),
              status: PayoutReversalStatus.PENDING,
            }
          })
        ],
        status: PayoutStatus.PENDING,
        type: PayoutType.PAYMENT_FEE,
      },
    }));
    super(data);
  }

  public async findByIdAndMarketplaceId(payoutId: string, marketplaceId: string): Promise<Payout> {
    return Promise.resolve(this.db.find((payout) =>
      payout.getId() === payoutId && payout.getProps().marketplaceId === marketplaceId)
    );
  }

  public async findByPaymentId(paymentId: string): Promise<Payout[]> {
    void paymentId;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
