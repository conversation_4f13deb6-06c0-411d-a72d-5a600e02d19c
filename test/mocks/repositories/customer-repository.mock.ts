import { Customer } from '@/domain/customer/customer';
import { CustomerRepositoryPort } from '@/domain/customer/ports';

import { BaseRepositoryMock } from './repository.mock';

export class CustomerRepositoryMock extends BaseRepositoryMock<Customer> implements CustomerRepositoryPort {
  constructor() {
    const data = Array.from({ length: 3 }).map((_, i) => new Customer({
      id: `customer-id-${i}`,
      props: {
        marketplaceId: `marketplace-id-${i}`,
        name: `customer-${i}`,
        email: `test-${i}@mail.com`,
        gatewayCustomers: [],
      },
    }));
    super(data);
  }

  public async getByCustomerId(customerId: string): Promise<Customer> {
    void customerId;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
