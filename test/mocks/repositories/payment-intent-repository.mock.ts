import { PaymentIntent, PaymentIntentStatus } from '@/domain/payment-intent/payment-intent';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports/payment-intent.repository';
import { StripePaymentIntent } from '@/domain/payment-intent/stripe-payment-intent';
import {
  AllowedCurrencies,
  Currency,
  Gateway,
  GatewayType,
  Money,
  PaymentMethodType,
} from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class PaymentIntentRepositoryMock
  extends BaseRepositoryMock<PaymentIntent>
  implements PaymentIntentRepositoryPort
{
  constructor() {
    const gateway = new Gateway('gateway-id-0', GatewayType.STRIPE);
    const data = Array.from({ length: 3 }).map(
      (_, i) =>
        new PaymentIntent({
          id: `payment-intent-id-${i}`,
          props: {
            feeSettingsId: null,
            marketplaceId: `marketplace-id-${i}`,
            amount: new Money(100, new Currency(AllowedCurrencies.USD)),
            status: PaymentIntentStatus.PENDING,
            paymentMethodType: PaymentMethodType.CARD,
            customerId: `customer-id-${i}`,
            gateway,
            businessEntityId: `business-entity-id-${i}`,
            paymentSplits: [],
            statementDescriptor: '',
            gatewayPaymentIntent: new StripePaymentIntent({
              id: `stripe-payment-intent-id-${i}`,
              props: {
                gateway,
                idAtGateway: `account-id-${i}-${GatewayType.STRIPE}`,
                customerIdAtGateway: `customer-id-${i}-${GatewayType.STRIPE}`,
                stripeClientSecret: '',
              },
            }),
            metadata: undefined,
            marketplacePaymentFeeFixed: new Money(1, new Currency(AllowedCurrencies.USD)),
            marketplacePaymentFeePercentage: 5,
            marketplacePaymentFee: new Money(5, new Currency(AllowedCurrencies.USD)),
            platformPaymentFeeFixed: new Money(1, new Currency(AllowedCurrencies.USD)),
            platformPaymentFeePercentage: 5,
            platformPaymentFee: new Money(5, new Currency(AllowedCurrencies.USD)),
            providerFeeFixed: new Money(1, new Currency(AllowedCurrencies.USD)),
            providerFeePercentage: 1,
            providerFee: new Money(1, new Currency(AllowedCurrencies.USD)),
            marketplaceOrderFee: new Money(1, new Currency(AllowedCurrencies.USD)),
            platformOrderFee: new Money(1, new Currency(AllowedCurrencies.USD)),
          },
        }),
    );
    super(data);
  }

  async findByPaymentIdAndMarketplaceId(
    paymentId: string,
    marketplaceId: string,
  ): Promise<PaymentIntent> {
    void paymentId;
    void marketplaceId;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async findByIdAndMarketplaceId(id: string, marketplaceId: string): Promise<PaymentIntent> {
    void id;
    void marketplaceId;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async findPaymentIntentByGatewayPaymentIntentId(
    gatewayPaymentIntentId: string,
    gateway: Gateway,
  ): Promise<PaymentIntent> {
    void gatewayPaymentIntentId;
    void gateway;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
