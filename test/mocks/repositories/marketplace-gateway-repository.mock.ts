import { MarketplaceGateway } from '@/domain/marketplace-gateway/marketplace-gateway';
import { MarketplaceGatewayRepositoryPort, FindByMarketplaceAndGatewayIdsProps } from '@/domain/marketplace-gateway/ports';
import { AllowedCurrencies, Money, Currency, Gateway, GatewayType } from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class MarketplaceGatewayRepositoryMock extends BaseRepositoryMock<MarketplaceGateway> implements MarketplaceGatewayRepositoryPort {
  constructor() {
    const gateway = new Gateway('gateway-id-0', GatewayType.STRIPE);
    const data = Array.from({ length: 3 }).map((_, i) => new MarketplaceGateway({
      id: `marketplace-gateway-id-${i}`,
      props: {
        balance: new Money(0, new Currency(AllowedCurrencies.USD)),
        currency: AllowedCurrencies.USD,
        marketplaceId: `marketplace-id-${i}`,
        gatewayId: gateway.getGatewayId(),
        marketplacePaymentFeeFixed: new Money(1, new Currency(AllowedCurrencies.USD)),
        marketplacePaymentFeePercentage: 5,
        platformPaymentFeeFixed: new Money(1, new Currency(AllowedCurrencies.USD)),
        platformPaymentFeePercentage: 5,
      }
    }));
    super(data);
  }

  public async findByMarketplaceAndGatewayId({ marketplaceId, gatewayId }: FindByMarketplaceAndGatewayIdsProps): Promise<MarketplaceGateway> {
    const result = this.db.find(item => item.getProps().marketplaceId === marketplaceId && item.getProps().gatewayId === gatewayId);
    return Promise.resolve(result);
  }
}
