import { Gateway } from '@/domain/gateway/gateway';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { GatewayType } from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class GatewayRepositoryMock extends BaseRepositoryMock<Gateway> implements GatewayRepositoryPort {
  constructor() {
    const data = [GatewayType.STRIPE, GatewayType.BRAINTREE].map((type, i) => new Gateway({
      id: `gateway-id-${i}`,
      props: {
        name: `Gateway ${type}`,
        type,
        feePercentage: 0.03,
        feeFixed: undefined,
      },
    }));
    super(data);
  }

  public async findByGatewayType(type: GatewayType): Promise<Gateway | null> {
    const gateway = this.db.find(item => item.getProps().type === type);
    return Promise.resolve(gateway || null);
  }
}
