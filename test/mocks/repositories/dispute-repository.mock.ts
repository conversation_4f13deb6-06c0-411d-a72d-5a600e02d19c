import {
  Dispute,
  DisputeEvidence,
  DisputeEvidenceDetails,
  DisputeStatus,
} from '@/domain/dispute/dispute';
import { DisputeRepositoryPort } from '@/domain/dispute/ports';
import { StripeDispute } from '@/domain/dispute/stripe-dispute';
import { AllowedCurrencies, Currency, Gateway, GatewayType, Money } from '@/domain/shared';

import { BaseRepositoryMock } from './repository.mock';

export class DisputeRepositoryMock
  extends BaseRepositoryMock<Dispute>
  implements DisputeRepositoryPort
{
  constructor() {
    const gateway = new Gateway('gateway-id-0', GatewayType.STRIPE);
    const data = Array.from({ length: 3 }).map(
      (_, i) =>
        new Dispute({
          id: `dispute-id-${i}`,
          props: {
            amount: new Money(100, new Currency(AllowedCurrencies.USD)),
            currency: AllowedCurrencies.USD,
            status: DisputeStatus.WON,
            reason: 'test',
            evidence: {} as DisputeEvidence,
            evidenceDetails: {} as DisputeEvidenceDetails,
            gateway,
            gatewayId: gateway.getGatewayId(),
            paymentIntentId: `payment-intent-id-${i}`,
            paymentId: `payment-id-${i}`,
            gatewayDispute: new StripeDispute({
              id: `gateway-dispute-id-${i}`,
              props: {
                stripeDisputeId: `stripe-dispute-id-${i}`,
                stripePaymentIntentId: `stripe-payment-intent-id-${i}`,
                gateway,
              },
            }),
          },
        }),
    );
    super(data);
  }

  public async findByGatewayDisputeId(
    gatewayDisputeId: string,
    gatewayType: GatewayType,
  ): Promise<Dispute> {
    switch (gatewayType) {
      case GatewayType.STRIPE:
        return Promise.resolve(
          this.db.find((el) => {
            const props = el.getProps();
            return (
              props.gatewayDispute.getId() === gatewayDisputeId &&
              props.gateway.getType() === gatewayType
            );
          }),
        );
      default:
        throw new Error('Not supported gateway type');
    }
  }

  public async isGatewayDisputeExists(
    stripeDisputeId: string,
    gatewayType: GatewayType,
  ): Promise<boolean> {
    void stripeDisputeId;
    void gatewayType;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
