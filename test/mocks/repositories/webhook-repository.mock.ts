import { WebhookData } from '@/application/webhook/interfaces/webhook-event.interface';
import { Gateway, GatewayType } from '@/domain/shared';
import { WebhookRepositoryPort } from '@/domain/webhook/ports';
import { Webhook, WebhookObject, WebhookStatus, WebhookType } from '@/domain/webhook/webhook';

import { BaseRepositoryMock } from './repository.mock';

export class WebhookRepositoryMock extends BaseRepositoryMock<Webhook> implements WebhookRepositoryPort {
  constructor() {
    const gateway = new Gateway('gateway-id-0', GatewayType.STRIPE);
    const data = Array.from({ length: 3 }).map((_, i) => new Webhook({
      id: `webhook-id-${i}`,
      props: {
        marketplaceId: `marketplace-id-${i}`,
        type: WebhookType.DISPUTE_CREATED,
        gateway,
        object: WebhookObject.PAYMENT_INTENT,
        status: WebhookStatus.PENDING,
        failReason: '',
        fails: 0,
        lastAttemptAt: new Date(),
        nextRetryAt: new Date(),
        data: { id: `dispute-id-${i}` } as WebhookData,
        url: `https://webhook-url-${i}.com`,
      },
    }));
    super(data);
  }
}
