import { SubscriberOptions , SubscriberHandler } from '@sw-web/nestjs-core/rabbitmq';
import { Replies, Options } from 'amqplib';

export class RabbitMQClientMock {
  public isHealthy() {
    return true;
  }

  public async publish<T = unknown>(
    exchange: string,
    routingKey: string,
    message: T,
    options?: Options.Publish,
  ): Promise<Replies.Empty> {
    void exchange;
    void routingKey;
    void message;
    void options;
    return Promise.reject(new Error('Mock implementation not provided'));
  }

  public async createSubscriber<T>(
    handler: SubscriberHandler<T>,
    subscriberOptions: SubscriberOptions,
    originalHandlerName: string,
  ): Promise<void> {
    void handler;
    void subscriberOptions;
    void originalHandlerName;
    return Promise.reject(new Error('Mock implementation not provided'));
  }
}
