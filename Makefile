include Makefile-variables

# Variables
DOCKER_BASE_IMAGE="payment-hub-base"
DOCKER_NETWORK="payment-hub"
DOCKER_NETWORK_TEST="payment-hub-test"

# Helper variables for Docker run
DOCKER_RUN = docker run --rm --env-file .env --network ${DOCKER_NETWORK} --user $(id -u):$(id -g)
DOCKER_RUN_TEST = docker run --rm --env-file .env --network ${DOCKER_NETWORK_TEST} --user $(id -u):$(id -g)

all: | ${NODE_ENV}

############# envs
development: | docker-build-base docker-up-infra db-migrate db-seed rabbitmq-migrate blue-green-deploy  
production: | docker-build-base docker-up-network db-migrate rabbitmq-migrate blue-green-deploy
local: | docker-build-base docker-up-infra db-migrate db-seed rabbitmq-migrate docker-up 

############## Blue-Green Deployment
blue-green-deploy:
	./deploy/scripts/deploy-blue-green.sh

############# Docker Targets
docker-up-network:
	docker compose -f ${DOCKER_COMPOSE_FILE} up --no-start

docker-up:
	docker compose -f ${DOCKER_COMPOSE_FILE} up -d --force-recreate

docker-down:
	docker compose -f ${DOCKER_COMPOSE_FILE} down

docker-build-base:
	docker build --target base -t ${DOCKER_BASE_IMAGE} -f ./Dockerfile .

docker-up-infra: 
	./deploy/scripts/docker-up-infra.sh ${DOCKER_COMPOSE_FILE}

############# Database Targets
db-migrate:
	${DOCKER_RUN} ${DOCKER_BASE_IMAGE} yarn migrate:prod

db-migrate-test:
	${DOCKER_RUN_TEST} ${DOCKER_BASE_IMAGE} yarn migrate:test

db-seed:
	${DOCKER_RUN} ${DOCKER_BASE_IMAGE} yarn prisma db seed

############# RABBITMQ Targets
docker-up-rabbitmq:
	docker compose -f ${DOCKER_COMPOSE_FILE} up -d --force-recreate rabbitmq

rabbitmq-migrate:
	${DOCKER_RUN} ${DOCKER_BASE_IMAGE} yarn rabbitmq:migrate

rabbitmq-migrate-test:
	${DOCKER_RUN_TEST} ${DOCKER_BASE_IMAGE} yarn rabbitmq:migrate


# WITHOUT THIS, TEST DOES NOT RUN
.PHONY: test

############# Test Targets
test: | docker-build-base docker-up-infra db-migrate-test
	${DOCKER_RUN_TEST} ${DOCKER_BASE_IMAGE} yarn test
	make docker-down

test-e2e: | docker-build-base docker-up-infra db-migrate-test rabbitmq-migrate-test  
	${DOCKER_RUN_TEST} ${DOCKER_BASE_IMAGE} yarn test:e2e
	make docker-down