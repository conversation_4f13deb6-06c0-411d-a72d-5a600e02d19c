version: '3.8'

services:
  traefik:
    image: traefik:v2.10.5
    command:
      - '--api.insecure=true'
      - '--providers.docker=true'
      - '--providers.docker.exposedbydefault=false'
      - '--entrypoints.payment-hub.address=:80'
      - '--ping=true'
    ports:
      - ${SERVER_PORT}:80
      - 8080:8080
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - payment-hub
    healthcheck:
      test: ['CMD-SHELL', 'traefik healthcheck --ping']
      interval: 5s
      timeout: 10s
      retries: 5
      start_period: 60s
    env_file:
      - .env

  payment-hub-base: &payment-hub-base
    image: payment-hub/app:${TAG:-latest}
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      traefik:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    restart: unless-stopped
    env_file:
      - .env
    networks:
      - payment-hub

  payment-hub-blue:
    <<: *payment-hub-base
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.payment-hub-blue.rule=PathPrefix(`/`)'
      - 'traefik.http.routers.payment-hub-blue.entrypoints=payment-hub'
      - 'traefik.http.services.payment-hub-blue.loadbalancer.server.port=${SERVER_PORT}'
      - 'traefik.http.services.payment-hub-blue.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.payment-hub-blue.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.payment-hub-blue.loadbalancer.healthcheck.timeout=5s'

  payment-hub-green:
    <<: *payment-hub-base
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.payment-hub-green.rule=PathPrefix(`/`)'
      - 'traefik.http.routers.payment-hub-green.entrypoints=payment-hub'
      - 'traefik.http.services.payment-hub-green.loadbalancer.server.port=${SERVER_PORT}'
      - 'traefik.http.services.payment-hub-green.loadbalancer.healthcheck.path=/health'
      - 'traefik.http.services.payment-hub-green.loadbalancer.healthcheck.interval=5s'
      - 'traefik.http.services.payment-hub-green.loadbalancer.healthcheck.timeout=5s'

  rabbitmq:
    labels:
      - 'infra=true'
    image: rabbitmq:management
    ports:
      - '${RABBITMQ_MANAGEMENT_PORT:-15672}:15672'
      - '${RABBITMQ_PORT:-5672}:5672'
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-payment-hub}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-payment-hub}
      RABBITMQ_DEFAULT_VHOST: payment-hub
    healthcheck:
      test: ['CMD', 'rabbitmqctl', 'status']
      interval: 10s
      timeout: 10s
      retries: 5
    networks:
      - payment-hub

volumes:
  postgres-data:

networks:
  payment-hub:
    name: payment-hub
    driver: bridge
