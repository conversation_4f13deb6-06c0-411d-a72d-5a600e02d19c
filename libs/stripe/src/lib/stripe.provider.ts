import { Provider } from '@nestjs/common';
import <PERSON><PERSON> from 'stripe';

import { STRIPE_MODULE_OPTIONS } from './stripe.di-tokens';
import { StripeModuleOptions } from './types';

export const stripeClientProvider: Provider = {
  provide: Stripe,
  useFactory: ({ apiKey, apiVersion }: StripeModuleOptions) => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore - <PERSON><PERSON> suggests to ignore type error when passing custom version
    const client = new Stripe(apiKey, { apiVersion });

    return client;
  },
  inject: [STRIPE_MODULE_OPTIONS],
};
