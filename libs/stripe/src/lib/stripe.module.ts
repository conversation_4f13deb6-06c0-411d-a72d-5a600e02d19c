import { DynamicModule, Module, Provider } from '@nestjs/common';

import { STRIPE_MODULE_OPTIONS } from './stripe.di-tokens';
import { stripeClientProvider } from './stripe.provider';
import { StripeModuleOptions } from './types';

@Module({})
export class StripeModule {
  static forRoot(moduleOptions: StripeModuleOptions): DynamicModule {
    const providers: Provider[] = [
      { provide: STRIPE_MODULE_OPTIONS, useValue: moduleOptions },
      stripeClientProvider,
    ];

    return {
      global: moduleOptions.global,
      module: StripeModule,
      providers: [...providers],
      exports: [stripeClientProvider],
    };
  }
}
