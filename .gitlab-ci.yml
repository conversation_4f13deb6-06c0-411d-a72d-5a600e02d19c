stages:
  - audit
  - test
  - deploy
  - sales-hub-integration-test

semgrep:
  stage: audit
  when: manual
  script:
    - docker run --rm -v "${PWD}:/src" docker.io/returntocorp/semgrep semgrep -c p/security-audit

test:dev:
  stage: test
  only:
    - development
  script:
    - >
      ansible-playbook deploy/test.dev.yml -l marc-do-dev-re-payment-hub
      -e "ENV_FILE=$ENV_FILE"
  environment:
    name: test

test:prod:
  stage: test
  only:
    - master
  script:
    - >
      ansible-playbook deploy/test.prod.yml -l marc-do-dev-re-payment-hub
      -e "ENV_FILE=$ENV_FILE"
  environment:
    name: test

trigger_integration_tests:dev:
  stage: sales-hub-integration-test
  only:
    - development
  trigger:
    project: sw-web/sales-hub-integration-tests
    branch: development
    strategy: depend

deploy:dev:
  stage: deploy
  only:
    - development
  script:
    - >
      ansible-playbook deploy/deploy.dev.yml -l marc-do-dev-re-payment-hub
      -e "ENV_FILE=$ENV_FILE"
  dependencies:
    - test:dev
  environment:
    name: development

deploy:prod:
  stage: deploy
  only:
    - master
  script:
    - >
      ansible-playbook deploy/deploy.prod.yml -l marc-do-dev-re-payment-hub
      -e "ENV_FILE=$ENV_FILE"
  dependencies:
    - test:prod
  environment:
    name: production
