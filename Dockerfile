# ---- Prisma Stage ----
FROM node:22-alpine AS base

RUN apk add --no-cache openssl

WORKDIR /usr/src/app

# Copy the package.json, yarn.lock, and prisma schema files
COPY package.json yarn.lock .npmrc ./ 
COPY src/database/prisma ./src/database/prisma/

# Install app dependencies including devDependencies
# Prisma will auto-generate types during this step in a post-install hook
RUN yarn install --frozen-lockfile

# Copy all source files to allow using it for running tests
COPY . .

# ---- Build Stage ----
FROM base AS build

WORKDIR /usr/src/app

# Use already installed dependencies and Prisma client from the prisma stag
# Build the application
RUN yarn build

# ---- Release Stage ----
FROM node:22-alpine

RUN apk add --no-cache openssl

WORKDIR /usr/src/app

# Copy package.json, yarn.lock and the built code from the previous stage
COPY package.json yarn.lock .npmrc ./
COPY --from=build /usr/src/app/dist ./dist
COPY --from=build /usr/src/app/node_modules/.prisma ./node_modules/.prisma

# Install only production dependencies
RUN yarn install --production --frozen-lockfile --ignore-scripts --prefer-offline && yarn cache clean

# Command to run the application
CMD ["yarn", "start:prod"]
