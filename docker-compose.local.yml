version: '3.8'

services:
  postgres:
    labels:
      - 'infra=true'
    image: postgres:16
    environment:
      POSTGRES_USER: ${DB_USER:-payment_hub}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-change-in-production}
      POSTGRES_DB: ${DB_NAME:-payment_hub}
    ports:
      - '${DB_PORT:-5432}:5432'
    volumes:
      - postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U payment_hub']
      interval: 5s
      timeout: 10s
      retries: 5
    networks:
      - payment-hub

  rabbitmq:
    labels:
      - 'infra=true'
    image: rabbitmq:management
    ports:
      - '${RABBITMQ_MANAGEMENT_PORT:-15672}:15672'
      - '${RABBITMQ_PORT:-5672}:5672'
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-payment-hub}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-payment-hub}
      RABBITMQ_DEFAULT_VHOST: payment-hub
    healthcheck:
      test: ['CMD', 'rabbitmqctl', 'status']
      interval: 10s
      timeout: 10s
      retries: 5
    networks:
      - payment-hub

  payment-hub:
    image: payment-hub/app
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      rabbitmq:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - ${HOST_PORT}:${SERVER_PORT}
    env_file:
      - .env
    networks:
      - payment-hub

volumes:
  postgres-data:

networks:
  payment-hub:
    name: payment-hub
    driver: bridge
