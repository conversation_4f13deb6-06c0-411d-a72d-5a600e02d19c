{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "builder": "webpack", "webpack": true}, "projects": {"logger": {"type": "library", "root": "libs/logger", "entryFile": "index", "sourceRoot": "libs/logger/src", "compilerOptions": {"tsConfigPath": "libs/logger/tsconfig.lib.json"}}, "event-bus": {"type": "library", "root": "libs/event-bus", "entryFile": "index", "sourceRoot": "libs/event-bus/src", "compilerOptions": {"tsConfigPath": "libs/event-bus/tsconfig.lib.json"}}, "stripe": {"type": "library", "root": "libs/stripe", "entryFile": "index", "sourceRoot": "libs/stripe/src", "compilerOptions": {"tsConfigPath": "libs/stripe/tsconfig.lib.json"}}, "rabbitmq": {"type": "library", "root": "libs/rabbitmq", "entryFile": "index", "sourceRoot": "libs/rabbitmq/src", "compilerOptions": {"tsConfigPath": "libs/rabbitmq/tsconfig.lib.json"}}}}