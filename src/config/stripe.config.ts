import { get } from 'env-var';

export class StripeConfig {
  public static readonly publicKey: string = get('STRIPE_PUBLIC_KEY').required().asString();

  public static readonly secretKey: string = get('STRIPE_SECRET_KEY').required().asString();

  public static readonly apiVersion: string = get('STRIPE_API_VERSION').required().asString();

  public static readonly webhookSecret: string = get('STRIPE_WEBHOOK_SECRET').required().asString();
}
