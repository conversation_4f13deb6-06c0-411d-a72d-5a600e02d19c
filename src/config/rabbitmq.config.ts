import { get } from 'env-var';

import { AppEnv, ServerConfig } from './server.config';

const rabbitmqUrlEnvName =
  ServerConfig.nodeEnv === AppEnv.TEST ? 'TEST_RABBIT_MQ_URL' : 'RABBIT_MQ_URL';

const rabbitMqAdminUrlEnvName =
  ServerConfig.nodeEnv === AppEnv.TEST ? 'TEST_RABBIT_MQ_ADMIN_URL' : 'RABBIT_MQ_ADMIN_URL';

const rabbitMqAdminUsername =
  ServerConfig.nodeEnv === AppEnv.TEST
    ? 'TEST_RABBIT_MQ_ADMIN_USERNAME'
    : 'RABBIT_MQ_ADMIN_USERNAME';

const rabbitMqAdminPassword =
  ServerConfig.nodeEnv === AppEnv.TEST
    ? 'TEST_RABBIT_MQ_ADMIN_PASSWORD'
    : 'RABBIT_MQ_ADMIN_PASSWORD';

export class RabbitMQConfig {
  public static readonly url: string[] = get(rabbitmqUrlEnvName)
    .required()
    .asString()
    .split(',')
    .map((url) => url.trim());

  public static readonly adminUrl: string = get(rabbitMqAdminUrlEnvName).required().asString();

  public static readonly adminUsername: string = get(rabbitMqAdminUsername).required().asString();

  public static readonly adminPassword: string = get(rabbitMqAdminPassword).required().asString();
}
