import { HttpException, Module } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { SentryInterceptor, SentryModule } from '@ntegral/nestjs-sentry';
import { EventBusModule } from '@sw-web/nestjs-core/event-bus';
import { LoggerModule } from '@sw-web/nestjs-core/logger';
import { RequestContextModule } from 'nestjs-request-context';

import { StripeModule } from '@/libs/stripe';

import { SentryConfig } from './config/sentry.config';
import { ServerConfig } from './config/server.config';
import { StripeConfig } from './config/stripe.config';
import { InfrastructureModule } from './infrastructure/infrastructure.module';

@Module({
  imports: [
    RequestContextModule,
    EventBusModule.forRoot(),
    LoggerModule.forRoot({
      serviceName: 'server',
    }),
    SentryModule.forRoot({
      dsn: SentryConfig.dsn,
      environment: ServerConfig.nodeEnv,
      logLevels: ['debug'],
    }),
    StripeModule.forRoot({
      apiKey: StripeConfig.secretKey,
      apiVersion: StripeConfig.apiVersion,
      global: true,
    }),
    InfrastructureModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useFactory: () =>
        new SentryInterceptor({
          filters: [
            {
              type: HttpException,
              filter: (exception: HttpException) => exception.getStatus() > 500,
            },
          ],
        }),
    },
  ],
})
export class AppModule {}
