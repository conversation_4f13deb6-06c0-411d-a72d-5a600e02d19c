/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { Customer } from '@/domain/customer/customer';
import { GatewayCustomer } from '@/domain/customer/gateway-customer';
import { CustomerRepositoryPort } from '@/domain/customer/ports';
import { Gateway } from '@/domain/shared';

export const CustomerValidator = Prisma.validator<Prisma.CustomerDefaultArgs>()({
  include: {
    gatewayCustomers: {
      include: {
        gateway: {
          select: {
            id: true,
            type: true,
          },
        },
      },
    },
  },
});

export type CustomerModel = Prisma.CustomerGetPayload<typeof CustomerValidator>;

@Injectable()
export class CustomerRepositoryAdapter
  extends SqlRepositoryBase<Customer>
  implements CustomerRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  protected async createImpl(customer: Customer): Promise<void> {
    const { id, name, email, marketplaceId, gatewayCustomers, createdAt, updatedAt } =
      customer.getProps();

    await this.db.customer.create({
      data: {
        id,
        name,
        email,
        marketplaceId,
        createdAt,
        updatedAt,
      },
    });

    const promises = gatewayCustomers.map(async (gatewayCustomer) => {
      const props = gatewayCustomer.getProps();

      await this.db.gatewayCustomer.create({
        data: {
          id: props.id,
          gatewayId: props.gateway.getGatewayId(),
          idAtGateway: props.idAtGateway,
          customerId: id,
          createdAt,
          updatedAt,
        },
      });
    });

    await Promise.all(promises);
  }

  protected async updateImpl(customer: Customer): Promise<void> {
    const { id, name, email, marketplaceId, gatewayCustomers, createdAt, updatedAt } =
      customer.getProps();

    await this.db.customer.update({
      data: {
        id,
        name,
        email,
        marketplaceId,
        createdAt,
        updatedAt,
      },
      where: {
        id,
      },
    });

    const promises = gatewayCustomers.map(async (gatewayCustomer) => {
      const props = gatewayCustomer.getProps();

      const data = {
        id: props.id,
        gatewayId: props.gateway.getGatewayId(),
        idAtGateway: props.idAtGateway,
        customerId: id,
        createdAt,
        updatedAt,
      };

      await this.db.gatewayCustomer.upsert({
        create: data,
        update: data,
        where: {
          id: props.id,
        },
      });
    });

    await Promise.all(promises);
  }

  async getByCustomerId(customerId: string): Promise<Customer | null> {
    const customer = await this.db.customer.findFirstOrThrow({
      where: {
        id: customerId,
      },
      ...CustomerValidator,
    });

    return this.mapToEntity(customer);
  }

  async findById(customerId: string): Promise<Customer | null> {
    const customer = await this.db.customer.findFirst({
      where: {
        id: customerId,
      },
      ...CustomerValidator,
    });

    return customer && this.mapToEntity(customer);
  }

  protected async deleteImpl(entity: Customer): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findAll(): Promise<Customer[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<Customer>> {
    throw new Error('Method not implemented.');
  }

  private mapToEntity(customer: CustomerModel): Customer {
    return new Customer({
      id: customer.id,
      props: {
        marketplaceId: customer.marketplaceId,
        email: customer.email,
        name: customer.name,
        gatewayCustomers: customer.gatewayCustomers.map(
          ({ gateway, ...gatewayCustomer }) =>
            new GatewayCustomer({
              id: gatewayCustomer.id,
              props: {
                idAtGateway: gatewayCustomer.idAtGateway,
                gateway: new Gateway(gateway.id, gateway.type),
              },
            }),
        ),
      },
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
    });
  }
}
