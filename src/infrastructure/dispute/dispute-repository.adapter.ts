/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';
import Stripe from 'stripe';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { Dispute, DisputeStatus } from '@/domain/dispute/dispute';
import { DisputeRepositoryPort } from '@/domain/dispute/ports';
import { StripeDispute } from '@/domain/dispute/stripe-dispute';
import {
  AllowedCurrencies,
  Gateway,
  GatewayType,
  Money,
  NotSupportedGatewayTypeError,
} from '@/domain/shared';

const DisputeValidator = Prisma.validator<Prisma.DisputeDefaultArgs>()({
  include: {
    gateway: true,
  },
});

const StripeDisputeValidator = Prisma.validator<Prisma.StripeDisputeDefaultArgs>()({
  include: {
    dispute: {
      include: {
        gateway: true,
      },
    },
  },
});

export type DisputeModel = Prisma.DisputeGetPayload<typeof DisputeValidator>;
export type StripeDisputeModel = Prisma.StripeDisputeGetPayload<typeof StripeDisputeValidator>;
export type Evidence = Stripe.Dispute.Evidence;
export type EvidenceDetails = Stripe.Dispute.EvidenceDetails;
export type GatewayDispute = StripeDispute;

@Injectable()
export class DisputeRepositoryAdapter
  extends SqlRepositoryBase<Dispute>
  implements DisputeRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  protected async createImpl(entity: Dispute): Promise<void> {
    const { id, gatewayDispute } = entity.getProps();

    await this.db.dispute.create({
      data: this.mapToDisputeDbRow(entity),
    });
    // update gateway dispute with dispute id
    await this.saveGatewayDispute(id, gatewayDispute);
  }

  private async saveGatewayDispute(disputeId: string, gatewayDispute: GatewayDispute) {
    const { gateway } = gatewayDispute.getProps();

    switch (gateway.getType()) {
      case GatewayType.STRIPE:
        return this.saveStripeDispute(disputeId, gatewayDispute);
      default:
        throw new NotSupportedGatewayTypeError();
    }
  }

  private async saveStripeDispute(disputeId: string, stripeDispute: StripeDispute) {
    const { id, stripeDisputeId, stripePaymentIntentId } = stripeDispute.getProps();

    const data = {
      id,
      disputeId,
      stripeDisputeId,
      stripePaymentIntentId,
    };

    await this.db.stripeDispute.upsert({
      create: data,
      update: data,
      where: {
        id,
      },
    });
  }

  protected async updateImpl(entity: Dispute): Promise<void> {
    const dispute = this.mapToDisputeDbRow(entity);
    await this.db.dispute.update({
      where: {
        id: dispute.id,
      },
      data: dispute,
    });
  }

  protected async deleteImpl(entity: Dispute): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<Dispute> {
    const dispute = await this.db.dispute.findFirst({
      ...DisputeValidator,
      where: {
        id,
      },
      include: {
        gateway: true,
      },
    });

    return this.mapDispute(dispute);
  }

  async findAll(): Promise<Dispute[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<Dispute>> {
    throw new Error('Method not implemented.');
  }

  async isGatewayDisputeExists(disputeId: string, gatewayType: GatewayType): Promise<boolean> {
    switch (gatewayType) {
      case GatewayType.STRIPE:
        return this.isStripeDisputeExists(disputeId);
      default:
        throw new NotSupportedGatewayTypeError();
    }
  }

  async findGatewayDisputeByDisputeId(
    disputeId: string,
    gatewayType: GatewayType,
  ): Promise<GatewayDispute> {
    switch (gatewayType) {
      case GatewayType.STRIPE:
        return this.findStripeDisputeByDisputeId(disputeId);
      default:
        throw new NotSupportedGatewayTypeError();
    }
  }

  async findStripeDisputeByDisputeId(disputeId: string): Promise<StripeDispute> {
    const stripeDisputeModel = await this.db.stripeDispute.findFirst({
      ...StripeDisputeValidator,
      where: {
        disputeId,
      },
    });

    return new StripeDispute({
      id: stripeDisputeModel.id,
      props: {
        stripeDisputeId: stripeDisputeModel.stripeDisputeId,
        stripePaymentIntentId: stripeDisputeModel.stripePaymentIntentId,
        gateway: new Gateway(
          stripeDisputeModel.dispute.gatewayId,
          stripeDisputeModel.dispute.gateway.type,
        ),
      },
    });
  }

  async findByGatewayDisputeId(disputeId: string, gatewayType: GatewayType): Promise<Dispute> {
    switch (gatewayType) {
      case GatewayType.STRIPE:
        return this.findDisputeByStripeDisputeId(disputeId);
      default:
        throw new NotSupportedGatewayTypeError();
    }
  }

  private async isStripeDisputeExists(disputeId: string): Promise<boolean> {
    const dispute = await this.db.stripeDispute.count({
      where: {
        stripeDisputeId: disputeId,
      },
    });

    return !!dispute;
  }

  private async findDisputeByStripeDisputeId(stripeDisputeId: string): Promise<Dispute> {
    const gatewayDisputeModel = await this.db.stripeDispute.findFirst({
      ...StripeDisputeValidator,
      where: {
        stripeDisputeId,
      },
      include: {
        dispute: {
          include: {
            gateway: true,
          },
        },
      },
    });

    return this.mapDisputeFromStripeDispute(gatewayDisputeModel);
  }

  private mapDisputeFromStripeDispute(gatewayDispute: StripeDisputeModel): Dispute {
    if (!gatewayDispute) {
      return null;
    }

    return new Dispute({
      id: gatewayDispute.dispute.id,
      props: {
        gatewayId: gatewayDispute.dispute.gatewayId,
        gateway: new Gateway(gatewayDispute.dispute.gatewayId, gatewayDispute.dispute.gateway.type),
        amount: Money.from(gatewayDispute.dispute.amount, gatewayDispute.dispute.currency),
        currency: gatewayDispute.dispute.currency as AllowedCurrencies,
        status: gatewayDispute.dispute.status as DisputeStatus,
        reason: gatewayDispute.dispute.reason,
        evidence: gatewayDispute.dispute.evidence as unknown as Evidence,
        evidenceDetails: gatewayDispute.dispute.evidenceDetails as unknown as EvidenceDetails,
        paymentIntentId: gatewayDispute.dispute.paymentIntentId,
        paymentId: gatewayDispute.dispute.paymentId,
        gatewayDispute: new StripeDispute({
          id: gatewayDispute.id,
          props: {
            stripeDisputeId: gatewayDispute.stripeDisputeId,
            stripePaymentIntentId: gatewayDispute.stripePaymentIntentId,
            gateway: new Gateway(
              gatewayDispute.dispute.gatewayId,
              gatewayDispute.dispute.gateway.type,
            ),
          },
        }),
      },
    });
  }

  private async mapDispute(model: DisputeModel): Promise<Dispute> {
    if (!model) {
      return null;
    }

    const gatewayDispute = await this.findGatewayDisputeByDisputeId(
      model.id,
      model.gateway.type as GatewayType,
    );

    return new Dispute({
      id: model.id,
      props: {
        gatewayId: model.gatewayId,
        gateway: new Gateway(model.gatewayId, model.gateway.type),
        amount: Money.from(model.amount, model.currency),
        currency: model.currency as AllowedCurrencies,
        status: model.status as DisputeStatus,
        reason: model.reason,
        evidence: model.evidence as unknown as Evidence,
        evidenceDetails: model.evidenceDetails as unknown as EvidenceDetails,
        paymentIntentId: model.paymentIntentId,
        gatewayDispute,
        paymentId: model.paymentId,
      },
    });
  }

  private mapToDisputeDbRow(dispute: Dispute): Prisma.DisputeUncheckedCreateInput {
    const props = dispute.getProps();

    return {
      id: dispute.getId(),
      paymentIntentId: props.paymentIntentId,
      paymentId: props.paymentId,
      amount: props.amount.toNumber(),
      currency: props.currency,
      status: props.status,
      reason: props.reason,
      gatewayId: props.gatewayId,
      evidence: props.evidence as unknown as Prisma.InputJsonValue,
      evidenceDetails: props.evidenceDetails as unknown as Prisma.InputJsonValue,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
