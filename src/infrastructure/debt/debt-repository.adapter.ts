/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { Debt, DebtSource, DebtStatus, DebtType } from '@/domain/debt/debt';
import { DebtRepositoryPort } from '@/domain/debt/ports';
import { Money } from '@/domain/shared';

const DebtValidator = Prisma.validator<Prisma.DebtDefaultArgs>()({});

export type DebtModel = Prisma.DebtGetPayload<typeof DebtValidator>;

@Injectable()
export class DebtRepositoryAdapter extends SqlRepositoryBase<Debt> implements DebtRepositoryPort {
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  protected async createImpl(debt: Debt): Promise<void> {
    await this.db.debt.create({
      data: this.mapToDbRow(debt),
    });
  }

  protected async updateImpl(entity: Debt): Promise<void> {
    throw new Error('Method not implemented.');
  }

  protected async deleteImpl(entity: Debt): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<Debt> {
    const debt = await this.db.debt.findFirst({
      ...DebtValidator,
      where: {
        id,
      },
    });

    if (!debt) {
      return null;
    }

    return this.mapToEntity(debt);
  }

  async findAll(): Promise<Debt[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<Debt>> {
    throw new Error('Method not implemented.');
  }

  private mapToEntity(debt: DebtModel): Debt {
    return new Debt({
      id: debt.id,
      props: {
        amount: Money.from(debt.amount, debt.currency),
        paidAmount: Money.from(debt.paidAmount, debt.currency),
        type: debt.type as DebtType,
        status: debt.status as DebtStatus,
        source: debt.source as DebtSource,
        sourceId: debt.sourceId,
        accountId: debt.accountId,
      },
      createdAt: debt.createdAt,
      updatedAt: debt.updatedAt,
    });
  }

  private mapToDbRow(debt: Debt): Prisma.DebtUncheckedCreateInput {
    const props = debt.getProps();

    return {
      id: props.id,
      source: props.source,
      sourceId: props.sourceId,
      status: props.status,
      type: props.type,
      amount: props.amount.getProps().amount,
      paidAmount: props.paidAmount.getProps().amount,
      currency: props.amount.getProps().currency.getValue(),
      accountId: props.accountId,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
