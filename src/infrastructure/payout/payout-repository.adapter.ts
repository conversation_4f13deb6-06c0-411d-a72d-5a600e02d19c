/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma, PayoutReversal as DbPayoutReversal } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { Payout, PayoutStatus, PayoutType } from '@/domain/payout/payout';
import { PayoutReversal, PayoutReversalStatus } from '@/domain/payout/payout-reversal';
import { PayoutRepositoryPort } from '@/domain/payout/ports';
import { Gateway, Money } from '@/domain/shared';

const PayoutValidator = Prisma.validator<Prisma.PayoutDefaultArgs>()({
  include: {
    gateway: true,
    payoutReversals: true,
  },
});

export type PayoutModel = Prisma.PayoutGetPayload<typeof PayoutValidator>;

@Injectable()
export class PayoutRepositoryAdapter
  extends SqlRepositoryBase<Payout>
  implements PayoutRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  async findByPaymentId(paymentId: string): Promise<Payout[]> {
    const payouts = await this.db.payout.findMany({
      ...PayoutValidator,
      where: {
        paymentId,
      },
    });

    return payouts.map((payout) => this.mapToEntity(payout));
  }

  async findByIdAndMarketplaceId(payoutId: string, marketplaceId: string): Promise<Payout> {
    const payout = await this.db.payout.findFirst({
      ...PayoutValidator,
      where: {
        id: payoutId,
        marketplaceId,
      },
    });

    if (!payout) {
      return null;
    }

    return this.mapToEntity(payout);
  }

  protected async createImpl(payout: Payout): Promise<void> {
    await this.db.payout.create({
      data: this.mapToDbRow(payout),
    });

    await this.db.payoutReversal.createMany({
      data: payout.getProps().reversals.map((x) => this.mapReversalToDbRow(payout.getId(), x)),
    });
  }

  protected async updateImpl(entity: Payout): Promise<void> {
    await this.db.payout.update({
      data: this.mapToDbRow(entity),
      where: {
        id: entity.getId(),
      },
    });

    const updatePromises = entity.getProps().reversals.map(async (reversal) => {
      const createData = this.mapReversalToDbRow(entity.getId(), reversal);

      await this.db.payoutReversal.upsert({
        create: createData,
        update: createData,
        where: {
          id: reversal.getId(),
        },
      });
    });

    await Promise.all(updatePromises);
  }

  protected async deleteImpl(entity: Payout): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<Payout> {
    const payout = await this.db.payout.findFirst({
      ...PayoutValidator,
      where: {
        id,
      },
    });

    if (!payout) {
      return null;
    }

    return this.mapToEntity(payout);
  }

  async findAll(): Promise<Payout[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<Payout>> {
    throw new Error('Method not implemented.');
  }

  private mapToEntity(payout: PayoutModel): Payout {
    return new Payout({
      id: payout.id,
      props: {
        marketplaceGatewayAccountId: payout.marketplaceGatewayAccountId,
        gateway: new Gateway(payout.gateway.id, payout.gateway.type),
        amount: Money.from(payout.amount, payout.currency),
        reversals: payout.payoutReversals.map((x) => this.mapReversalToEntity(x)),
        reversedAmount: Money.from(payout.reversedAmount, payout.currency),
        status: payout.status as PayoutStatus,
        paymentId: payout.paymentId,
        type: payout.type as PayoutType,
        marketplaceId: payout.marketplaceId,
      },
      createdAt: payout.createdAt,
      updatedAt: payout.updatedAt,
    });
  }

  private mapReversalToEntity(reversal: DbPayoutReversal): PayoutReversal {
    return new PayoutReversal({
      id: reversal.id,
      props: {
        amount: Money.from(reversal.amount, reversal.currency),
        status: reversal.status as PayoutReversalStatus,
        refundId: reversal.refundId,
      },
      createdAt: reversal.createdAt,
      updatedAt: reversal.updatedAt,
    });
  }

  private mapReversalToDbRow(
    payoutId: string,
    reversal: PayoutReversal,
  ): Prisma.PayoutReversalUncheckedCreateInput {
    const props = reversal.getProps();

    return {
      id: props.id,
      payoutId,
      amount: props.amount.getProps().amount,
      currency: props.amount.getProps().currency.getValue(),
      status: props.status,
      refundId: props.refundId,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }

  private mapToDbRow(payout: Payout): Prisma.PayoutUncheckedCreateInput {
    const props = payout.getProps();

    return {
      id: props.id,
      marketplaceGatewayAccountId: props.marketplaceGatewayAccountId,
      gatewayId: props.gateway.getGatewayId(),
      amount: props.amount.getProps().amount,
      currency: props.amount.getProps().currency.getValue(),
      status: props.status,
      reversedAmount: props.reversedAmount.toNumber(),
      marketplaceId: payout.getProps().marketplaceId,
      paymentId: props.paymentId,
      type: props.type,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
