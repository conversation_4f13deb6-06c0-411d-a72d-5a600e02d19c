/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { FeeSettings } from '@/domain/fee-settings/fee-settings';
import { FeeSettingsRepositoryPort } from '@/domain/fee-settings/ports';
import { Gateway, Money } from '@/domain/shared';

export const FeeSettingsValidator = Prisma.validator<Prisma.FeeSettingsDefaultArgs>()({
  include: {
    marketplaceGateway: {
      select: {
        currency: true,
      },
    },
  },
});

export type FeeSettingsModel = Prisma.FeeSettingsGetPayload<typeof FeeSettingsValidator>;

@Injectable()
export class FeeSettingsRepositoryAdapter
  extends SqlRepositoryBase<FeeSettings>
  implements FeeSettingsRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  async findByMarketplaceGatewayId(marketplaceGatewayId: string): Promise<FeeSettings[]> {
    const feeSettings = await this.db.feeSettings.findMany({
      where: {
        marketplaceGatewayId,
      },
      ...FeeSettingsValidator,
    });

    return feeSettings.map((x) => this.mapToEntity(x));
  }

  protected async createImpl(feeSettings: FeeSettings): Promise<void> {
    await this.db.feeSettings.create({
      data: this.mapToDbModel(feeSettings),
    });
  }

  protected async updateImpl(feeSettings: FeeSettings): Promise<void> {
    await this.db.feeSettings.update({
      data: this.mapToDbModel(feeSettings),
      where: {
        id: feeSettings.getId(),
      },
    });
  }

  private mapToDbModel(feeSettings: FeeSettings): Prisma.FeeSettingsUncheckedCreateInput {
    const {
      id,
      platformFeeFixed,
      platformFeePercentage,
      marketplaceFeeFixed,
      marketplaceFeePercentage,
      marketplaceGatewayId,
      isDefault,
      createdAt,
      updatedAt,
    } = feeSettings.getProps();

    return {
      id,
      platformFeeFixed: platformFeeFixed.getProps().amount,
      platformFeePercentage: new Decimal(platformFeePercentage),
      marketplaceFeeFixed: marketplaceFeeFixed.getProps().amount,
      marketplaceFeePercentage: new Decimal(marketplaceFeePercentage),
      marketplaceGatewayId,
      isDefault,
      createdAt,
      updatedAt,
    };
  }

  async findById(feeSettingsId: string): Promise<FeeSettings | null> {
    const feeSettings = await this.db.feeSettings.findFirst({
      where: {
        id: feeSettingsId,
      },
      ...FeeSettingsValidator,
    });

    return feeSettings && this.mapToEntity(feeSettings);
  }

  protected async deleteImpl(entity: FeeSettings): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findAll(): Promise<FeeSettings[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<FeeSettings>> {
    throw new Error('Method not implemented.');
  }

  private mapToEntity(feeSettings: FeeSettingsModel): FeeSettings {
    return new FeeSettings({
      id: feeSettings.id,
      props: {
        platformFeeFixed: Money.from(
          feeSettings.platformFeeFixed,
          feeSettings.marketplaceGateway.currency,
        ),
        platformFeePercentage: feeSettings.platformFeePercentage.toNumber(),
        marketplaceFeeFixed: Money.from(
          feeSettings.marketplaceFeeFixed,
          feeSettings.marketplaceGateway.currency,
        ),
        marketplaceFeePercentage: feeSettings.marketplaceFeePercentage.toNumber(),
        marketplaceGatewayId: feeSettings.marketplaceGatewayId,
        isDefault: feeSettings.isDefault,
      },
      createdAt: feeSettings.createdAt,
      updatedAt: feeSettings.updatedAt,
    });
  }
}
