/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma, PaymentSplitRefund as DbRefundSplit } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { PaymentSplit, PaymentSplitStatus } from '@/domain/payment-split/payment-split';
import {
  PaymentSplitRefund,
  PaymentSplitRefundStatus,
} from '@/domain/payment-split/payment-split-refund';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';
import { Gateway, Money } from '@/domain/shared';

export const PaymentSplitValidator = Prisma.validator<Prisma.PaymentSplitDefaultArgs>()({
  include: {
    paymentSplitRefunds: true,
    gateway: true,
  },
});

export type PaymentSplitModel = Prisma.PaymentSplitGetPayload<typeof PaymentSplitValidator>;

@Injectable()
export class PaymentSplitRepositoryAdapter
  extends SqlRepositoryBase<PaymentSplit>
  implements PaymentSplitRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  async findByPaymentId(paymentId: string): Promise<PaymentSplit[]> {
    const paymentSplits = await this.db.paymentSplit.findMany({
      ...PaymentSplitValidator,
      where: {
        paymentId,
      },
    });

    return paymentSplits.map((x) => this.mapPaymentSplit(x));
  }

  async findById(id: string): Promise<PaymentSplit | null> {
    const paymentSplit = await this.db.paymentSplit.findFirst({
      ...PaymentSplitValidator,
      where: {
        id,
      },
    });

    return paymentSplit && this.mapPaymentSplit(paymentSplit);
  }

  private mapPaymentSplit(paymentSplit: PaymentSplitModel): PaymentSplit {
    return new PaymentSplit({
      id: paymentSplit.id,
      props: {
        gateway: new Gateway(paymentSplit.gatewayId, paymentSplit.gateway.type),
        paymentId: paymentSplit.paymentId,
        refunds: paymentSplit.paymentSplitRefunds.map((x) => this.mapPaymentSplitRefund(x)),
        status: paymentSplit.status as PaymentSplitStatus,
        refundedAmount: Money.from(paymentSplit.refundedAmount, paymentSplit.currency),
        accountId: paymentSplit.accountId,
        amount: Money.from(paymentSplit.amount, paymentSplit.currency),
      },
      createdAt: paymentSplit.createdAt,
      updatedAt: paymentSplit.updatedAt,
    });
  }

  private mapPaymentSplitRefund(dbRefundSplit: DbRefundSplit): PaymentSplitRefund {
    return new PaymentSplitRefund({
      id: dbRefundSplit.id,
      props: {
        isManual: dbRefundSplit.isManual,
        refundId: dbRefundSplit.refundId,
        amount: Money.from(dbRefundSplit.amount, dbRefundSplit.currency),
        status: dbRefundSplit.status as PaymentSplitRefundStatus,
      },
      createdAt: dbRefundSplit.createdAt,
      updatedAt: dbRefundSplit.updatedAt,
    });
  }

  async createImpl(paymentSplit: PaymentSplit): Promise<void> {
    const { refunds } = paymentSplit.getProps();

    await this.db.paymentSplit.create({
      data: this.mapToPaymentSplitDbRow(paymentSplit),
    });

    await this.db.paymentSplitRefund.createMany({
      data: refunds.map((refund) => this.mapToRefundSplitDbRow(paymentSplit, refund)),
    });
  }

  protected async updateImpl(paymentSplit: PaymentSplit): Promise<void> {
    const { id, refunds: paymentSplitRefunds } = paymentSplit.getProps();

    await this.db.paymentSplit.update({
      data: this.mapToPaymentSplitDbRow(paymentSplit),
      where: {
        id,
      },
    });

    await Promise.all(
      paymentSplitRefunds.map(async (paymentSplitRefund) => {
        const create = this.mapToRefundSplitDbRow(paymentSplit, paymentSplitRefund);

        return this.db.paymentSplitRefund.upsert({
          create,
          update: create,
          where: {
            id: paymentSplitRefund.getId(),
          },
        });
      }),
    );
  }

  protected async deleteImpl(entity: PaymentSplit): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findAll(): Promise<PaymentSplit[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<PaymentSplit>> {
    throw new Error('Method not implemented.');
  }

  private mapToPaymentSplitDbRow(
    paymentSplit: PaymentSplit,
  ): Prisma.PaymentSplitUncheckedCreateInput {
    const props = paymentSplit.getProps();
    const currency = props.amount.getCurrency().getValue();

    return {
      id: props.id,
      gatewayId: props.gateway.getGatewayId(),
      paymentId: props.paymentId,
      accountId: props.accountId,
      amount: props.amount.toNumber(),
      currency,
      refundedAmount: props.refundedAmount.toNumber(),
      status: props.status,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }

  private mapToRefundSplitDbRow(
    paymentSplit: PaymentSplit,
    paymentSplitRefund: PaymentSplitRefund,
  ): Prisma.PaymentSplitRefundUncheckedCreateInput {
    const props = paymentSplitRefund.getProps();
    const currency = props.amount.getCurrency().getValue();

    return {
      id: props.id,
      refundId: props.refundId,
      accountId: paymentSplit.getProps().accountId,
      amount: props.amount.toNumber(),
      isManual: props.isManual,
      currency,
      paymentSplitId: paymentSplit.getId(),
      status: props.status,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
