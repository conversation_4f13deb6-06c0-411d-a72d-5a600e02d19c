import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { ScheduleModule } from '@nestjs/schedule';
import { TerminusModule } from '@nestjs/terminus';
import { RabbitMQModule } from '@sw-web/nestjs-core/rabbitmq';

import { CONTROLLERS } from '@/api';
import { RabbitMQConfig } from '@/config/rabbitmq.config';
import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseModule } from '@/database';

import { PROVIDERS } from './providers';

@Module({
  imports: [
    DatabaseModule,
    CqrsModule,
    TerminusModule,
    HttpModule.register({
      timeout: 5000,
      maxRedirects: 5,
    }),
    ScheduleModule.forRoot(),
    RabbitMQModule.forRoot({
      exchanges: Object.values(RABBIT_MQ_EXCHANGES).map(({ name, type }) => ({
        name,
        type,
      })),
      adminUrl: RabbitMQConfig.adminUrl,
      adminPassword: RabbitMQConfig.adminPassword,
      adminUsername: RabbitMQConfig.adminUsername,
      uri: RabbitMQConfig.url,
    }),
  ],
  controllers: CONTROLLERS,
  providers: PROVIDERS,
})
export class InfrastructureModule {}
