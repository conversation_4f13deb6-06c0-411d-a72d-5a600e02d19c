/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { MarketplaceGateway } from '@/domain/marketplace-gateway/marketplace-gateway';
import {
  FindByMarketplaceAndGatewayIdsProps,
  MarketplaceGatewayRepositoryPort,
} from '@/domain/marketplace-gateway/ports';
import { AllowedCurrencies, Money } from '@/domain/shared';

const Validator = Prisma.validator<Prisma.MarketplaceGatewayDefaultArgs>()({});

export type Model = Prisma.MarketplaceGatewayGetPayload<typeof Validator>;

@Injectable()
export class MarketplaceGatewayRepositoryAdapter
  extends SqlRepositoryBase<MarketplaceGateway>
  implements MarketplaceGatewayRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  protected async createImpl(entity: MarketplaceGateway): Promise<void> {
    await this.db.marketplaceGateway.create({
      data: this.mapToDbRow(entity),
    });
  }

  protected async updateImpl(entity: MarketplaceGateway): Promise<void> {
    const marketplaceGateway = this.mapToDbRow(entity);

    await this.db.marketplaceGateway.update({
      where: {
        id: marketplaceGateway.id,
      },
      data: marketplaceGateway,
    });
  }

  protected async deleteImpl(entity: MarketplaceGateway): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findByMarketplaceAndGatewayId({
    marketplaceId,
    gatewayId,
  }: FindByMarketplaceAndGatewayIdsProps): Promise<MarketplaceGateway> {
    const marketplaceGateway = await this.db.marketplaceGateway.findFirst({
      ...Validator,
      where: {
        marketplaceId,
        gatewayId,
      },
    });

    return this.mapMarketplaceGateway(marketplaceGateway);
  }

  async findById(id: string): Promise<MarketplaceGateway> {
    const marketplaceGateway = await this.db.marketplaceGateway.findFirst({
      ...Validator,
      where: {
        id,
      },
    });

    return this.mapMarketplaceGateway(marketplaceGateway);
  }

  async findAll(): Promise<MarketplaceGateway[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<MarketplaceGateway>> {
    throw new Error('Method not implemented.');
  }

  private async mapMarketplaceGateway(marketplaceGateway: Model): Promise<MarketplaceGateway> {
    if (!marketplaceGateway) {
      return null;
    }

    return new MarketplaceGateway({
      id: marketplaceGateway.id,
      props: {
        balance: Money.from(marketplaceGateway.balance, marketplaceGateway.currency),
        currency: marketplaceGateway.currency as AllowedCurrencies,
        marketplaceId: marketplaceGateway.marketplaceId,
        gatewayId: marketplaceGateway.gatewayId,
        platformPaymentFeeFixed: Money.from(
          marketplaceGateway.platformPaymentFeeFixed,
          marketplaceGateway.currency,
        ),
        platformPaymentFeePercentage: marketplaceGateway.platformPaymentFeePercentage.toNumber(),
        marketplacePaymentFeeFixed: Money.from(
          marketplaceGateway.marketplacePaymentFeeFixed,
          marketplaceGateway.currency,
        ),
        marketplacePaymentFeePercentage:
          marketplaceGateway.marketplacePaymentFeePercentage.toNumber(),
      },
    });
  }

  private mapToDbRow(data: MarketplaceGateway): Prisma.MarketplaceGatewayUncheckedCreateInput {
    const props = data.getProps();

    return {
      id: data.getId(),
      balance: props.balance.toObject().amount,
      currency: props.currency,
      marketplaceId: props.marketplaceId,
      gatewayId: props.gatewayId,
      platformPaymentFeeFixed: props.platformPaymentFeeFixed.toNumber(),
      platformPaymentFeePercentage: props.platformPaymentFeePercentage,
      marketplacePaymentFeeFixed: props.marketplacePaymentFeeFixed.toNumber(),
      marketplacePaymentFeePercentage: props.marketplacePaymentFeePercentage,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
