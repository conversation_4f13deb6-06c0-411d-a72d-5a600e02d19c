/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { Money } from '@/domain/shared';
import { TransactionRepositoryPort } from '@/domain/transaction/ports';
import { Transaction, TransactionStatus, TransactionType } from '@/domain/transaction/transaction';

const TransactionValidator = Prisma.validator<Prisma.TransactionDefaultArgs>()({
  include: {
    dispute: true,
  },
});

export type TransactionModel = Prisma.TransactionGetPayload<typeof TransactionValidator>;

@Injectable()
export class TransactionRepositoryAdapter
  extends SqlRepositoryBase<Transaction>
  implements TransactionRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  protected async createImpl(entity: Transaction): Promise<void> {
    await this.db.transaction.create({
      data: this.mapToDbRow(entity),
    });
  }

  protected async updateImpl(entity: Transaction): Promise<void> {
    const transaction = this.mapToDbRow(entity);

    await this.db.transaction.update({
      where: {
        id: transaction.id,
      },
      data: transaction,
    });
  }

  protected async deleteImpl(entity: Transaction): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<Transaction> {
    const transaction = await this.db.transaction.findFirst({
      ...TransactionValidator,
      where: {
        id,
      },
      include: {
        dispute: true,
      },
    });

    return this.mapTransaction(transaction);
  }

  async findAll(): Promise<Transaction[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<Transaction>> {
    throw new Error('Method not implemented.');
  }

  private mapTransaction(model: TransactionModel): Transaction {
    if (!model) {
      return null;
    }

    return new Transaction({
      id: model.id,
      props: {
        amount: Money.from(model.amount, model.currency),
        type: model.type as TransactionType,
        status: model.status as TransactionStatus,
        paymentId: model.paymentId,
        accountId: model.accountId,
        refundId: model.refundId,
        paymentSplitId: model.paymentSplitId,
        paymentSplitRefundId: model.paymentSplitRefundId,
        disputeId: model.disputeId,
      },
    });
  }

  private mapToDbRow(transaction: Transaction): Prisma.TransactionUncheckedCreateInput {
    const props = transaction.getProps();

    return {
      id: transaction.getId(),
      amount: props.amount.toNumber(),
      currency: props.amount.getCurrency().getProps().value,
      type: props.type,
      status: props.status,
      paymentId: props.paymentId,
      accountId: props.accountId,
      refundId: props.refundId,
      paymentSplitId: props.paymentSplitId,
      paymentSplitRefundId: props.paymentSplitRefundId,
      disputeId: props.disputeId,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
