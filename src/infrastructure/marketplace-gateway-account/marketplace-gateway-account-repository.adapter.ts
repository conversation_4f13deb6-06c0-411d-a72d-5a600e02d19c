/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { MarketplaceGatewayAccount } from '@/domain/marketplace-gateway-account/marketplace-gateway-account';
import { MarketplaceGatewayAccountRepositoryPort } from '@/domain/marketplace-gateway-account/ports';
import { Gateway } from '@/domain/shared';

const MarketplaceGatewayAccountValidator =
  Prisma.validator<Prisma.MarketplaceGatewayAccountDefaultArgs>()({
    include: {
      marketplaceGateway: {
        select: {
          gateway: true,
        },
      },
    },
  });

export type MarketplaceGatewayAccountModel = Prisma.MarketplaceGatewayAccountGetPayload<
  typeof MarketplaceGatewayAccountValidator
>;

@Injectable()
export class MarketplaceGatewayAccountRepositoryAdapter
  extends SqlRepositoryBase<MarketplaceGatewayAccount>
  implements MarketplaceGatewayAccountRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  async findByMarketplaceAndGatewayId(
    marketplaceId: string,
    gatewayId: string,
  ): Promise<MarketplaceGatewayAccount> {
    const marketplaceGatewayAccount = await this.db.marketplaceGatewayAccount.findFirst({
      ...MarketplaceGatewayAccountValidator,
      where: {
        marketplaceGateway: {
          marketplaceId,
          gatewayId,
        },
      },
    });

    if (!marketplaceGatewayAccount) {
      return null;
    }

    return this.mapToEntity(marketplaceGatewayAccount);
  }

  protected async createImpl(marketplaceGatewayAccount: MarketplaceGatewayAccount): Promise<void> {
    await this.db.marketplaceGatewayAccount.create({
      data: this.mapToDbRow(marketplaceGatewayAccount),
    });
  }

  protected async updateImpl(entity: MarketplaceGatewayAccount): Promise<void> {
    throw new Error('Method not implemented.');
  }

  protected async deleteImpl(entity: MarketplaceGatewayAccount): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<MarketplaceGatewayAccount> {
    const marketplaceGatewayAccount = await this.db.marketplaceGatewayAccount.findFirst({
      ...MarketplaceGatewayAccountValidator,
      where: {
        id,
      },
    });

    if (!marketplaceGatewayAccount) {
      return null;
    }

    return this.mapToEntity(marketplaceGatewayAccount);
  }

  async findAll(): Promise<MarketplaceGatewayAccount[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(
    params: PaginatedQueryParams,
  ): Promise<Paginated<MarketplaceGatewayAccount>> {
    throw new Error('Method not implemented.');
  }

  private mapToEntity(
    marketplaceGatewayAccount: MarketplaceGatewayAccountModel,
  ): MarketplaceGatewayAccount {
    return new MarketplaceGatewayAccount({
      id: marketplaceGatewayAccount.id,
      props: {
        name: marketplaceGatewayAccount.name,
        idAtGateway: marketplaceGatewayAccount.idAtGateway,
        gateway: new Gateway(
          marketplaceGatewayAccount.marketplaceGateway.gateway.id,
          marketplaceGatewayAccount.marketplaceGateway.gateway.type,
        ),
        marketplaceGatewayId: marketplaceGatewayAccount.marketplaceGatewayId,
      },
      createdAt: marketplaceGatewayAccount.createdAt,
      updatedAt: marketplaceGatewayAccount.updatedAt,
    });
  }

  private mapToDbRow(
    marketplaceGatewayAccount: MarketplaceGatewayAccount,
  ): Prisma.MarketplaceGatewayAccountUncheckedCreateInput {
    const props = marketplaceGatewayAccount.getProps();

    return {
      id: props.id,
      name: props.name,
      idAtGateway: props.idAtGateway,
      marketplaceGatewayId: props.marketplaceGatewayId,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
