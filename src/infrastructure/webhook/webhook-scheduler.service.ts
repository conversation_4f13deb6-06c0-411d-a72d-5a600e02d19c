import { Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { Webhook } from '@prisma/client';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';
import dayjs from 'dayjs';
import { keys, pickBy, omitBy } from 'lodash';

import { CRON_EXPRESSIONS, RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import { WebhookScheduledIntegrationEvent } from '@/domain/webhook/integration-events';
import { WebhookStatus } from '@/domain/webhook/webhook';

const { webhookSender } = RABBIT_MQ_EXCHANGES;

@Injectable()
export class WebhookSchedulerService {
  private readonly MAX_WEBHOOKS_TO_SCHEDULE = 100;
  private readonly SCHEDULING_TIMEOUT_SECONDS = 30;

  constructor(
    private readonly db: DatabaseService,
    private readonly amqp: RabbitMQClient,
    private readonly logger: AppLoggerService,
  ) {}

  @Cron(CRON_EXPRESSIONS.EVERY_5_SECONDS)
  async handleResetStaleWebhooks() {
    try {
      const schedulingTimeout = dayjs()
        .subtract(this.SCHEDULING_TIMEOUT_SECONDS, 'seconds')
        .toDate();

      await this.db.webhook.updateMany({
        where: {
          status: WebhookStatus.SCHEDULING,
          updatedAt: {
            lt: schedulingTimeout,
          },
        },
        data: {
          status: WebhookStatus.PENDING,
        },
      });
    } catch (error) {
      this.logger.error('Error resetting stale webhooks', error);
    }
  }

  @Cron(CRON_EXPRESSIONS.EVERY_SECOND)
  async handleWebhookScheduling() {
    try {
      const webhooks = await this.getPendingWebhooks();

      if (webhooks.length === 0) {
        return;
      }

      // Mark webhooks as SCHEDULING to prevent re-picking
      await this.markWebhooksScheduling(webhooks);

      const { scheduledWebhookIds, failedWebhookIds } = await this.scheduleWebhooks(webhooks);

      if (scheduledWebhookIds.length) {
        await this.db.webhook.updateMany({
          where: {
            id: {
              in: scheduledWebhookIds,
            },
            status: WebhookStatus.SCHEDULING,
          },
          data: {
            status: WebhookStatus.SCHEDULED,
          },
        });
      }

      if (failedWebhookIds.length) {
        await this.db.webhook.updateMany({
          where: {
            id: {
              in: failedWebhookIds,
            },
            status: WebhookStatus.SCHEDULING,
          },
          data: {
            status: WebhookStatus.PENDING,
          },
        });
      }
    } catch (error) {
      this.logger.error('Error scheduling webhooks', error);
    }
  }

  private async scheduleWebhooks(webhooks: Pick<Webhook, 'id'>[]) {
    const schedulingResults = {};

    await Promise.all(
      webhooks.map(async (webhook) => {
        const isScheduled = await this.scheduleWebhook(webhook);

        schedulingResults[webhook.id] = isScheduled;
      }),
    );

    const scheduledWebhookIds = keys(pickBy(schedulingResults, Boolean));
    const failedWebhookIds = keys(omitBy(schedulingResults, Boolean));

    return {
      scheduledWebhookIds,
      failedWebhookIds,
    };
  }

  private async markWebhooksScheduling(webhooks: Pick<Webhook, 'id'>[]) {
    await this.db.webhook.updateMany({
      where: {
        id: {
          in: webhooks.map(({ id }) => id),
        },
      },
      data: {
        status: WebhookStatus.SCHEDULING,
      },
    });
  }

  private async scheduleWebhook(webhook: Pick<Webhook, 'id'>): Promise<boolean> {
    try {
      await this.amqp.publish(
        webhookSender.name,
        webhookSender.routingKeys.webhookSend.name,
        new WebhookScheduledIntegrationEvent({ aggregateId: webhook.id }),
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Error publishing job to exchange ${webhookSender.name}, routingKey: ${webhookSender.routingKeys.webhookSend.name}`,
        { error, webhook },
      );
      return false;
    }
  }

  private async getPendingWebhooks() {
    return this.db.$queryRaw<Pick<Webhook, 'id'>[]>`
      SELECT id FROM "Webhook"
        WHERE status = ${WebhookStatus.PENDING}
        AND ("nextRetryAt" IS NULL OR "nextRetryAt" <= (NOW() AT TIME ZONE 'UTC'))
        LIMIT ${this.MAX_WEBHOOKS_TO_SCHEDULE}
    `;
  }
}
