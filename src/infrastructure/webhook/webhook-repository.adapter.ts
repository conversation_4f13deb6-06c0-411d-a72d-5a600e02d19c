/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Gateway as GatewayModel, Prisma, Webhook as WebhookModel } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { WebhookData } from '@/application/webhook/interfaces/webhook-event.interface';
import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { Gateway } from '@/domain/shared';
import { WebhookRepositoryPort } from '@/domain/webhook/ports';
import { Webhook, WebhookObject, WebhookStatus, WebhookType } from '@/domain/webhook/webhook';

@Injectable()
export class WebhookRepositoryAdapter
  extends SqlRepositoryBase<Webhook>
  implements WebhookRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  protected async createImpl(webhook: Webhook): Promise<void> {
    await this.db.webhook.create({
      data: this.mapToDbRow(webhook),
    });
  }

  protected async updateImpl(webhook: Webhook): Promise<void> {
    await this.db.webhook.update({
      data: this.mapToDbRow(webhook),
      where: {
        id: webhook.getId(),
      },
    });
  }

  async findById(id: string): Promise<Webhook | null> {
    const webhook = await this.db.webhook.findFirst({
      where: {
        id,
      },
      include: {
        gateway: true,
      },
    });

    if (!webhook) {
      return null;
    }

    return this.mapToEntity(webhook);
  }

  protected async deleteImpl(entity: Webhook): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findAll(): Promise<Webhook[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<Webhook>> {
    throw new Error('Method not implemented.');
  }

  private mapToEntity({ gateway, ...webhook }: WebhookModel & { gateway: GatewayModel }) {
    return new Webhook({
      id: webhook.id,
      createdAt: webhook.createdAt,
      updatedAt: webhook.updatedAt,
      props: {
        object: webhook.object as WebhookObject,
        status: webhook.status as WebhookStatus,
        fails: webhook.fails,
        failReason: webhook.failReason,
        marketplaceId: webhook.marketplaceId,
        gateway: new Gateway(gateway.id, gateway.name),
        type: webhook.type as WebhookType,
        data: webhook.data as WebhookData,
        url: webhook.url,
        lastAttemptAt: webhook.lastAttemptAt,
        nextRetryAt: webhook.nextRetryAt,
      },
    });
  }

  private mapToDbRow(webhook: Webhook): Prisma.WebhookUncheckedCreateInput {
    const props = webhook.getProps();

    return {
      id: props.id,
      object: props.object,
      status: props.status,
      fails: props.fails,
      failReason: props.failReason,
      marketplaceId: props.marketplaceId,
      gatewayId: props.gateway.getGatewayId(),
      type: props.type,
      data: props.data,
      url: props.url,
      lastAttemptAt: props.lastAttemptAt,
      nextRetryAt: props.nextRetryAt,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
