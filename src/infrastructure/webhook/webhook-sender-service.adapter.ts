import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';
import { AxiosError, isAxiosError } from 'axios';
import { pick } from 'lodash';

import { WebhookEvent } from '@/application/webhook/interfaces/webhook-event.interface';
import { SendWebhookResponse, WebhookSenderServicePort } from '@/domain/webhook/ports';
import { Webhook } from '@/domain/webhook/webhook';

@Injectable()
export class WebhookSenderServiceAdapter implements WebhookSenderServicePort {
  private WEBHOOK_AUTHORIZATION_PREFIX = 'X-AUTH-TOKEN';

  constructor(
    private readonly http: HttpService,
    private readonly logger: AppLoggerService,
  ) {}

  async send(webhook: Webhook, secret: string): Promise<SendWebhookResponse> {
    const props = webhook.getProps();
    try {
      const data = this.mapToWebhookEvent(webhook);

      const response = await this.http.axiosRef.post(props.url, data, {
        headers: { [this.WEBHOOK_AUTHORIZATION_PREFIX]: secret },
      });

      this.logger.debug(`Response from webhook ${props.url}`, response.data);

      return {
        hasFailed: false,
      };
    } catch (error: unknown) {
      this.logger.debug(`Error from webhook ${props.url}`, this.formatException(error));

      return {
        hasFailed: true,
        failReason: this.formatFailReason(error),
      };
    }
  }

  private formatException(exception: unknown) {
    if (isAxiosError(exception)) {
      return {
        axiosError: {
          config: pick(exception.config, ['data', 'url', 'baseURL', 'method', 'headers']),
          response: {
            status: exception.response?.status,
            data: exception.response?.data,
            headers: exception.response?.headers,
          },
          code: exception.code,
          message: exception.message,
          names: exception.name,
        },
      };
    }

    return exception;
  }

  private mapToWebhookEvent(webhook: Webhook): WebhookEvent {
    const props = webhook.getProps();

    return {
      id: props.id,
      marketplaceId: props.marketplaceId,
      object: props.object,
      type: props.type,
      data: props.data,
    };
  }

  private formatFailReason(error: unknown) {
    if (!(error instanceof Error)) {
      return JSON.stringify(error);
    }

    let errorMessage = error.message;

    if (error instanceof AxiosError) {
      // Include status and message in the error string
      errorMessage = `Status: ${error.response?.status} - ${
        error.response?.data?.message || error.message
      }`;
    }

    errorMessage += ` - Stack: ${error.stack}`;

    return errorMessage;
  }
}
