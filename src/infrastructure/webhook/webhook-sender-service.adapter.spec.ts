import { HttpService } from '@nestjs/axios';
import { Test, TestingModule } from '@nestjs/testing';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';
import { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { pick } from 'lodash';

import { Webhook } from '@/domain/webhook/webhook';
import { WebhookRepositoryMock } from '@/test/mocks/repositories/webhook-repository.mock';
import { AppLoggerServiceMock } from '@/test/mocks/services/app-logger-service.mock';
import { HttpServiceMock } from '@/test/mocks/services/http-service.mock';

import { WebhookSenderServiceAdapter } from './webhook-sender-service.adapter';

describe('WebhookSenderServiceAdapter', () => {
  let service: WebhookSenderServiceAdapter;
  let httpService: HttpService;
  let logger: AppLoggerService;
  let webhook: Webhook;
  let httpPostSpy: jest.SpyInstance;
  let responseData: unknown;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookSenderServiceAdapter,
        { provide: HttpService, useClass: HttpServiceMock },
        { provide: AppLoggerService, useClass: AppLoggerServiceMock },
      ],
    }).compile();

    service = module.get<WebhookSenderServiceAdapter>(WebhookSenderServiceAdapter);
    httpService = module.get<HttpService>(HttpService);
    logger = module.get<AppLoggerService>(AppLoggerService);
    [webhook] = new WebhookRepositoryMock().db;
    responseData = { data: 'response' };
    httpPostSpy = jest
      .spyOn(httpService.axiosRef, 'post')
      .mockResolvedValue({ data: responseData });
    logger.debug = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should authorize the webhook request with the secret', async () => {
    const secret = 'auth-secret';
    await service.send(webhook, secret);

    expect(httpPostSpy).toHaveBeenCalledWith(expect.any(String), expect.any(Object), {
      headers: expect.objectContaining({ 'X-AUTH-TOKEN': secret }),
    });
  });

  it('should send the webhook request with the correct data', async () => {
    const result = await service.send(webhook, '');

    const props = webhook.getProps();
    expect(httpPostSpy).toHaveBeenCalledWith(
      props.url,
      {
        id: props.id,
        object: props.object,
        type: props.type,
        marketplaceId: props.marketplaceId,
        data: props.data,
      },
      expect.any(Object),
    );

    expect(result.hasFailed).toBe(false);
  });

  it('should log the successful response from the webhook', async () => {
    const result = await service.send(webhook, '');

    expect(httpPostSpy).toHaveBeenCalled();
    expect(result.hasFailed).toBe(false);
    expect(logger.debug).toHaveBeenCalledWith(
      `Response from webhook ${webhook.getProps().url}`,
      responseData,
    );
  });

  it('should log the axios error response from the webhook', async () => {
    const data = { data: 'value' };
    const headers = { header: 'value' };
    const message = 'Network Error';
    const config = {
      data,
      url: 'http://example.com/webhook',
      baseURL: 'http://example.com',
      method: 'POST',
      headers,
      params: {},
      withCredentials: true,
    } as unknown as InternalAxiosRequestConfig;
    const error = new AxiosError(message, '500', config, {}, {
      status: 500,
      data,
      headers,
    } as unknown as AxiosResponse);
    httpPostSpy.mockRejectedValue(error);

    const result = await service.send(webhook, '');

    expect(httpPostSpy).toHaveBeenCalled();
    expect(result).toEqual({
      hasFailed: true,
      failReason: `Status: ${error.response?.status} - ${
        error.response?.data?.message || error.message
      } - Stack: ${error.stack}`,
    });
    expect(logger.debug).toHaveBeenCalledWith(`Error from webhook ${webhook.getProps().url}`, {
      axiosError: {
        config: pick(config, ['data', 'url', 'baseURL', 'method', 'headers']),
        response: {
          status: error.response?.status,
          data: error.response?.data,
          headers: error.response?.headers,
        },
        code: error.code,
        message: error.message,
        names: error.name,
      },
    });
  });

  it('should log the error response from the webhook', async () => {
    const error = new Error('Network Error');
    httpPostSpy.mockRejectedValueOnce(error);

    const result = await service.send(webhook, '');

    expect(httpPostSpy).toHaveBeenCalled();
    expect(result).toEqual({
      hasFailed: true,
      failReason: `${error.message} - Stack: ${error.stack}`,
    });
    expect(logger.debug).toHaveBeenCalledWith(
      `Error from webhook ${webhook.getProps().url}`,
      expect.any(Object),
    );
  });
});
