import { Test, TestingModule } from '@nestjs/testing';
import { Gateway as GatewayModel, Prisma, Webhook as WebhookModel } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { PaginatedQueryParams } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { Webhook } from '@/domain/webhook/webhook';
import { WebhookRepositoryAdapter } from '@/infrastructure/webhook/webhook-repository.adapter';
import { WebhookRepositoryMock } from '@/test/mocks/repositories/webhook-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

describe('WebhookRepositoryAdapter', () => {
  let repository: WebhookRepositoryAdapter;
  let db: DatabaseServiceMock;
  let webhook: Webhook;
  let webhookModel: WebhookModel & { gateway: GatewayModel };
  let findFirstSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookRepositoryAdapter,
        { provide: EventBus, useValue: {} },
        { provide: AppLoggerService, useValue: {} },
        { provide: DatabaseService, useClass: DatabaseServiceMock },
      ],
    }).compile();

    repository = module.get<WebhookRepositoryAdapter>(WebhookRepositoryAdapter);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    [webhook] = new WebhookRepositoryMock().db;
    const { gateway, data, version, ...modelProps } = webhook.getProps();
    webhookModel = {
      ...modelProps,
      data: data as Prisma.JsonValue,
      gatewayId: gateway.getGatewayId(),
      gateway: {
        id: gateway.getGatewayId(),
        name: gateway.getType(), // Use gateway type as name
      } as GatewayModel,
    };

    webhook.publishEvents = jest.fn();
    db.webhook.create = jest.fn();
    db.webhook.update = jest.fn();
    findFirstSpy = jest.spyOn(db.webhook, 'findFirst').mockResolvedValue(webhookModel);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should trigger database webhook create when creating a webhook', async () => {
    await repository.create(webhook);

    expect(webhook.publishEvents).toHaveBeenCalled();
    expect(db.webhook.create).toHaveBeenCalledWith({
      data: expect.any(Object),
    });
  });

  it('should trigger database webhook update when updating a webhook', async () => {
    await repository.update(webhook);

    expect(webhook.publishEvents).toHaveBeenCalled();
    expect(db.webhook.update).toHaveBeenCalledWith({
      data: expect.any(Object),
      where: { id: webhook.getId() },
    });
  });

  it('should search for a webhook by id', async () => {
    const result = await repository.findById(webhook.getId());

    expect(findFirstSpy).toHaveBeenCalledWith({
      where: { id: webhook.getId() },
      include: { gateway: true },
    });
    expect(result).toBeInstanceOf(Webhook);
  });

  it('should return null if webhook is not found by id', async () => {
    findFirstSpy.mockResolvedValue(null);

    const id = 'invalid-webhook-id';
    const result = await repository.findById(id);

    expect(findFirstSpy).toHaveBeenCalledWith({
      where: { id },
      include: { gateway: true },
    });
    expect(result).toBeNull();
  });

  it('should correctly map to webhook entity', async () => {
    const result = await repository.findById(webhook.getId());

    delete webhook.publishEvents; // Remove spy function to compare objects
    expect(result).toBeInstanceOf(Webhook);
    expect(result).toEqual(webhook);
  });

  it('should correctly map to database row', async () => {
    await repository.create(webhook);

    const { gateway, ...data } = webhookModel;
    expect(db.webhook.create).toHaveBeenCalledWith({ data });
  });

  it('should throw an error when trying ta access unimplemented methods', async () => {
    const error = new Error('Method not implemented.');
    await expect(repository.delete(webhook)).rejects.toThrow(error);
    await expect(repository.findAll()).rejects.toThrow(error);
    await expect(repository.findAllPaginated({} as PaginatedQueryParams)).rejects.toThrow(error);
  });
});
