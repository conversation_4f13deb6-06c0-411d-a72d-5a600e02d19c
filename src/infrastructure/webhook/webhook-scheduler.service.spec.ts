import { Test, TestingModule } from '@nestjs/testing';
import { Webhook } from '@prisma/client';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';
import dayjs from 'dayjs';

import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import { WebhookScheduledIntegrationEvent } from '@/domain/webhook/integration-events';
import { WebhookStatus } from '@/domain/webhook/webhook';
import { WebhookSchedulerService } from '@/infrastructure/webhook/webhook-scheduler.service';
import { RabbitMQClientMock } from '@/test/mocks/libs/rabbitmq-client.mock';
import { AppLoggerServiceMock } from '@/test/mocks/services/app-logger-service.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

const { webhookSender } = RABBIT_MQ_EXCHANGES;

describe('WebhookSchedulerService', () => {
  let service: WebhookSchedulerService;
  let db: DatabaseService;
  let amqp: RabbitMQClient;
  let logger: AppLoggerService;
  let updateManySpy: jest.SpyInstance;
  let queryRawSpy: jest.SpyInstance;
  let amqpPublishSpy: jest.SpyInstance;
  let MAX_WEBHOOKS_TO_SCHEDULE: number;
  let webhooks: Pick<Webhook, 'id'>[];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookSchedulerService,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: RabbitMQClient, useClass: RabbitMQClientMock },
        { provide: AppLoggerService, useClass: AppLoggerServiceMock },
      ],
    }).compile();

    service = module.get<WebhookSchedulerService>(WebhookSchedulerService);
    db = module.get<DatabaseService>(DatabaseService);
    amqp = module.get<RabbitMQClient>(RabbitMQClient);
    logger = module.get<AppLoggerService>(AppLoggerService);
    ({ MAX_WEBHOOKS_TO_SCHEDULE } = service as unknown as Record<string, number>);
    webhooks = [{ id: 'webhook-id-0' }, { id: 'webhook-id-1' }, { id: 'webhook-id-2' }];

    updateManySpy = jest.spyOn(db.webhook, 'updateMany').mockResolvedValue(null);
    queryRawSpy = jest.spyOn(db, '$queryRaw').mockResolvedValue(webhooks);
    amqpPublishSpy = jest.spyOn(amqp, 'publish').mockResolvedValue(null);
    logger.error = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('handleResetStaleWebhooks', () => {
    it('should update scheduled webhooks to pending when timeout is reached', async () => {
      const dateSpy = jest.spyOn(dayjs.prototype, 'toDate');

      await service.handleResetStaleWebhooks();

      const schedulingTimeout = dateSpy.mock.results[0].value;
      expect(db.webhook.updateMany).toHaveBeenCalledWith({
        where: {
          status: WebhookStatus.SCHEDULING,
          updatedAt: { lt: schedulingTimeout },
        },
        data: { status: WebhookStatus.PENDING },
      });
    });

    it('should log error when failed to handle resetting stale webhooks', async () => {
      const error = new Error('Resetting stale webhooks error');
      updateManySpy.mockRejectedValue(error);

      await service.handleResetStaleWebhooks();

      expect(logger.error).toHaveBeenCalledWith('Error resetting stale webhooks', error);
    });
  });

  describe('handleWebhookScheduling', () => {
    it('should log error when failed to handle webhook scheduling', async () => {
      const error = new Error('Webhooks error');
      queryRawSpy.mockRejectedValue(error);

      await service.handleWebhookScheduling();

      expect(logger.error).toHaveBeenCalledWith('Error scheduling webhooks', error);
    });

    it('should seek for pending webhooks', async () => {
      queryRawSpy.mockResolvedValue([]);

      await service.handleWebhookScheduling();

      expect(queryRawSpy).toHaveBeenCalledWith(
        expect.any(Array), // SQL query
        WebhookStatus.PENDING,
        MAX_WEBHOOKS_TO_SCHEDULE,
      );
    });

    it('should update webhooks to scheduling status to prevent re-picking', async () => {
      await service.handleWebhookScheduling();

      expect(updateManySpy).toHaveBeenCalledWith({
        where: {
          id: { in: webhooks.map(({ id }) => id) },
        },
        data: { status: WebhookStatus.SCHEDULING },
      });
    });

    it('should publish webhook scheduled event for each scheduled webhook', async () => {
      // Mock the Date object to avoid time-based issues
      const fixedDate = new Date();
      jest.spyOn(global, 'Date').mockImplementation(() => fixedDate);

      await service.handleWebhookScheduling();

      webhooks.forEach((webhook) => {
        expect(amqpPublishSpy).toHaveBeenCalledWith(
          webhookSender.name,
          webhookSender.routingKeys.webhookSend.name,
          new WebhookScheduledIntegrationEvent({ aggregateId: webhook.id }),
        );
      });
    });

    it('should log error when failing to publish webhook scheduled event', async () => {
      const error = new Error('Publishing error');
      amqpPublishSpy.mockRejectedValue(error);

      await service.handleWebhookScheduling();

      expect(logger.error).toHaveBeenCalledWith(
        `Error publishing job to exchange ${webhookSender.name}, routingKey: ${webhookSender.routingKeys.webhookSend.name}`,
        { error, webhook: webhooks[0] },
      );
    });

    it('should update webhooks to scheduled status when successfully scheduled', async () => {
      await service.handleWebhookScheduling();

      expect(updateManySpy).toHaveBeenCalledWith({
        where: {
          id: { in: webhooks.map(({ id }) => id) },
          status: WebhookStatus.SCHEDULING,
        },
        data: { status: WebhookStatus.SCHEDULED },
      });
    });

    it('should update webhooks to pending status when failed to schedule', async () => {
      amqpPublishSpy.mockRejectedValue(new Error('Publishing error'));

      await service.handleWebhookScheduling();

      expect(updateManySpy).toHaveBeenCalledWith({
        where: {
          id: { in: webhooks.map(({ id }) => id) },
          status: WebhookStatus.SCHEDULING,
        },
        data: { status: WebhookStatus.PENDING },
      });
    });
  });
});
