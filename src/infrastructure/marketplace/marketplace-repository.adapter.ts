/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Inject, Injectable } from '@nestjs/common';
import { Marketplace as PrismaMarketplace } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { Marketplace } from '@/domain/marketplace/marketplace';
import { MarketplaceRepositoryPort } from '@/domain/marketplace/ports';
import { WebhookEndpoint } from '@/domain/marketplace/webhook-endpoint.value-object';
import { ApiKeyPair } from '@/domain/shared';

import { SecretManagerServicePort } from '../shared';
import { SECRET_MANAGER_SERVICE_TOKEN } from '../shared/di-tokens';

@Injectable()
export class MarketplaceRepositoryAdapter
  extends SqlRepositoryBase<Marketplace>
  implements MarketplaceRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
    @Inject(SECRET_MANAGER_SERVICE_TOKEN)
    private readonly secretManagerService: SecretManagerServicePort,
  ) {
    super(db, eventBus, logger);
  }

  async findByPublishableKey(publishableKey: string): Promise<Marketplace> {
    const marketplace = await this.db.marketplace.findFirst({
      where: {
        publishableKey,
      },
    });

    if (!marketplace) {
      return null;
    }

    return this.mapToMarketplace(marketplace);
  }

  async findBySecretKey(secretKey: string): Promise<Marketplace | null> {
    const secretHash = this.secretManagerService.generateSecretKeyHash(secretKey);
    const secret = await this.secretManagerService.findSecretByHash(secretHash);

    if (!secret) {
      return null;
    }

    const marketplace = await this.db.marketplace.findFirst({
      where: {
        secretKeyId: secret.getSecretId(),
      },
    });

    if (!marketplace) {
      return null;
    }

    return this.mapToMarketplace(marketplace);
  }

  protected async createImpl(marketplace: Marketplace): Promise<void> {
    const { apiKeyPair, webhookEndpoint } = marketplace.getProps();

    const { secretKey } = apiKeyPair.getProps();
    const { webhookSecret } = webhookEndpoint.getProps();

    await this.secretManagerService.createSecret(secretKey);

    await this.secretManagerService.createSecret(webhookSecret);

    await this.db.marketplace.create({
      data: this.mapToDbRow(marketplace),
    });
  }

  async findById(id: string): Promise<Marketplace | null> {
    const marketplace = await this.db.marketplace.findFirst({
      where: {
        id,
      },
    });

    if (!marketplace) {
      return null;
    }

    return this.mapToMarketplace(marketplace);
  }

  protected async updateImpl(marketplace: Marketplace): Promise<void> {
    const { apiKeyPair, webhookEndpoint } = marketplace.getProps();

    const { secretKey } = apiKeyPair.getProps();
    const { webhookSecret } = webhookEndpoint.getProps();

    await this.secretManagerService.updateSecret(secretKey);

    await this.secretManagerService.updateSecret(webhookSecret);

    await this.db.marketplace.update({
      data: this.mapToDbRow(marketplace),
      where: {
        id: marketplace.getId(),
      },
    });
  }

  protected async deleteImpl(entity: Marketplace): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findAll(): Promise<Marketplace[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<Marketplace>> {
    throw new Error('Method not implemented.');
  }

  private async mapToMarketplace(marketplace: PrismaMarketplace) {
    const secretKey = await this.secretManagerService.findSecretById(marketplace.secretKeyId);
    const webhookSecret = await this.secretManagerService.findSecretById(
      marketplace.webhookSecretId,
    );

    return new Marketplace({
      id: marketplace.id,
      props: {
        name: marketplace.name,
        apiKeyPair: new ApiKeyPair({
          secretKey,
          publishableKey: marketplace.publishableKey,
        }),
        webhookEndpoint: new WebhookEndpoint({
          webhookSecret,
          webhookUrl: marketplace.webhookUrl,
        }),
      },
    });
  }

  private mapToDbRow(marketplace: Marketplace): PrismaMarketplace {
    const props = marketplace.getProps();

    const { secretKey } = props.apiKeyPair.getProps();
    const { webhookSecret } = props.webhookEndpoint.getProps();

    return {
      id: props.id,
      name: props.name,
      description: null,
      secretKeyId: secretKey.getSecretId(),
      publishableKey: props.apiKeyPair.getPublishableKey(),
      webhookSecretId: webhookSecret.getSecretId(),
      webhookUrl: props.webhookEndpoint.getWebhookUrl(),
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
