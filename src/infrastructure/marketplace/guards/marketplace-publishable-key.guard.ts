import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { Request } from 'express';

import { MARKETPLACE_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Marketplace } from '@/domain/marketplace/marketplace';
import { MarketplaceRepositoryPort } from '@/domain/marketplace/ports/marketplace-repository.port';

import { RequestMarketplace } from '../interfaces';

@Injectable()
export class MarketplacePublishableKeyGuard implements CanActivate {
  constructor(
    @Inject(MARKETPLACE_REPOSITORY_TOKEN)
    private readonly marketplaceRepository: MarketplaceRepositoryPort,
  ) {}

  private readonly PUBLISHABLE_KEY_PARAM_NAME = 'publishableKey';

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const publishableKey = this.getPublishableKeyFromRequest(request);

    const marketplace = await this.marketplaceRepository.findByPublishableKey(publishableKey);

    if (!marketplace) {
      throw new UnauthorizedException('Invalid publishable key');
    }

    this.setContextMarketplace(context, marketplace);

    return true;
  }

  private setContextMarketplace(context: ExecutionContext, marketplace: Marketplace) {
    const request = context.switchToHttp().getRequest();

    const requestMarketplace: RequestMarketplace = {
      id: marketplace.getId(),
      name: marketplace.getProps().name,
    };

    request.marketplace = requestMarketplace;
  }

  private getPublishableKeyFromRequest(request: Request): string | undefined {
    const publishableKey = request.query[this.PUBLISHABLE_KEY_PARAM_NAME] as string;

    if (!publishableKey) {
      throw new UnauthorizedException('Publishable key is not provided');
    }

    return publishableKey;
  }
}
