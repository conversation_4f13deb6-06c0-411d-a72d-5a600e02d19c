import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { Request } from 'express';

import { MARKETPLACE_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Marketplace } from '@/domain/marketplace/marketplace';
import { MarketplaceRepositoryPort } from '@/domain/marketplace/ports/marketplace-repository.port';

import { RequestMarketplace } from '../interfaces';

@Injectable()
export class MarketplaceApiKeyGuard implements CanActivate {
  constructor(
    @Inject(MARKETPLACE_REPOSITORY_TOKEN)
    private readonly marketplaceRepository: MarketplaceRepositoryPort,
  ) {}

  private readonly API_KEY_HEADER_NAMES = ['x-api-key', 'api-key'];

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const apiKey = this.getSecretKeyFromRequest(request);

    const marketplace = await this.marketplaceRepository.findBySecretKey(apiKey);

    if (!marketplace) {
      throw new UnauthorizedException('Invalid API key');
    }

    this.setContextMarketplace(context, marketplace);

    return true;
  }

  private setContextMarketplace(context: ExecutionContext, marketplace: Marketplace) {
    const request = context.switchToHttp().getRequest();

    const requestMarketplace: RequestMarketplace = {
      id: marketplace.getId(),
      name: marketplace.getProps().name,
    };

    request.marketplace = requestMarketplace;
  }

  private getSecretKeyFromRequest(request: Request): string {
    for (const headerName of this.API_KEY_HEADER_NAMES) {
      const apiKey = request.headers[headerName];
      if (apiKey) {
        return apiKey as string;
      }
    }
    throw new UnauthorizedException('API key not provided');
  }
}
