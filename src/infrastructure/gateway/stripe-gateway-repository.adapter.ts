import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import Stripe from 'stripe';

import { StripeGatewayRepositoryPort } from '@/application/gateway/ports/stripe-gateway-repository.port';
import { DatabaseService } from '@/database';

@Injectable()
export class StripeGatewayRepositoryAdapter implements StripeGatewayRepositoryPort {
  constructor(private readonly db: DatabaseService) {}

  async getPaymentIntentIdByGatewayPaymentIntentId(
    stripePaymentIntentId: string,
  ): Promise<{ paymentIntentId: string }> {
    const { paymentIntentId } = await this.db.stripePaymentIntent.findFirstOrThrow({
      where: {
        stripePaymentIntentId,
      },
      select: {
        paymentIntentId: true,
      },
    });

    return { paymentIntentId };
  }

  async saveWebhookEvent(event: Stripe.Event): Promise<{ isDuplicate: boolean }> {
    const stripeEvent = await this.db.stripeEvent.upsert({
      where: {
        id: event.id,
      },
      create: this.mapStripeEvent(event),
      update: {
        id: event.id, // to trigger update
      },
    });

    return {
      isDuplicate: stripeEvent.createdAt.getTime() !== stripeEvent.updatedAt.getTime(),
    };
  }

  private mapStripeEvent(event: Stripe.Event): Prisma.StripeEventCreateInput {
    const eventObject = event.data.object;
    const status = 'status' in eventObject ? (eventObject.status as string) : null;
    const eventObjectId = 'id' in eventObject ? (eventObject.id as string) : null;

    return {
      id: event.id,
      type: event.type,
      status,
      eventObjectId,
      data: event as unknown as Prisma.JsonObject,
    };
  }
}
