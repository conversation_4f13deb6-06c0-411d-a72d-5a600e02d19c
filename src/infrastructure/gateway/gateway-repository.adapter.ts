/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma, Gateway as PrismaDbModel } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { Gateway } from '@/domain/gateway/gateway';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { AllowedCurrencies, GatewayType, Money } from '@/domain/shared';

@Injectable()
export class GatewayRepositoryAdapter
  extends SqlRepositoryBase<Gateway>
  implements GatewayRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  async findByGatewayType(gatewayType: GatewayType): Promise<Gateway | null> {
    const gateway = await this.db.gateway.findFirst({
      where: {
        type: gatewayType,
      },
    });

    if (!gateway) {
      return null;
    }

    return this.mapToEntity(gateway);
  }

  private mapToEntity(dbModel: PrismaDbModel): Gateway {
    return new Gateway({
      id: dbModel.id,
      props: {
        name: dbModel.name,
        type: dbModel.type as GatewayType,
        feeFixed: Money.from(dbModel.feeFixed, AllowedCurrencies.USD),
        feePercentage: dbModel.feePercentage.toNumber(),
      },
      createdAt: dbModel.createdAt,
      updatedAt: dbModel.updatedAt,
    });
  }

  protected async createImpl(gateway: Gateway): Promise<void> {
    await this.db.gateway.create({
      data: this.mapToDbRow(gateway),
    });
  }

  private mapToDbRow(gateway: Gateway): Prisma.GatewayUncheckedCreateInput {
    const { id, name, type, createdAt, updatedAt, feeFixed, feePercentage } = gateway.getProps();

    return {
      id,
      name,
      type,
      feePercentage,
      feeFixed: feeFixed.toNumber(),
      createdAt,
      updatedAt,
    };
  }

  async findById(id: string): Promise<Gateway | null> {
    const webhook = await this.db.gateway.findFirst({
      where: {
        id,
      },
    });

    return this.mapToEntity(webhook);
  }

  protected async updateImpl(entity: Gateway): Promise<void> {
    throw new Error('Method not implemented.');
  }

  protected async deleteImpl(entity: Gateway): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findAll(): Promise<Gateway[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<Gateway>> {
    throw new Error('Method not implemented.');
  }
}
