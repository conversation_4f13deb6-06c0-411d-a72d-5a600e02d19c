import { BadRequestException } from '@nestjs/common';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';
import Stripe from 'stripe';

import { StripeConfig } from '@/config/stripe.config';
import { GatewayCustomer } from '@/domain/customer/gateway-customer';
import { GatewayRefund } from '@/domain/payment/gateway-refund';
import { GatewayPaymentIntent } from '@/domain/payment-intent/payment-intent';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';
import { StripePaymentIntent } from '@/domain/payment-intent/stripe-payment-intent';
import {
  AllowedCurrencies,
  CreateGatewayPaymentIntentParams,
  CreateGatewayRefundParams,
  CreateInboundGatewayTransferParams,
  CreateOutboundGatewayTransferParams,
  Gateway,
  GatewayMetadata,
  GatewayServicePort,
  GatewayTransferRepositoryPort,
  GatewayType,
  Money,
  NotSupportedCurrencyError,
  NotSupportedPaymentMethodTypeError,
  PaymentMethodType,
  UpdateGatewayPaymentIntentParams,
} from '@/domain/shared';
import {
  GatewayTransfer,
  GatewayTransferSource,
  GatewayTransferStatus,
} from '@/domain/shared/interfaces';

export class StripeGatewayServiceAdapter implements GatewayServicePort {
  constructor(
    readonly gateway: Gateway,
    private readonly logger: AppLoggerService,
    private readonly stripe: Stripe,
    private readonly gatewayTransferRepository: GatewayTransferRepositoryPort,
    private readonly paymentIntentRepository: PaymentIntentRepositoryPort,
  ) {}

  async getFingerprintByPaymentMethodId(paymentMethodId: string): Promise<string> {
    const paymentMethod = await this.stripe.paymentMethods.retrieve(paymentMethodId);

    return (paymentMethod[paymentMethod.type] as { fingerprint?: string })?.fingerprint || null;
  }

  async getAccount(accountId: string): Promise<{ id: string; email: string }> {
    return this.stripe.accounts
      .retrieve({ stripeAccount: accountId })
      .then((x) => ({ id: x.id, email: x.email }));
  }

  async createRefund(params: CreateGatewayRefundParams): Promise<GatewayRefund> {
    const { payment, idempotencyKey } = params;
    const { paymentIntentId } = payment.getProps();
    const { amount, currency } = params.amount.toObject();

    const paymentIntent = await this.paymentIntentRepository.findById(paymentIntentId);

    const { gatewayPaymentIntent } = paymentIntent.getProps();

    const stripeRefund = await this.stripe.refunds.create(
      {
        payment_intent: gatewayPaymentIntent.getProps().idAtGateway,
        amount: this.roundToCents(amount),
        metadata: params.metadata && this.formatMetadata(params.metadata),
      },
      { idempotencyKey },
    );

    return GatewayRefund.create({
      idAtGateway: stripeRefund.id,
      idempotencyKey,
      amount: Money.from(this.roundToCents(amount), currency),
      gateway: this.gateway,
    });
  }

  async getProviderFee(gatewayPaymentIntent: GatewayPaymentIntent): Promise<Money> {
    const stripePaymentIntent = await this.stripe.paymentIntents.retrieve(
      gatewayPaymentIntent.getProps().idAtGateway,
      {
        expand: ['latest_charge.balance_transaction'],
      },
    );

    const stripeCharge = stripePaymentIntent.latest_charge as Stripe.Charge;

    const stripeBalanceTransaction = stripeCharge.balance_transaction as Stripe.BalanceTransaction;

    return Money.from(
      stripeBalanceTransaction.fee,
      this.mapToMoneyCurrency(stripeBalanceTransaction.currency),
    );
  }

  getValidatedWebhookData(request: Request, data: Buffer): Stripe.Event {
    try {
      const signature = request.headers['stripe-signature'] as string;

      return this.stripe.webhooks.constructEvent(data, signature, StripeConfig.webhookSecret);
    } catch (err) {
      this.logger.error('Error while constructing stripe event ', err);
      throw new BadRequestException();
    }
  }

  async createOutboundTransfer(
    params: CreateOutboundGatewayTransferParams,
  ): Promise<GatewayTransfer> {
    const { receivingGatewayAccountId: gatewayReceivingAccountId, idempotencyKey } = params;
    const { amount, currency } = params.amount.toObject();

    let stripeChargeId: string | undefined;

    if (params.paymentIntentId) {
      const stripePaymentIntent = await this.getStripePaymentIntent(params.paymentIntentId);
      stripeChargeId = stripePaymentIntent.latest_charge as string;
    }

    const stripeTransfer = await this.stripe.transfers.create(
      {
        amount: this.roundToCents(amount),
        currency: this.mapToStripeCurrency(currency),
        destination: gatewayReceivingAccountId,
        source_transaction: stripeChargeId,
        transfer_group: stripeChargeId,
      },
      { idempotencyKey },
    );

    return this.gatewayTransferRepository.create({
      idAtGateway: stripeTransfer.id,
      destinationAccountId: gatewayReceivingAccountId,
      gatewayType: GatewayType.STRIPE,
      source: params.source,
      sourceId: params.sourceId,
      idempotencyKey,
      amount: Money.from(this.roundToCents(amount), currency),
      status: GatewayTransferStatus.SETTLED,
    });
  }

  private roundToCents(amount: number) {
    return Math.floor(amount);
  }

  async createInboundTransfer(params: CreateInboundGatewayTransferParams) {
    const { amount, currency } = params.amount.toObject();

    if (params.originalSourceId) {
      const reversingTransfer = await this.gatewayTransferRepository.findBySource(
        this.getOriginalSource(params.source),
        params.originalSourceId,
      );

      const stripeTransferReversal = await this.stripe.transfers.createReversal(
        reversingTransfer.idAtGateway,
        {
          amount: this.roundToCents(amount),
        },
        { idempotencyKey: params.idempotencyKey },
      );

      return this.gatewayTransferRepository.create({
        idAtGateway: stripeTransferReversal.id,
        destinationAccountId: reversingTransfer.destinationAccountId,
        gatewayType: GatewayType.STRIPE,
        source: params.source,
        sourceId: params.sourceId,
        idempotencyKey: params.idempotencyKey,
        amount: Money.from(this.roundToCents(amount), currency),
        status: GatewayTransferStatus.SETTLED,
      });
    }

    throw new Error('Inbound transfer is not implemented without transfer reversal');
  }

  getOriginalSource(source: GatewayTransferSource) {
    switch (source) {
      case GatewayTransferSource.REFUND_SPLIT:
        return GatewayTransferSource.PAYMENT_SPLIT;
      case GatewayTransferSource.PAYOUT_REVERSAL:
        return GatewayTransferSource.PAYOUT;
      default:
        throw new Error(`No original source for ${source}`);
    }
  }

  private async getStripePaymentIntent(paymentIntentId: string) {
    const paymentIntent = await this.paymentIntentRepository.findById(paymentIntentId);

    return this.stripe.paymentIntents.retrieve(
      paymentIntent.getProps().gatewayPaymentIntent.getProps().idAtGateway,
    );
  }

  async createCustomer(): Promise<GatewayCustomer> {
    const stripeCustomer = await this.stripe.customers.create();

    return GatewayCustomer.create({
      idAtGateway: stripeCustomer.id,
      gateway: this.gateway,
    });
  }

  async createPaymentIntent(data: CreateGatewayPaymentIntentParams): Promise<GatewayPaymentIntent> {
    const { amount, currency } = data.amount.toObject();

    const paymentIntent = await this.stripe.paymentIntents.create({
      amount,
      currency: this.mapToStripeCurrency(currency),
      customer: data.gatewayCustomerId,
      payment_method_types:
        data.paymentMethodType && this.mapToStripePaymentMethodTypes([data.paymentMethodType]),
      statement_descriptor: data.statementDescriptor,
      metadata: this.formatMetadata(data.metadata),
    });

    return StripePaymentIntent.create({
      gateway: this.gateway,
      customerIdAtGateway: paymentIntent.customer as string,
      idAtGateway: paymentIntent.id,
      stripeClientSecret: paymentIntent.client_secret,
    });
  }

  formatMetadata(data: GatewayMetadata = {}): Record<string, string> {
    const formattedData: Record<string, string> = {};

    Object.keys(data).forEach((key) => {
      const value = data[key];

      formattedData[String(key)] =
        typeof value === 'object' ? JSON.stringify(value) : String(value);
    });

    return formattedData;
  }

  async updatePaymentIntent(
    data: UpdateGatewayPaymentIntentParams<StripePaymentIntent>,
  ): Promise<GatewayPaymentIntent> {
    const { amount, currency } = data.amount?.toObject() || {};
    const { gatewayPaymentIntent } = data;

    const paymentIntent = await this.stripe.paymentIntents.update(
      gatewayPaymentIntent.getProps().idAtGateway,
      {
        amount,
        currency,
        payment_method_types:
          data.paymentMethodType && this.mapToStripePaymentMethodTypes([data.paymentMethodType]),
        metadata: this.formatMetadata(data.metadata),
        statement_descriptor: data.statementDescriptor,
      },
    );

    gatewayPaymentIntent.update({
      stripeClientSecret: paymentIntent.client_secret,
    });

    return gatewayPaymentIntent;
  }

  private mapToStripePaymentMethodTypes(paymentMethodTypes: PaymentMethodType[]) {
    return paymentMethodTypes.map((x) => this.mapToStripePaymentMethodType(x));
  }

  private mapToStripePaymentMethodType(paymentMethodType: PaymentMethodType) {
    switch (paymentMethodType) {
      case PaymentMethodType.CARD:
        return 'card';
      case PaymentMethodType.ACH:
        return 'us_bank_account';
      default:
        throw new NotSupportedPaymentMethodTypeError();
    }
  }

  private mapToStripeCurrency(currency: AllowedCurrencies) {
    switch (currency) {
      case AllowedCurrencies.USD:
        return 'usd';
      default:
        throw new NotSupportedCurrencyError();
    }
  }

  private mapToMoneyCurrency(currency: string) {
    switch (currency.toLowerCase()) {
      case 'usd':
        return AllowedCurrencies.USD;
      default:
        throw new NotSupportedCurrencyError();
    }
  }
}
