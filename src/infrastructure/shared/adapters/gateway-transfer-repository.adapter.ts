import { Injectable } from '@nestjs/common';
import { GatewayTransfer as DbGatewayTransfer, Prisma } from '@prisma/client';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { idFactory } from '@/core/ddd';
import { DatabaseService } from '@/database';
import {
  CreateGatewayTransferRepositoryParams,
  GatewayTransferRepositoryPort,
  GatewayType,
  Money,
  UpdateGatewayTransferRepositoryParams,
} from '@/domain/shared';
import {
  GatewayTransfer,
  GatewayTransferSource,
  GatewayTransferStatus,
} from '@/domain/shared/interfaces';

@Injectable()
export class GatewayTransferRepositoryAdapter implements GatewayTransferRepositoryPort {
  constructor(
    readonly db: DatabaseService,
    readonly logger: AppLoggerService,
  ) {}

  async findByGatewayTransferIds(gatewayTransferIds: string[]): Promise<GatewayTransfer[]> {
    const transfers = await this.db.gatewayTransfer.findMany({
      where: {
        idAtGateway: {
          in: gatewayTransferIds,
        },
      },
    });

    return transfers.map((x) => this.mapToGatewayTransfer(x));
  }

  async findBySource(
    sourceType: GatewayTransferSource,
    sourceId: string,
  ): Promise<GatewayTransfer> {
    const transfer = await this.db.gatewayTransfer.findFirst({
      where: {
        source: sourceType,
        sourceId,
      },
    });

    return transfer && this.mapToGatewayTransfer(transfer);
  }

  async findByGatewayTransferId(gatewayTransferId: string): Promise<GatewayTransfer | null> {
    const transfer = await this.db.gatewayTransfer.findFirst({
      where: {
        idAtGateway: gatewayTransferId,
      },
    });

    return transfer && this.mapToGatewayTransfer(transfer);
  }

  private mapToGatewayTransfer(dbTransfer: DbGatewayTransfer): GatewayTransfer {
    return {
      id: dbTransfer.id.toString(),
      idAtGateway: dbTransfer.idAtGateway,
      idempotencyKey: dbTransfer.idempotencyKey,
      destinationAccountId: dbTransfer.destinationAccountId.toString(),
      sourceId: dbTransfer.sourceId.toString(),
      amount: Money.from(dbTransfer.amount, dbTransfer.currency),
      status: dbTransfer.status as GatewayTransferStatus,
      source: dbTransfer.source as GatewayTransferSource,
      gatewayType: dbTransfer.gatewayType as GatewayType,
      createdAt: dbTransfer.createdAt,
      updatedAt: dbTransfer.updatedAt,
      version: dbTransfer.version,
    };
  }

  private mapToDbGatewayTransfer(
    gatewayTransfer: CreateGatewayTransferRepositoryParams,
  ): Prisma.GatewayTransferUncheckedCreateInput {
    const { amount, currency } = gatewayTransfer.amount.toObject();

    return {
      id: idFactory(),
      idAtGateway: gatewayTransfer.idAtGateway,
      idempotencyKey: gatewayTransfer.idempotencyKey,
      destinationAccountId: gatewayTransfer.destinationAccountId,
      sourceId: gatewayTransfer.sourceId,
      amount,
      currency,
      status: gatewayTransfer.status as GatewayTransferStatus,
      source: gatewayTransfer.source as GatewayTransferSource,
      gatewayType: gatewayTransfer.gatewayType as GatewayType,
    };
  }

  async create(params: CreateGatewayTransferRepositoryParams): Promise<GatewayTransfer> {
    const transfer = await this.db.gatewayTransfer.upsert({
      create: this.mapToDbGatewayTransfer(params),
      update: this.mapToDbGatewayTransfer(params),
      where: {
        idAtGateway: params.idAtGateway,
      },
    });

    return this.mapToGatewayTransfer(transfer);
  }

  async update(params: UpdateGatewayTransferRepositoryParams): Promise<void> {
    const { count: updatedCount } = await this.db.gatewayTransfer.updateMany({
      where: {
        id: params.id,
        version: params.version,
      },
      data: {
        ...this.mapToDbGatewayTransfer(params),
        version: params.version + 1,
      },
    });

    if (!updatedCount) {
      throw new Error('Transfer not found'); // todo: custom error
    }
  }
}
