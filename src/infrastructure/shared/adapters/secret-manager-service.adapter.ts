import { createHash } from 'crypto';

import { Inject, Injectable } from '@nestjs/common';

import { DatabaseService } from '@/database';
import { ManagedSecret } from '@/domain/shared';

import { SECRET_MANAGER_PROVIDER_TOKEN } from '../di-tokens';
import { SecretManagerProviderPort } from '../ports/secret-manager-provider.port';
import { SecretManagerServicePort } from '../ports/secret-manager-service.port';

@Injectable()
export class SecretManagerServiceAdapter implements SecretManagerServicePort {
  private secretHashToValueMap = new Map<string, ManagedSecret>();

  constructor(
    @Inject(SECRET_MANAGER_PROVIDER_TOKEN) readonly provider: SecretManagerProviderPort,
    private readonly db: DatabaseService,
  ) {}

  generateSecretKeyHash(secretValue: string): string {
    const hash = createHash('sha256');
    hash.update(secretValue);
    return hash.digest('hex');
  }

  async createSecret(secret: ManagedSecret): Promise<void> {
    const secretFromProvider = await this.provider.createSecret(secret.getValue());

    await this.db.managedSecret.create({
      data: {
        id: secret.getSecretId(),
        hash: secret.getSecretHash(),
        idAtProvider: secretFromProvider.id,
        providerType: secretFromProvider.providerType,
      },
    });
  }

  async updateSecret(secret: ManagedSecret): Promise<void> {
    const secretFromDb = await this.db.managedSecret.update({
      where: {
        id: secret.getSecretId(),
      },
      data: {
        hash: secret.getSecretHash(),
      },
    });

    // todo: check when not exist

    await this.provider.updateSecret(secretFromDb.idAtProvider, secret.getValue());

    this.cacheSecret(secret);
  }

  async findSecretById(secretId: string): Promise<ManagedSecret | null> {
    return this.findSecretByHashOrSecretId({ secretId });
  }

  async findSecretByHash(secretHash: string): Promise<ManagedSecret | null> {
    return this.findSecretByHashOrSecretId({ hash: secretHash });
  }

  private async findSecretByHashOrSecretId(criteria: { secretId: string } | { hash: string }) {
    const secretFromDb = await this.db.managedSecret.findFirst({
      where: 'secretId' in criteria ? { id: criteria.secretId } : { hash: criteria.hash },
    });

    if (!secretFromDb) {
      return null;
    }

    const cachedSecret = this.findSecretFromCache(secretFromDb.hash);

    if (cachedSecret) {
      return cachedSecret;
    }

    const secretValue = await this.provider.findSecret(secretFromDb.idAtProvider);

    if (!secretValue) {
      return null;
    }

    const secret = new ManagedSecret({
      secretId: secretFromDb.id,
      secretValue,
      secretHash: secretFromDb.hash,
    });

    this.cacheSecret(secret);

    return secret;
  }

  private findSecretFromCache(hash: string): ManagedSecret | null {
    return this.secretHashToValueMap.get(hash) || null;
  }

  private cacheSecret(secret: ManagedSecret): void {
    this.secretHashToValueMap.set(secret.getSecretHash(), secret);
  }
}
