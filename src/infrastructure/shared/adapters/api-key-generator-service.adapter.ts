import { createHash, randomBytes } from 'crypto';

import { Injectable } from '@nestjs/common';

import { idFactory } from '@/core/ddd';
import { ApiKeyGeneratorServicePort, ApiKeyPair, ManagedSecret } from '@/domain/shared';

@Injectable()
export class ApiKeyGeneratorServiceAdapter implements ApiKeyGeneratorServicePort {
  private SECRET_KEY_PREFIX = 'sk';
  private PUBLISHABLE_KEY_PREFIX = 'pk';

  generatePair(): ApiKeyPair {
    const publishableKey = this.generatePublicKey(this.PUBLISHABLE_KEY_PREFIX);
    const secretKey = this.generateSecretKey(this.SECRET_KEY_PREFIX);

    return new ApiKeyPair({
      publishableKey,
      secretKey,
    });
  }

  generateSecretKey(prefix: string) {
    const secretValue = this.generateSecretKeyValue(prefix);

    return new ManagedSecret({
      secretId: idFactory(),
      secretValue,
      secretHash: this.generateSecretKeyHash(secretValue),
    });
  }

  private generatePublicKey(prefix: string) {
    return this.generateSecretKeyValue(prefix);
  }

  private generateSecretKeyValue(prefix: string) {
    const hexSecret = randomBytes(48).toString('hex');

    return `${prefix}_${hexSecret}`;
  }

  generateSecretKeyHash(secretValue: string): string {
    const hash = createHash('sha256');
    hash.update(secretValue);
    return hash.digest('hex');
  }
}
