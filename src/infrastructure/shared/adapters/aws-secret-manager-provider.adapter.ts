import * as AWS from '@aws-sdk/client-secrets-manager';
import { Injectable } from '@nestjs/common';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';
import { v4 } from 'uuid';

import { AWSConfig } from '@/config/aws.config';

import { ManagedSecretProviderType } from '../enums';
import { SecretManagerProviderPort } from '../ports/secret-manager-provider.port';

@Injectable()
export class AWSSecretManagerProviderAdapter implements SecretManagerProviderPort {
  private readonly client: AWS.SecretsManager;

  constructor(private readonly logger: AppLoggerService) {
    this.client = new AWS.SecretsManager({
      region: AWSConfig.region,
      credentials: { accessKeyId: AWSConfig.accessKeyId, secretAccessKey: AWSConfig.secretKey },
    });
  }

  async deleteSecret(id: string): Promise<void> {
    await this.client.deleteSecret({ SecretId: id });
  }

  async findSecret(id: string): Promise<string | null> {
    try {
      const secret = await this.client.getSecretValue({ SecretId: id });

      if (secret.SecretString) {
        return secret.SecretString;
      }

      return null;
    } catch (err) {
      this.logger.error(`Error getting secret from AWS`, err);
      throw err;
    }
  }

  async createSecret(
    secretValue: string,
  ): Promise<{ id: string; providerType: ManagedSecretProviderType.AWS }> {
    const secret = await this.client.createSecret({
      SecretString: secretValue,
      Name: this.generateUuid(),
    });

    return {
      id: secret.ARN,
      providerType: ManagedSecretProviderType.AWS,
    };
  }

  async updateSecret(id: string, secretValue: string): Promise<void> {
    try {
      await this.client.putSecretValue({
        SecretString: secretValue,
        SecretId: id,
      });
    } catch (err) {
      this.logger.error(`Error updating AWS secret by id ${id}`, err);
      throw err;
    }
  }

  private generateUuid() {
    return v4();
  }
}
