import { Inject, Injectable } from '@nestjs/common';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';
import Stripe from 'stripe';

import {
  GATEWAY_TRANSFER_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';
import {
  Gateway,
  GatewayServiceFactoryPort,
  GatewayServicePort,
  GatewayTransferRepositoryPort,
  GatewayType,
  NotSupportedGatewayTypeError,
} from '@/domain/shared';

import { StripeGatewayServiceAdapter } from './stripe-payment-gateway-service.adapter';

@Injectable()
export class GatewayServiceFactoryAdapter implements GatewayServiceFactoryPort {
  constructor(
    @Inject(GATEWAY_TRANSFER_REPOSITORY_TOKEN)
    private readonly gatewayTransferRepository: GatewayTransferRepositoryPort,
    @Inject(PAYMENT_INTENT_REPOSITORY_TOKEN)
    private readonly paymentIntentRepository: PaymentIntentRepositoryPort,
    private readonly stripe: Stripe,
    private readonly logger: AppLoggerService,
  ) {}

  getGateway(gateway: Gateway): GatewayServicePort {
    switch (gateway.getType()) {
      case GatewayType.STRIPE:
        return new StripeGatewayServiceAdapter(
          gateway,
          this.logger,
          this.stripe,
          this.gatewayTransferRepository,
          this.paymentIntentRepository,
        );
      default:
        throw new NotSupportedGatewayTypeError();
    }
  }
}
