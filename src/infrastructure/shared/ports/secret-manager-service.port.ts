import { ManagedSecret } from '@/domain/shared';

import { SecretManagerProviderPort } from './secret-manager-provider.port';

export interface SecretManagerServicePort {
  provider: SecretManagerProviderPort;

  generateSecretKeyHash(secretValue: string): string;

  findSecretByHash(secretHash: string): Promise<ManagedSecret | null>;

  findSecretById(secretId: string): Promise<ManagedSecret | null>;

  createSecret(secret: ManagedSecret): Promise<void>;

  updateSecret(secret: ManagedSecret): Promise<void>;
}
