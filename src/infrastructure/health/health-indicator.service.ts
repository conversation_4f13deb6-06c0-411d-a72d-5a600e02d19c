import { Injectable } from '@nestjs/common';
import { HealthCheckError, HealthIndicator, HealthIndicatorResult } from '@nestjs/terminus';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';

import { DatabaseService } from '@/database';

@Injectable()
export class HealthIndicatorService extends HealthIndicator {
  constructor(
    private readonly db: DatabaseService,
    private readonly rabbitmq: RabbitMQClient,
  ) {
    super();
  }

  public async checkDB(): Promise<HealthIndicatorResult> {
    try {
      await this.db.$queryRaw`SELECT 1`;
      return this.getStatus('database', true);
    } catch (e) {
      throw new HealthCheckError('Db check failed', e);
    }
  }

  public checkRabbitMQ(): HealthIndicatorResult {
    if (this.rabbitmq.isHealthy()) {
      return this.getStatus('rabbitmq', true);
    }

    return this.getStatus('rabbitmq', false);
  }
}
