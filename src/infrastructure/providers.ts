import { Provider } from '@nestjs/common';

import { APPLICATION_PROVIDERS } from '@/application';
import { STRIPE_GATEWAY_REPOSITORY_TOKEN } from '@/application/di-tokens';
import { DOMAIN_PROVIDERS } from '@/domain';
import {
  ACCOUNT_REPOSITORY_TOKEN,
  API_KEY_GENERATOR_SERVICE_TOKEN,
  CUSTOMER_REPOSITORY_TOKEN,
  DEBT_REPOSITORY_TOKEN,
  DISPUTE_REPOSITORY_TOKEN,
  TRANSACTION_REPOSITORY_TOKEN,
  GATEWAY_REPOSITORY_TOKEN,
  GATEWAY_SERVICE_FACTORY_TOKEN,
  GATEWAY_TRANSFER_REPOSITORY_TOKEN,
  MARKETPLACE_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
  WEB<PERSON><PERSON><PERSON>_REPOSITORY_TOKEN,
  WEB<PERSON><PERSON><PERSON>_SENDER_SERVICE_TOKEN,
  MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
  PAYOUT_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN,
  PAYMENT_METHOD_REPOSITORY_TOKEN,
  FEE_SETTINGS_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { MarketplaceGatewayRepositoryAdapter } from '@/infrastructure/marketplace-gateway';
import { MarketplaceGatewayTransactionRepositoryAdapter } from '@/infrastructure/marketplace-gateway-transaction';
import { TransactionRepositoryAdapter } from '@/infrastructure/transaction';

import { AccountRepositoryAdapter } from './account';
import { CustomerRepositoryAdapter } from './customer';
import { DebtRepositoryAdapter } from './debt';
import { DisputeRepositoryAdapter } from './dispute';
import { FeeSettingsRepositoryAdapter } from './fee-settings';
import { GatewayRepositoryAdapter, StripeGatewayRepositoryAdapter } from './gateway';
import { HealthIndicatorService } from './health';
import { MarketplaceRepositoryAdapter } from './marketplace';
import { MarketplaceGatewayAccountRepositoryAdapter } from './marketplace-gateway-account';
import { PaymentRepositoryAdapter } from './payment';
import { PaymentIntentRepositoryAdapter } from './payment-intent';
import { PaymentMethodRepositoryAdapter } from './payment-method';
import { PaymentSplitRepositoryAdapter } from './payment-split';
import { PayoutRepositoryAdapter } from './payout';
import {
  AWSSecretManagerProviderAdapter,
  ApiKeyGeneratorServiceAdapter,
  GatewayServiceFactoryAdapter,
  SecretManagerServiceAdapter,
} from './shared';
import { GatewayTransferRepositoryAdapter } from './shared/adapters/gateway-transfer-repository.adapter';
import { SECRET_MANAGER_PROVIDER_TOKEN, SECRET_MANAGER_SERVICE_TOKEN } from './shared/di-tokens';
import { WebhookRepositoryAdapter } from './webhook';
import { WebhookSchedulerService } from './webhook/webhook-scheduler.service';
import { WebhookSenderServiceAdapter } from './webhook/webhook-sender-service.adapter';

const REPOSITORIES: Provider[] = [
  {
    provide: CUSTOMER_REPOSITORY_TOKEN,
    useClass: CustomerRepositoryAdapter,
  },
  {
    provide: GATEWAY_REPOSITORY_TOKEN,
    useClass: GatewayRepositoryAdapter,
  },
  {
    provide: PAYMENT_INTENT_REPOSITORY_TOKEN,
    useClass: PaymentIntentRepositoryAdapter,
  },
  {
    provide: ACCOUNT_REPOSITORY_TOKEN,
    useClass: AccountRepositoryAdapter,
  },
  {
    provide: STRIPE_GATEWAY_REPOSITORY_TOKEN,
    useClass: StripeGatewayRepositoryAdapter,
  },
  {
    provide: MARKETPLACE_REPOSITORY_TOKEN,
    useClass: MarketplaceRepositoryAdapter,
  },
  {
    provide: PAYMENT_REPOSITORY_TOKEN,
    useClass: PaymentRepositoryAdapter,
  },
  {
    provide: PAYMENT_SPLIT_REPOSITORY_TOKEN,
    useClass: PaymentSplitRepositoryAdapter,
  },
  {
    provide: WEBHOOK_REPOSITORY_TOKEN,
    useClass: WebhookRepositoryAdapter,
  },
  {
    provide: GATEWAY_TRANSFER_REPOSITORY_TOKEN,
    useClass: GatewayTransferRepositoryAdapter,
  },
  {
    provide: DEBT_REPOSITORY_TOKEN,
    useClass: DebtRepositoryAdapter,
  },
  {
    provide: DISPUTE_REPOSITORY_TOKEN,
    useClass: DisputeRepositoryAdapter,
  },
  {
    provide: TRANSACTION_REPOSITORY_TOKEN,
    useClass: TransactionRepositoryAdapter,
  },
  {
    provide: MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN,
    useClass: MarketplaceGatewayTransactionRepositoryAdapter,
  },
  {
    provide: MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
    useClass: MarketplaceGatewayRepositoryAdapter,
  },
  {
    provide: PAYOUT_REPOSITORY_TOKEN,
    useClass: PayoutRepositoryAdapter,
  },
  {
    provide: MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN,
    useClass: MarketplaceGatewayAccountRepositoryAdapter,
  },
  {
    provide: MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN,
    useClass: MarketplaceGatewayTransactionRepositoryAdapter,
  },
  {
    provide: MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
    useClass: MarketplaceGatewayRepositoryAdapter,
  },
  {
    provide: PAYMENT_METHOD_REPOSITORY_TOKEN,
    useClass: PaymentMethodRepositoryAdapter,
  },
  {
    provide: FEE_SETTINGS_REPOSITORY_TOKEN,
    useClass: FeeSettingsRepositoryAdapter,
  },
];

const SERVICES: Provider[] = [
  {
    provide: GATEWAY_SERVICE_FACTORY_TOKEN,
    useClass: GatewayServiceFactoryAdapter,
  },
  {
    provide: SECRET_MANAGER_PROVIDER_TOKEN,
    useClass: AWSSecretManagerProviderAdapter,
  },
  {
    provide: SECRET_MANAGER_SERVICE_TOKEN,
    useClass: SecretManagerServiceAdapter,
  },
  {
    provide: API_KEY_GENERATOR_SERVICE_TOKEN,
    useClass: ApiKeyGeneratorServiceAdapter,
  },
  {
    provide: WEBHOOK_SENDER_SERVICE_TOKEN,
    useClass: WebhookSenderServiceAdapter,
  },
  WebhookSchedulerService,
  HealthIndicatorService,
];

export const PROVIDERS = [
  ...REPOSITORIES,
  ...SERVICES,
  ...APPLICATION_PROVIDERS,
  ...DOMAIN_PROVIDERS,
];
