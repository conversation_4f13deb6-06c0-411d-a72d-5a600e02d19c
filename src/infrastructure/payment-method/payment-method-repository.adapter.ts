/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { GatewayPaymentMethod } from '@/domain/payment-method/gateway-payment-method';
import { PaymentMethod } from '@/domain/payment-method/payment-method';
import { PaymentMethodRepositoryPort } from '@/domain/payment-method/ports';
import { Gateway, PaymentMethodType } from '@/domain/shared';

const PaymentMethodValidator = Prisma.validator<Prisma.PaymentMethodDefaultArgs>()({
  include: {
    gatewayPaymentMethod: {
      include: {
        gateway: true,
      },
    },
  },
});

export type PaymentMethodModel = Prisma.PaymentMethodGetPayload<typeof PaymentMethodValidator>;

@Injectable()
export class PaymentMethodRepositoryAdapter
  extends SqlRepositoryBase<PaymentMethod>
  implements PaymentMethodRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  async findByFingerprint(fingerprint: string): Promise<PaymentMethod | null> {
    const paymentMethod = await this.db.paymentMethod.findFirst({
      ...PaymentMethodValidator,
      where: {
        gatewayPaymentMethod: {
          fingerprint,
        },
      },
    });

    return paymentMethod && this.mapToEntity(paymentMethod);
  }

  protected async createImpl(paymentMethod: PaymentMethod): Promise<void> {
    await this.db.$transaction(async () => {
      await this.db.paymentMethod.create({
        data: this.mapToPaymentMethodDbRow(paymentMethod),
      });

      await this.db.gatewayPaymentMethod.create({
        data: this.mapToGatewayPaymentMethodDbRow(paymentMethod),
      });
    });
  }

  protected async updateImpl(paymentMethod: PaymentMethod): Promise<void> {
    await this.db.$transaction(async () => {
      await this.db.paymentMethod.update({
        data: this.mapToPaymentMethodDbRow(paymentMethod),
        where: {
          id: paymentMethod.getId(),
        },
      });

      await this.db.gatewayPaymentMethod.update({
        data: this.mapToGatewayPaymentMethodDbRow(paymentMethod),
        where: {
          paymentMethodId: paymentMethod.getId(),
        },
      });
    });
  }

  protected async deleteImpl(entity: PaymentMethod): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<PaymentMethod> {
    const paymentMethod = await this.db.paymentMethod.findFirst({
      ...PaymentMethodValidator,
      where: {
        id,
      },
    });

    return paymentMethod && this.mapToEntity(paymentMethod);
  }

  async findAll(): Promise<PaymentMethod[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<PaymentMethod>> {
    throw new Error('Method not implemented.');
  }

  private mapToEntity(paymentMethod: PaymentMethodModel): PaymentMethod {
    if (!paymentMethod.gatewayPaymentMethod) {
      throw new Error('Gateway payment method cannot be found');
    }

    return new PaymentMethod({
      id: paymentMethod.id,
      props: {
        type: paymentMethod.type as PaymentMethodType,
        cardLast4: paymentMethod.cardLast4,
        cardExpiryYear: paymentMethod.cardExpiryYear,
        cardExpiryMonth: paymentMethod.cardExpiryMonth,
        gatewayPaymentMethod: this.mapToGatewayPaymentMethod(paymentMethod.gatewayPaymentMethod),
      },
      createdAt: paymentMethod.createdAt,
      updatedAt: paymentMethod.updatedAt,
    });
  }

  private mapToGatewayPaymentMethod(
    gatewayPaymentMethod: PaymentMethodModel['gatewayPaymentMethod'],
  ): GatewayPaymentMethod {
    const { idAtGateway, fingerprint, gateway } = gatewayPaymentMethod;
    return new GatewayPaymentMethod({
      id: gatewayPaymentMethod.id,
      props: {
        idAtGateway,
        gateway: new Gateway(gateway.id, gateway.type),
        fingerprint,
      },
    });
  }

  private mapToPaymentMethodDbRow(
    paymentMethod: PaymentMethod,
  ): Prisma.PaymentMethodUncheckedCreateInput {
    const props = paymentMethod.getProps();

    return {
      id: props.id,
      type: props.type,
      cardLast4: props.cardLast4,
      cardExpiryYear: props.cardExpiryYear,
      cardExpiryMonth: props.cardExpiryMonth,
    };
  }

  private mapToGatewayPaymentMethodDbRow(
    paymentMethod: PaymentMethod,
  ): Prisma.GatewayPaymentMethodUncheckedCreateInput {
    const props = paymentMethod.getProps().gatewayPaymentMethod.getProps();

    return {
      id: props.id,
      paymentMethodId: paymentMethod.getId(),
      idAtGateway: props.idAtGateway,
      fingerprint: props.fingerprint,
      gatewayId: props.gateway.getGatewayId(),
    };
  }
}
