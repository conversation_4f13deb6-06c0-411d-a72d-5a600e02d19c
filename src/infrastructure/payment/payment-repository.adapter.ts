/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma, GatewayRefund as DbGatewayRefund } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { GatewayRefund } from '@/domain/payment/gateway-refund';
import { Payment, PaymentStatus } from '@/domain/payment/payment';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { Refund, RefundStatus, RefundType } from '@/domain/payment/refund';
import { Gateway, Money, PaymentMethodType } from '@/domain/shared';

export const PaymentValidator = Prisma.validator<Prisma.PaymentDefaultArgs>()({
  include: {
    gateway: {
      select: {
        id: true,
        type: true,
      },
    },
  },
});

export type PaymentModel = Prisma.PaymentGetPayload<typeof PaymentValidator>;

@Injectable()
export class PaymentRepositoryAdapter
  extends SqlRepositoryBase<Payment>
  implements PaymentRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  async findByGatewayRefundId(gatewayRefundId: string): Promise<Payment> {
    const payment = await this.db.payment.findFirst({
      ...PaymentValidator,
      where: {
        gatewayRefunds: {
          some: {
            idAtGateway: gatewayRefundId,
          },
        },
      },
    });

    return payment && this.mapPayment(payment);
  }

  async findByPaymentSplitId(paymentSplitId: string): Promise<Payment> {
    const payment = await this.db.payment.findFirst({
      ...PaymentValidator,
      where: {
        paymentSplits: {
          some: {
            id: paymentSplitId,
          },
        },
      },
    });

    return payment && this.mapPayment(payment);
  }

  async findByRefundId(refundId: string): Promise<Payment> {
    const payment = await this.db.payment.findFirst({
      ...PaymentValidator,
      where: {
        refunds: {
          some: {
            id: refundId,
          },
        },
      },
    });

    return payment && this.mapPayment(payment);
  }

  async findByPaymentIntentId(paymentIntentId: string): Promise<Payment | null> {
    const payment = await this.db.payment.findFirst({
      ...PaymentValidator,
      where: {
        paymentIntentId,
      },
    });

    return payment && this.mapPayment(payment);
  }

  async findById(id: string): Promise<Payment | null> {
    const payment = await this.db.payment.findFirst({
      ...PaymentValidator,
      where: {
        id,
      },
    });

    return payment && this.mapPayment(payment);
  }

  private async mapPayment(payment: PaymentModel): Promise<Payment> {
    const gateway = new Gateway(payment.gatewayId, payment.gateway.type);

    return new Payment({
      id: payment.id,
      props: {
        amount: Money.from(payment.amount, payment.currency),
        marketplaceId: payment.marketplaceId,
        gateway: new Gateway(payment.gatewayId, payment.gateway.type),
        status: payment.status as PaymentStatus,
        paymentMethodType: payment.paymentMethodType as PaymentMethodType,
        customerId: payment.customerId,
        paymentIntentId: payment.paymentIntentId,
        refundedAmount: Money.from(payment.refundedAmount, payment.currency),
        refunds: await this.getRefunds(payment.id, gateway),
        providerFee: Money.from(payment.providerFee, payment.currency),
        providerFeeFixed: Money.from(payment.providerFeeFixed, payment.currency),
        platformPaymentFee: Money.from(payment.platformPaymentFee, payment.currency),
        platformPaymentFeeFixed: Money.from(payment.platformPaymentFeeFixed, payment.currency),
        marketplacePaymentFee: Money.from(payment.marketplacePaymentFee, payment.currency),
        marketplacePaymentFeeFixed: Money.from(
          payment.marketplacePaymentFeeFixed,
          payment.currency,
        ),
        paymentMethodId: payment.paymentMethodId,
        providerFeePercentage: payment.providerFeePercentage.toNumber(),
        platformPaymentFeePercentage: payment.platformPaymentFeePercentage.toNumber(),
        marketplacePaymentFeePercentage: payment.marketplacePaymentFeePercentage.toNumber(),
        platformOrderFee: Money.from(payment.platformOrderFee, payment.currency),
        marketplaceOrderFee: Money.from(payment.marketplaceOrderFee, payment.currency),
        marketplacePaymentFeeRefunded: Money.from(
          payment.marketplacePaymentFeeRefunded,
          payment.currency,
        ),
        platformPaymentFeeRefunded: Money.from(
          payment.platformPaymentFeeRefunded,
          payment.currency,
        ),
        providerPaymentFeeRefunded: Money.from(
          payment.providerPaymentFeeRefunded,
          payment.currency,
        ),
        marketplaceOrderFeeRefunded: Money.from(
          payment.marketplaceOrderFeeRefunded,
          payment.currency,
        ),
        platformOrderFeeRefunded: Money.from(payment.platformOrderFeeRefunded, payment.currency),
        feeSettingsId: payment.feeSettingsId,
      },
      version: payment.version,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
    });
  }

  private async getRefunds(paymentId: string, gateway: Gateway): Promise<Refund[]> {
    const refunds = await this.db.refund.findMany({
      where: {
        paymentId,
      },
      include: {
        paymentSplitRefunds: true,
        gatewayRefund: true,
      },
    });

    return refunds.map(
      (refund) =>
        new Refund({
          id: refund.id,
          props: {
            metadata: refund.metadata as Prisma.JsonObject,
            amount: Money.from(refund.amount, refund.currency),
            status: refund.status as RefundStatus,
            type: refund.type as RefundType,
            gatewayRefund: this.mapGatewayRefund(refund.gatewayRefund, gateway),
            isManual: refund.isManual,
            marketplacePaymentFeeRefunded: Money.from(
              refund.marketplacePaymentFeeRefunded,
              refund.currency,
            ),
            platformPaymentFeeRefunded: Money.from(
              refund.platformPaymentFeeRefunded,
              refund.currency,
            ),
            providerPaymentFeeRefunded: Money.from(
              refund.providerPaymentFeeRefunded,
              refund.currency,
            ),
            marketplaceOrderFeeRefunded: Money.from(
              refund.marketplaceOrderFeeRefunded,
              refund.currency,
            ),
            platformOrderFeeRefunded: Money.from(refund.platformOrderFeeRefunded, refund.currency),
          },
          createdAt: refund.createdAt,
          updatedAt: refund.updatedAt,
        }),
    );
  }

  private mapGatewayRefund(dbGatewayRefund: DbGatewayRefund, gateway: Gateway): GatewayRefund {
    return new GatewayRefund({
      id: dbGatewayRefund.id,
      props: {
        idAtGateway: dbGatewayRefund.idAtGateway,
        idempotencyKey: dbGatewayRefund.idempotencyKey,
        amount: Money.from(dbGatewayRefund.amount, dbGatewayRefund.currency),
        gateway,
      },
      createdAt: dbGatewayRefund.createdAt,
      updatedAt: dbGatewayRefund.updatedAt,
    });
  }

  async createImpl(payment: Payment): Promise<void> {
    const { id, refunds } = payment.getProps();

    await this.db.payment.create({
      data: this.mapToPaymentDbRow(payment),
    });

    await this.db.refund.createMany({
      data: refunds.map((refund) => this.mapToRefundDbRow(id, refund)),
    });

    const gatewayRefunds = refunds.map((refund) =>
      this.mapToGatewayRefundDbRow(
        payment.getId(),
        refund.getId(),
        refund.getProps().gatewayRefund,
      ),
    );

    await this.db.gatewayRefund.createMany({
      data: gatewayRefunds,
    });
  }

  protected async updateImpl(payment: Payment): Promise<void> {
    const { id, refunds } = payment.getProps();

    await this.db.payment.update({
      data: this.mapToPaymentUpdateDbRow(payment),
      where: {
        id,
        version: payment.getProps().version,
      },
    });

    await Promise.all(
      refunds.map(async (refund) => {
        const create = this.mapToRefundDbRow(id, refund);

        return this.db.refund.upsert({
          create,
          update: create,
          where: {
            id: refund.getId(),
          },
        });
      }),
    );

    await Promise.all(
      refunds.map(async (refund) => {
        const { gatewayRefund } = refund.getProps();
        const create = this.mapToGatewayRefundDbRow(payment.getId(), refund.getId(), gatewayRefund);

        return this.db.gatewayRefund.upsert({
          create,
          update: create,
          where: {
            id: gatewayRefund.getId(),
          },
        });
      }),
    );
  }

  protected async deleteImpl(entity: Payment): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findAll(): Promise<Payment[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<Payment>> {
    throw new Error('Method not implemented.');
  }

  private mapToPaymentUpdateDbRow(payment: Payment): Prisma.PaymentUncheckedUpdateInput {
    const dbRow = this.mapToPaymentDbRow(payment);

    return {
      ...dbRow,
      version: payment.getProps().version + 1,
    };
  }

  private mapToPaymentDbRow(payment: Payment): Prisma.PaymentUncheckedCreateInput {
    const props = payment.getProps();
    const currency = props.amount.getCurrency().getValue();

    return {
      id: props.id,
      amount: props.amount.toNumber(),
      currency,
      status: props.status,
      paymentMethodType: props.paymentMethodType,
      gatewayId: props.gateway.getGatewayId(),
      customerId: props.customerId,
      marketplaceId: props.marketplaceId,
      refundedAmount: props.refundedAmount.toNumber(),
      paymentIntentId: props.paymentIntentId,
      // payment fees
      providerFee: props.providerFee.toNumber(),
      providerFeeFixed: props.providerFeeFixed.toNumber(),
      providerFeePercentage: props.providerFeePercentage,
      platformPaymentFee: props.platformPaymentFee.toNumber(),
      platformPaymentFeeFixed: props.platformPaymentFeeFixed.toNumber(),
      platformPaymentFeePercentage: props.platformPaymentFeePercentage,
      marketplacePaymentFee: props.marketplacePaymentFee.toNumber(),
      marketplacePaymentFeeFixed: props.marketplacePaymentFeeFixed.toNumber(),
      marketplacePaymentFeePercentage: props.marketplacePaymentFeePercentage,
      platformOrderFee: props.platformOrderFee.toNumber(),
      marketplaceOrderFee: props.marketplaceOrderFee.toNumber(),
      // refunded fees
      marketplacePaymentFeeRefunded: props.marketplacePaymentFeeRefunded.toNumber(),
      platformPaymentFeeRefunded: props.platformPaymentFeeRefunded.toNumber(),
      providerPaymentFeeRefunded: props.providerPaymentFeeRefunded.toNumber(),
      marketplaceOrderFeeRefunded: props.marketplaceOrderFeeRefunded.toNumber(),
      platformOrderFeeRefunded: props.platformOrderFeeRefunded.toNumber(),
      paymentMethodId: props.paymentMethodId,
      feeSettingsId: props.feeSettingsId,
      version: props.version,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }

  private mapToGatewayRefundDbRow(
    paymentId: string,
    refundId: string,
    gatewayRefund: GatewayRefund,
  ): Prisma.GatewayRefundUncheckedCreateInput {
    const props = gatewayRefund.getProps();
    const currency = props.amount.getCurrency().getValue();

    return {
      id: props.id,
      amount: props.amount.toNumber(),
      currency,
      idAtGateway: props.idAtGateway,
      idempotencyKey: props.idempotencyKey,
      gatewayType: props.gateway.getType(),
      refundId,
      paymentId,
    };
  }

  private mapToRefundDbRow(paymentId: string, refund: Refund): Prisma.RefundUncheckedCreateInput {
    const props = refund.getProps();
    const currency = props.amount.getCurrency().getValue();

    return {
      id: props.id,
      paymentId,
      amount: props.amount.toNumber(),
      currency,
      type: props.type,
      status: props.status,
      isManual: props.isManual,
      metadata: props.metadata as Prisma.JsonObject,
      marketplacePaymentFeeRefunded: props.marketplacePaymentFeeRefunded.toNumber(),
      platformPaymentFeeRefunded: props.platformPaymentFeeRefunded.toNumber(),
      providerPaymentFeeRefunded: props.providerPaymentFeeRefunded.toNumber(),
      marketplaceOrderFeeRefunded: props.marketplaceOrderFeeRefunded.toNumber(),
      platformOrderFeeRefunded: props.platformOrderFeeRefunded.toNumber(),
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
