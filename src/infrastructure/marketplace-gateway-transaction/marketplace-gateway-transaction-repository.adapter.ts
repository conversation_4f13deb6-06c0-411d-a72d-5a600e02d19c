/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { MarketplaceGatewayTransaction } from '@/domain/marketplace-gateway-transaction/marketplace-gateway-transaction';
import { MarketplaceGatewayTransactionRepositoryPort } from '@/domain/marketplace-gateway-transaction/ports';
import { Money } from '@/domain/shared';

const Validator = Prisma.validator<Prisma.MarketplaceGatewayTransactionDefaultArgs>()({});

export type Model = Prisma.MarketplaceGatewayTransactionGetPayload<typeof Validator>;

@Injectable()
export class MarketplaceGatewayTransactionRepositoryAdapter
  extends SqlRepositoryBase<MarketplaceGatewayTransaction>
  implements MarketplaceGatewayTransactionRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  protected async createImpl(entity: MarketplaceGatewayTransaction): Promise<void> {
    await this.db.marketplaceGatewayTransaction.create({
      data: this.mapToDbRow(entity),
    });
  }

  protected async updateImpl(entity: MarketplaceGatewayTransaction): Promise<void> {
    const transaction = this.mapToDbRow(entity);

    await this.db.marketplaceGatewayTransaction.update({
      where: {
        id: transaction.id,
      },
      data: transaction,
    });
  }

  protected async deleteImpl(entity: MarketplaceGatewayTransaction): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<MarketplaceGatewayTransaction> {
    const transaction = await this.db.marketplaceGatewayTransaction.findFirst({
      ...Validator,
      where: {
        id,
      },
    });

    return transaction && this.mapTransaction(transaction);
  }

  async findAll(): Promise<MarketplaceGatewayTransaction[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(
    params: PaginatedQueryParams,
  ): Promise<Paginated<MarketplaceGatewayTransaction>> {
    throw new Error('Method not implemented.');
  }

  private async mapTransaction(model: Model): Promise<MarketplaceGatewayTransaction> {
    if (!model) {
      return null;
    }

    return new MarketplaceGatewayTransaction({
      id: model.id,
      props: {
        marketplaceGatewayId: model.marketplaceGatewayId,
        amount: Money.from(model.amount, model.currency),
        transactionId: model.transactionId,
      },
    });
  }

  private mapToDbRow(
    data: MarketplaceGatewayTransaction,
  ): Prisma.MarketplaceGatewayTransactionUncheckedCreateInput {
    const props = data.getProps();

    return {
      id: data.getId(),
      marketplaceGatewayId: props.marketplaceGatewayId,
      amount: props.amount.toNumber(),
      currency: props.amount.getCurrency().getProps().value,
      transactionId: props.transactionId,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
