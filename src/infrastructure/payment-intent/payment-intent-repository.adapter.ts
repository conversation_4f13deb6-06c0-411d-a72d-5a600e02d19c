/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import {
  GatewayPaymentIntent,
  PaymentIntent,
  PaymentIntentStatus,
} from '@/domain/payment-intent/payment-intent';
import { PaymentSplit } from '@/domain/payment-intent/payment-split.value-object';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';
import { StripePaymentIntent } from '@/domain/payment-intent/stripe-payment-intent';
import {
  Gateway,
  GatewayType,
  Money,
  NotSupportedGatewayTypeError,
  PaymentMethodType,
} from '@/domain/shared';

export const PaymentIntentValidator = Prisma.validator<Prisma.PaymentIntentDefaultArgs>()({
  include: {
    paymentIntentDistributions: true,
    gateway: {
      select: {
        id: true,
        type: true,
      },
    },
  },
});

export type PaymentIntentModel = Prisma.PaymentIntentGetPayload<typeof PaymentIntentValidator>;

@Injectable()
export class PaymentIntentRepositoryAdapter
  extends SqlRepositoryBase<PaymentIntent>
  implements PaymentIntentRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  async findByPaymentIdAndMarketplaceId(
    paymentId: string,
    marketplaceId: string,
  ): Promise<PaymentIntent> {
    const paymentIntent = await this.db.paymentIntent.findFirst({
      ...PaymentIntentValidator,
      where: {
        payments: {
          some: {
            id: paymentId,
          },
        },
        marketplaceId,
      },
    });

    return paymentIntent && this.mapToEntity(paymentIntent);
  }

  async findByIdAndMarketplaceId(id: string, marketplaceId: string): Promise<PaymentIntent> {
    const paymentIntent = await this.db.paymentIntent.findFirst({
      ...PaymentIntentValidator,
      where: {
        id,
        marketplaceId,
      },
    });

    if (!paymentIntent) {
      return null;
    }

    return this.mapToEntity(paymentIntent);
  }

  async findById(id: string): Promise<PaymentIntent | null> {
    const paymentIntent = await this.db.paymentIntent.findFirst({
      ...PaymentIntentValidator,
      where: {
        id,
      },
    });

    if (!paymentIntent) {
      return null;
    }

    return this.mapToEntity(paymentIntent);
  }

  async findPaymentIntentByGatewayPaymentIntentId(
    gatewayPaymentIntentId: string,
    gateway: Gateway,
  ) {
    switch (gateway.getType()) {
      case GatewayType.STRIPE:
        return this.getPaymentIntentByStripePaymentIntentId(gatewayPaymentIntentId, gateway);
      default:
        throw new NotSupportedGatewayTypeError();
    }
  }

  private async mapToEntity(paymentIntent: PaymentIntentModel): Promise<PaymentIntent> {
    const gateway = new Gateway(paymentIntent.gateway.id, paymentIntent.gateway.type);

    const gatewayPaymentIntent = await this.getGatewayPaymentIntent(paymentIntent.id, gateway);

    return new PaymentIntent({
      id: paymentIntent.id,
      props: {
        amount: Money.from(paymentIntent.amount, paymentIntent.currency),
        marketplaceId: paymentIntent.marketplaceId,
        gateway: new Gateway(paymentIntent.gatewayId, paymentIntent.gateway.type),
        status: paymentIntent.status as PaymentIntentStatus,
        paymentMethodType: paymentIntent.paymentMethodType as PaymentMethodType,
        customerId: paymentIntent.customerId,
        businessEntityId: paymentIntent.businessEntityId,
        gatewayPaymentIntent,
        metadata: paymentIntent.metadata as Prisma.JsonObject,
        statementDescriptor: paymentIntent.statementDescriptor,
        paymentSplits: paymentIntent.paymentIntentDistributions.map(
          (split) =>
            new PaymentSplit({
              accountId: split.accountId,
              amount: Money.from(split.amount, paymentIntent.currency),
            }),
        ),
        providerFee: Money.from(paymentIntent.providerFee, paymentIntent.currency),
        providerFeeFixed: Money.from(paymentIntent.providerFeeFixed, paymentIntent.currency),
        platformPaymentFee: Money.from(paymentIntent.platformPaymentFee, paymentIntent.currency),
        platformPaymentFeeFixed: Money.from(
          paymentIntent.platformPaymentFeeFixed,
          paymentIntent.currency,
        ),
        marketplacePaymentFee: Money.from(
          paymentIntent.marketplacePaymentFee,
          paymentIntent.currency,
        ),
        marketplacePaymentFeeFixed: Money.from(
          paymentIntent.marketplacePaymentFeeFixed,
          paymentIntent.currency,
        ),
        providerFeePercentage: paymentIntent.providerFeePercentage.toNumber(),
        platformPaymentFeePercentage: paymentIntent.platformPaymentFeePercentage.toNumber(),
        marketplacePaymentFeePercentage: paymentIntent.marketplacePaymentFeePercentage.toNumber(),
        platformOrderFee: Money.from(paymentIntent.platformOrderFee, paymentIntent.currency),
        marketplaceOrderFee: Money.from(paymentIntent.marketplaceOrderFee, paymentIntent.currency),
        feeSettingsId: paymentIntent.feeSettingsId,
      },
    });
  }

  private async getGatewayPaymentIntent(paymentIntentId: string, gateway: Gateway) {
    switch (gateway.getType()) {
      case GatewayType.STRIPE:
        return this.getStripePaymentIntent(paymentIntentId, gateway);
      default:
        throw new NotSupportedGatewayTypeError();
    }
  }

  private async getStripePaymentIntent(paymentIntentId: string, gateway: Gateway) {
    const stripePaymentIntent = await this.db.stripePaymentIntent.findFirstOrThrow({
      where: {
        paymentIntentId,
      },
    });

    return new StripePaymentIntent({
      id: stripePaymentIntent.id,
      props: {
        gateway,
        stripeClientSecret: stripePaymentIntent.clientSecret,
        customerIdAtGateway: stripePaymentIntent.stripeCustomerId,
        idAtGateway: stripePaymentIntent.stripePaymentIntentId,
      },
    });
  }

  private async getPaymentIntentByStripePaymentIntentId(
    stripePaymentIntentId: string,
    gateway: Gateway,
  ) {
    const stripePaymentIntent = await this.db.stripePaymentIntent.findFirstOrThrow({
      where: {
        stripePaymentIntentId,
      },
      include: {
        paymentIntent: {
          include: {
            paymentIntentDistributions: true,
          },
        },
      },
    });

    return this.mapToEntity({
      ...stripePaymentIntent.paymentIntent,
      gateway: {
        id: gateway.getGatewayId(),
        type: gateway.getType(),
      },
    });
  }

  async createImpl(paymentIntent: PaymentIntent): Promise<void> {
    const { id, paymentSplits, gatewayPaymentIntent } = paymentIntent.getProps();

    const { currency } = paymentIntent.getProps().amount.toObject();

    await this.db.paymentIntent.create({
      data: this.mapToDbRow(paymentIntent),
    });

    await this.db.paymentIntentDistribution.createMany({
      data: paymentSplits.map((split) => {
        const { accountId, amount: splitAmount } = split.getProps();

        return {
          paymentIntentId: id,
          accountId,
          amount: splitAmount.getProps().amount,
          currency,
        };
      }),
    });

    await this.saveGatewayPaymentIntent(id, gatewayPaymentIntent);
  }

  private async saveGatewayPaymentIntent(
    paymentIntentId: string,
    gatewayPaymentIntent: GatewayPaymentIntent,
  ) {
    const { gateway } = gatewayPaymentIntent.getProps();

    switch (gateway.getType()) {
      case GatewayType.STRIPE:
        return this.saveStripeGatewayIntent(
          paymentIntentId,
          gatewayPaymentIntent as StripePaymentIntent,
        );
      default:
        throw new NotSupportedGatewayTypeError();
    }
  }

  private async saveStripeGatewayIntent(
    paymentIntentId: string,
    stripePaymentIntent: StripePaymentIntent,
  ) {
    const {
      id,
      stripeClientSecret,
      customerIdAtGateway: stripeCustomerId,
      idAtGateway: stripePaymentIntentId,
    } = stripePaymentIntent.getProps();

    const data = {
      id,
      stripeCustomerId,
      stripePaymentIntentId,
      clientSecret: stripeClientSecret,
      paymentIntentId,
    };

    await this.db.stripePaymentIntent.upsert({
      create: data,
      update: data,
      where: {
        id,
      },
    });
  }

  protected async updateImpl(paymentIntent: PaymentIntent): Promise<void> {
    const { id, paymentSplits, gatewayPaymentIntent } = paymentIntent.getProps();

    const { currency } = paymentIntent.getProps().amount.toObject();

    await this.db.paymentIntent.update({
      data: this.mapToDbRow(paymentIntent),
      where: {
        id, // todo: add version
      },
    });

    await this.db.paymentIntentDistribution.deleteMany({
      where: {
        paymentIntentId: id,
      },
    });

    await this.db.paymentIntentDistribution.createMany({
      data: paymentSplits.map((split) => {
        const { accountId, amount: splitAmount } = split.getProps();

        return {
          paymentIntentId: id,
          accountId,
          amount: splitAmount.getProps().amount,
          currency,
        };
      }),
    });

    await this.saveGatewayPaymentIntent(id, gatewayPaymentIntent);
  }

  protected async deleteImpl(entity: PaymentIntent): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findAll(): Promise<PaymentIntent[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<PaymentIntent>> {
    throw new Error('Method not implemented.');
  }

  private mapToDbRow(paymentIntent: PaymentIntent): Prisma.PaymentIntentUncheckedCreateInput {
    const props = paymentIntent.getProps();
    const { amount, currency } = props.amount.toObject();

    return {
      id: props.id,
      amount,
      currency,
      status: props.status,
      paymentMethodType: props.paymentMethodType,
      gatewayId: props.gateway.getGatewayId(),
      customerId: props.customerId,
      marketplaceId: props.marketplaceId,
      businessEntityId: props.businessEntityId,
      metadata: props.metadata as Prisma.JsonObject,
      statementDescriptor: props.statementDescriptor,
      providerFee: props.providerFee.toNumber(),
      providerFeeFixed: props.providerFeeFixed.toNumber(),
      providerFeePercentage: props.providerFeePercentage,
      platformPaymentFee: props.platformPaymentFee.toNumber(),
      platformPaymentFeeFixed: props.platformPaymentFeeFixed.toNumber(),
      platformPaymentFeePercentage: props.platformPaymentFeePercentage,
      marketplacePaymentFee: props.marketplacePaymentFee.toNumber(),
      marketplacePaymentFeeFixed: props.marketplacePaymentFeeFixed.toNumber(),
      marketplacePaymentFeePercentage: props.marketplacePaymentFeePercentage,
      marketplaceOrderFee: props.marketplaceOrderFee.toNumber(),
      platformOrderFee: props.platformOrderFee.toNumber(),
      feeSettingsId: props.feeSettingsId,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
