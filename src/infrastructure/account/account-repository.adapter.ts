/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/require-await */
import { Injectable } from '@nestjs/common';
import { Prisma, Account as DbAccount } from '@prisma/client';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { SqlRepositoryBase } from '@/core/db';
import { PaginatedQueryParams, Paginated } from '@/core/ddd';
import { DatabaseService } from '@/database';
import { Account } from '@/domain/account/account';
import { GatewayAccount } from '@/domain/account/gateway-account';
import { AccountRepositoryPort } from '@/domain/account/ports';
import { Gateway, Money } from '@/domain/shared';

const AccountValidator = Prisma.validator<Prisma.AccountDefaultArgs>()({
  include: {
    gatewayAccounts: {
      include: {
        gateway: {
          select: {
            id: true,
            type: true,
          },
        },
      },
    },
  },
});

export type AccountModel = Prisma.AccountGetPayload<typeof AccountValidator>;

@Injectable()
export class AccountRepositoryAdapter
  extends SqlRepositoryBase<Account>
  implements AccountRepositoryPort
{
  constructor(
    readonly db: DatabaseService,
    readonly eventBus: EventBus,
    readonly logger: AppLoggerService,
  ) {
    super(db, eventBus, logger);
  }

  async findByMarketplaceAndAccountIds(
    marketplaceId: string,
    accountIds: string[],
  ): Promise<Account[]> {
    const accounts = await this.db.account.findMany({
      ...AccountValidator,
      where: {
        id: {
          in: accountIds,
        },
        marketplaceId,
      },
    });

    return accounts.map((account) => this.mapToEntity(account));
  }

  async accountsExistInMarketplace(marketplaceId: string, accountIds: string[]): Promise<boolean> {
    const count = await this.db.account.count({
      where: {
        id: {
          in: accountIds,
        },
        marketplaceId,
      },
    });

    return count === accountIds.length;
  }

  protected async createImpl(account: Account): Promise<void> {
    await this.db.account.create({
      data: this.mapToDbRow(account),
    });

    const gatewayAccountsData = account
      .getProps()
      .gatewayAccounts.map((x) => this.mapToGatewayAccountDbRow(account.getId(), x));

    await this.db.gatewayAccount.createMany({ data: gatewayAccountsData });
  }

  protected async updateImpl(account: Account): Promise<void> {
    await this.db.account.update({
      data: this.mapToDbRow(account),
      where: {
        id: account.getId(),
      },
    });

    const updateGatewayAccounts = account.getProps().gatewayAccounts.map(async (gatewayAccount) => {
      const createData = this.mapToGatewayAccountDbRow(account.getId(), gatewayAccount);

      await this.db.gatewayAccount.upsert({
        create: createData,
        update: createData,
        where: {
          id: gatewayAccount.getId(),
        },
      });
    });

    await Promise.all(updateGatewayAccounts);
  }

  private mapToGatewayAccountDbRow(
    accountId: string,
    gatewayAccount: GatewayAccount,
  ): Prisma.GatewayAccountUncheckedCreateInput {
    const props = gatewayAccount.getProps();
    return {
      id: props.id,
      gatewayId: props.gateway.getGatewayId(),
      accountId,
      idAtGateway: props.idAtGateway,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }

  protected async deleteImpl(entity: Account): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<Account> {
    const account = await this.db.account.findFirst({
      ...AccountValidator,
      where: {
        id,
      },
    });

    if (!account) {
      return null;
    }

    return this.mapToEntity(account);
  }

  async findAll(): Promise<Account[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<Account>> {
    throw new Error('Method not implemented.');
  }

  private mapToEntity(account: AccountModel): Account {
    return new Account({
      id: account.id,
      props: {
        email: null,
        gatewayAccounts: account.gatewayAccounts.map((x) => this.mapToGatewayAccount(x)),
        name: account.name,
        balance: Money.from(account.balance, account.currency),
        debt: Money.from(account.debt, account.currency),
        marketplaceId: account.marketplaceId,
      },
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
    });
  }

  private mapToGatewayAccount(
    gatewayAccount: AccountModel['gatewayAccounts'][number],
  ): GatewayAccount {
    const { gateway } = gatewayAccount;
    return new GatewayAccount({
      id: gatewayAccount.id,
      props: {
        idAtGateway: gatewayAccount.idAtGateway,
        gateway: new Gateway(gateway.id, gateway.type),
      },
      createdAt: gatewayAccount.createdAt,
      updatedAt: gatewayAccount.updatedAt,
    });
  }

  private mapToDbRow(account: Account): Prisma.AccountUncheckedCreateInput {
    const props = account.getProps();

    return {
      id: props.id,
      name: props.name,
      balance: props.balance.getProps().amount,
      debt: props.debt.getProps().amount,
      currency: props.balance.getProps().currency.getValue(),
      marketplaceId: props.marketplaceId,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
