import { PayoutReversal } from '@/domain/payout/payout-reversal';

export class ReversePayoutResponseDto {
  id: string;
  amount: number;
  currency: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;

  constructor(payoutReversal: PayoutReversal) {
    const props = payoutReversal.getProps();
    const { amount, currency } = props.amount.toObject();

    this.id = props.id;
    this.amount = amount;
    this.currency = currency;
    this.status = props.status;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
  }
}
