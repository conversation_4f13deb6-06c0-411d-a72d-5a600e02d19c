import { Body, Controller, Param, Post, UseGuards } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { RabbitSubscribe } from '@sw-web/nestjs-core/rabbitmq';

import { CreatePayoutRefundReversalsCommand } from '@/application/payout/commands/create-payout-refund-reversals/create-payout-refund-reversals.command';
import { ProcessPayoutCommand } from '@/application/payout/commands/process-payout';
import { ProcessPayoutReversalCommand } from '@/application/payout/commands/process-payout-reversal';
import { ReversePayoutCommand } from '@/application/payout/commands/reverse-payout';
import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { RefundIssuedIntegrationEvent } from '@/domain/payment/integration-events';
import {
  PayoutCreatedIntegrationEvent,
  PayoutReversalCreatedIntegrationEvent,
} from '@/domain/payout/integration-events';
import { PayoutReversal } from '@/domain/payout/payout-reversal';
import {
  MarketplaceApiKeyGuard,
  ReqMarketplace,
  RequestMarketplace,
} from '@/infrastructure/marketplace';

import { ReversePayoutRequestDto, ReversePayoutResponseDto } from './dto';

const { payout: payoutExchange, payment: paymentExchange } = RABBIT_MQ_EXCHANGES;

const {
  payoutCreated: payoutCreatedRoutingKey,
  payoutReversalCreated: payoutReversalCreatedRoutingKey,
} = payoutExchange.routingKeys;

const { refundIssued: refundIssuedRoutingKey } = paymentExchange.routingKeys;

@Controller('payouts')
export class PayoutController {
  constructor(private readonly commandBus: CommandBus) {}

  @RabbitSubscribe({
    exchange: paymentExchange.name,
    routingKey: refundIssuedRoutingKey.name,
    queue: refundIssuedRoutingKey.queues.createPayoutRefundReversals.name,
  })
  async processRefund(event: RefundIssuedIntegrationEvent) {
    await this.commandBus.execute<CreatePayoutRefundReversalsCommand, void>(
      new CreatePayoutRefundReversalsCommand({
        paymentId: event.aggregateId,
        refundId: event.refundId,
      }),
    );
  }

  @RabbitSubscribe({
    exchange: payoutExchange.name,
    routingKey: payoutCreatedRoutingKey.name,
    queue: payoutCreatedRoutingKey.queues.processPayout.name,
  })
  async processPayout(event: PayoutCreatedIntegrationEvent) {
    await this.commandBus.execute<ProcessPayoutCommand, void>(
      new ProcessPayoutCommand({ payoutId: event.aggregateId }),
    );
  }

  @RabbitSubscribe({
    exchange: payoutExchange.name,
    routingKey: payoutReversalCreatedRoutingKey.name,
    queue: payoutReversalCreatedRoutingKey.queues.processPayoutReversal.name,
  })
  async processPayoutReversal(event: PayoutReversalCreatedIntegrationEvent) {
    await this.commandBus.execute<ProcessPayoutReversalCommand, void>(
      new ProcessPayoutReversalCommand({
        payoutId: event.aggregateId,
        reversalId: event.reversalId,
      }),
    );
  }

  @UseGuards(MarketplaceApiKeyGuard)
  @Post(':payoutId/reverse')
  async create(
    @Body() dto: ReversePayoutRequestDto,
    @ReqMarketplace() marketplace: RequestMarketplace,
    @Param('payoutId') payoutId: string,
  ) {
    const payoutReversal = await this.commandBus.execute<ReversePayoutCommand, PayoutReversal>(
      new ReversePayoutCommand({
        marketplaceId: marketplace.id,
        payoutId,
        amount: dto.amount,
        currency: dto.currency,
      }),
    );

    return new ReversePayoutResponseDto(payoutReversal);
  }
}
