import { Controller, Get } from '@nestjs/common';
import { HealthCheck, HealthCheckService } from '@nestjs/terminus';

import { HealthIndicatorService } from '@/infrastructure/health';

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private healthIndicator: HealthIndicatorService,
  ) {}

  @Get()
  @HealthCheck()
  async check() {
    const healthChecks = [
      async () => this.healthIndicator.checkDB(),
      () => this.healthIndicator.checkRabbitMQ(),
    ];

    return this.health.check(healthChecks);
  }
}
