import { Body, Controller, Param, Post, UseGuards } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

import { CreateAccountCommand } from '@/application/account/commands/create-account';
import { LinkGatewayAccountCommand } from '@/application/account/commands/link-gateway-account';
import { Account } from '@/domain/account/account';
import {
  MarketplaceApiKeyGuard,
  ReqMarketplace,
  RequestMarketplace,
} from '@/infrastructure/marketplace';

import {
  CreateAccountRequestDto,
  CreateAccountResponseDto,
  CreateGatewayAccountRequestDto,
} from './dto';

@Controller()
export class AccountController {
  constructor(private readonly commandBus: CommandBus) {}

  @UseGuards(MarketplaceApiKeyGuard)
  @Post('accounts')
  async create(
    @Body() dto: CreateAccountRequestDto,
    @ReqMarketplace() marketplace: RequestMarketplace,
  ) {
    const account = await this.commandBus.execute<CreateAccountCommand, Account>(
      new CreateAccountCommand({
        name: dto.name,
        email: dto.email,
        marketplaceId: marketplace.id,
      }),
    );

    return new CreateAccountResponseDto(account);
  }

  @UseGuards(MarketplaceApiKeyGuard)
  @Post('accounts/:accountId/gateway-accounts')
  async createGatewayAccount(
    @Body() dto: CreateGatewayAccountRequestDto,
    @ReqMarketplace() marketplace: RequestMarketplace,
    @Param('accountId') accountId: string,
  ) {
    const account = await this.commandBus.execute<LinkGatewayAccountCommand, Account>(
      new LinkGatewayAccountCommand({
        accountId,
        gatewayAccountId: dto.idAtGateway,
        marketplaceId: marketplace.id,
        gatewayType: dto.gatewayType,
      }),
    );

    return account;
  }
}
