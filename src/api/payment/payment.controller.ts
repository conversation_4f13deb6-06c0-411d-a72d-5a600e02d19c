import { Controller } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { RabbitSubscribe } from '@sw-web/nestjs-core/rabbitmq';

import { ProcessPaymentSplitsCommand } from '@/application/payment-split/commands/process-payment-splits';
import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { PaymentSettledIntegrationEvent } from '@/domain/payment/integration-events';

const { payment: paymentExchange } = RABBIT_MQ_EXCHANGES;

const { paymentSettled: paymentSettledRoutingKey } = paymentExchange.routingKeys;

@Controller('payments')
export class PaymentController {
  constructor(private readonly commandBus: CommandBus) {}

  @RabbitSubscribe({
    exchange: paymentExchange.name,
    routingKey: paymentSettledRoutingKey.name,
    queue: paymentSettledRoutingKey.queues.processPaymentSplits.name,
  })
  async processPaymentSplits(event: PaymentSettledIntegrationEvent) {
    await this.commandBus.execute(
      new ProcessPaymentSplitsCommand({ paymentId: event.aggregateId }),
    );
  }
}
