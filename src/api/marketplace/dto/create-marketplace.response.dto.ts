import { Marketplace } from '@/domain/marketplace/marketplace';

export class CreateMarketplaceResponseDto {
  id: string;
  name: string;
  webhookUrl: string;
  webhookSecret: string;
  secretKey: string;
  publishableKey: string;

  constructor(marketplace: Marketplace) {
    const props = marketplace.getProps();

    this.id = props.id;
    this.name = props.name;
    this.webhookSecret = props.webhookEndpoint.getWebhookSecret();
    this.webhookUrl = props.webhookEndpoint.getWebhookUrl();
    this.secretKey = props.apiKeyPair.getSecretKey();
    this.publishableKey = props.apiKeyPair.getPublishableKey();
  }
}
