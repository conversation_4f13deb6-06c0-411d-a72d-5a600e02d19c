import { Body, Controller, Post } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

import { CreateMarketplaceCommand } from '@/application/marketplace/commands/create-marketplace';
import { Marketplace } from '@/domain/marketplace/marketplace';

import { CreateMarketplaceRequestDto, CreateMarketplaceResponseDto } from './dto';

@Controller()
export class MarketplaceController {
  constructor(private readonly commandBus: CommandBus) {}

  @Post('marketplaces')
  async create(@Body() dto: CreateMarketplaceRequestDto) {
    const marketplace = await this.commandBus.execute<CreateMarketplaceCommand, Marketplace>(
      new CreateMarketplaceCommand({
        name: dto.name,
        webhookUrl: dto.webhookUrl,
      }),
    );

    return new CreateMarketplaceResponseDto(marketplace);
  }
}
