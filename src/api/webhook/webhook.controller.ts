import { Controller } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { RabbitSubscribe } from '@sw-web/nestjs-core/rabbitmq';

import { SendWebhookCommand } from '@/application/webhook/commands/send-webhook/send-webhook.command';
import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { WebhookScheduledIntegrationEvent } from '@/domain/webhook/integration-events';

const { webhookSender } = RABBIT_MQ_EXCHANGES;

const { webhookSend } = webhookSender.routingKeys;

@Controller()
export class WebhookController {
  constructor(private readonly commandBus: CommandBus) {}

  @RabbitSubscribe({
    exchange: webhookSender.name,
    routingKey: webhookSend.name,
    queue: webhookSend.queues.webhookSender.name,
  })
  async sendWebhook(event: WebhookScheduledIntegrationEvent) {
    await this.commandBus.execute(new SendWebhookCommand({ webhookId: event.aggregateId }));
  }
}
