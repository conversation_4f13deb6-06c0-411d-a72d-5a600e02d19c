import { FeeSettings } from '@/domain/fee-settings/fee-settings';

export class CreateFeeSettingsResponseDto {
  id: string;
  platformFeePercentage: number;
  platformFeeFixed: number;
  marketplaceFeePercentage: number;
  marketplaceFeeFixed: number;
  isDefault: boolean;

  constructor(feeSettings: FeeSettings) {
    const props = feeSettings.getProps();
    this.id = props.id;
    this.platformFeePercentage = props.platformFeePercentage;
    this.platformFeeFixed = props.platformFeeFixed.getProps().amount.toNumber();
    this.marketplaceFeePercentage = props.marketplaceFeePercentage;
    this.marketplaceFeeFixed = props.marketplaceFeeFixed.getProps().amount.toNumber();
    this.isDefault = props.isDefault;
  }
}
