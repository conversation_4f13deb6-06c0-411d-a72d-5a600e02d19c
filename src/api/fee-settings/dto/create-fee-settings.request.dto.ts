import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ot<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';

export class CreateFeeSettingsRequestDto {
  @IsNumber()
  @IsNotEmpty()
  @Min(0)
  @Max(1)
  platformFeePercentage: number;

  @IsNumber()
  @IsNotEmpty()
  platformFeeFixed: number;

  @IsNumber()
  @IsNotEmpty()
  @Min(0)
  @Max(1)
  marketplaceFeePercentage: number;

  @IsNumber()
  @IsNotEmpty()
  marketplaceFeeFixed: number;

  @IsBoolean()
  isDefault: boolean;
}
