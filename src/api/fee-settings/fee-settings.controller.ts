import { Body, Controller, Param, Post, UseGuards } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

import { CreateFeeSettingsCommand } from '@/application/fee-settings/commands/create-fee-settings';
import { FeeSettings } from '@/domain/fee-settings/fee-settings';
import { AllowedCurrencies } from '@/domain/shared';
import { MarketplaceApiKeyGuard } from '@/infrastructure/marketplace';

import { CreateFeeSettingsRequestDto, CreateFeeSettingsResponseDto } from './dto';

@Controller()
export class FeeSettingsController {
  constructor(private readonly commandBus: CommandBus) {}

  @UseGuards(MarketplaceApiKeyGuard)
  @Post('marketplace-gateways/:marketplaceGatewayId/fee-settings')
  async create(
    @Body() dto: CreateFeeSettingsRequestDto,
    @Param('marketplaceGatewayId') marketplaceGatewayId: string,
  ) {
    const feeSettings = await this.commandBus.execute<CreateFeeSettingsCommand, FeeSettings>(
      new CreateFeeSettingsCommand({
        platformFeePercentage: dto.platformFeePercentage,
        platformFeeFixed: dto.platformFeeFixed,
        marketplaceFeePercentage: dto.marketplaceFeePercentage,
        marketplaceFeeFixed: dto.marketplaceFeeFixed,
        isDefault: dto.isDefault,
        marketplaceGatewayId,
        currency: AllowedCurrencies.USD,
      }),
    );

    return new CreateFeeSettingsResponseDto(feeSettings);
  }
}
