import { Body, Controller, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';

import { CreatePaymentIntentCommand } from '@/application/payment-intent/commands/create-payment-intent';
import { UpdatePaymentIntentCommand } from '@/application/payment-intent/commands/update-payment-intent';
import { FindPaymentIntentByIdQuery } from '@/application/payment-intent/queries/find-payment-intent-by-id.query';
import { FindPaymentIntentByPaymentIdQuery } from '@/application/payment-intent/queries/find-payment-intent-by-payment-id.query';
import { PaymentIntent, PaymentIntentUpdateType } from '@/domain/payment-intent/payment-intent';
import {
  MarketplaceApiKeyGuard,
  MarketplacePublishableKeyGuard,
  ReqMarketplace,
  RequestMarketplace,
} from '@/infrastructure/marketplace';

import {
  ClientPaymentIntentResponseDto,
  CreatePaymentIntentRequestDto,
  UpdatePaymentIntentRequestDto,
} from './dto';

@Controller()
export class PaymentIntentController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {}

  @UseGuards(MarketplaceApiKeyGuard)
  @Post('payment-intents')
  async create(
    @Body() dto: CreatePaymentIntentRequestDto,
    @ReqMarketplace() marketplace: RequestMarketplace,
  ) {
    const paymentIntent = await this.commandBus.execute<CreatePaymentIntentCommand, PaymentIntent>(
      new CreatePaymentIntentCommand({
        marketplaceId: marketplace.id,
        gatewayType: dto.gatewayType,
        amount: dto.amount,
        currency: dto.currency,
        customerId: dto.customerId,
        paymentMethodType: dto.paymentMethodType,
        paymentSplitsData: dto.paymentSplits,
        businessEntityId: dto.businessEntityId,
        metadata: dto.metadata,
        statementDescriptor: dto.statementDescriptor,
        marketplaceOrderFee: dto.marketplaceOrderFee,
        platformOrderFee: dto.platformOrderFee,
      }),
    );

    return new ClientPaymentIntentResponseDto(paymentIntent);
  }

  @UseGuards(MarketplaceApiKeyGuard)
  @Put('payment-intents/:paymentIntentId')
  async update(
    @Body() dto: UpdatePaymentIntentRequestDto,
    @Param('paymentIntentId') paymentIntentId: string,
    @ReqMarketplace() marketplace: RequestMarketplace,
  ) {
    const paymentIntent = await this.commandBus.execute<UpdatePaymentIntentCommand, PaymentIntent>(
      new UpdatePaymentIntentCommand({
        paymentIntentId,
        paymentMethodType: dto.paymentMethodType,
        businessEntityId: dto.businessEntityId,
        marketplaceId: marketplace.id,
        metadata: dto.metadata,
        statementDescriptor: dto.statementDescriptor,
        ...(dto.amount
          ? {
              updateType: PaymentIntentUpdateType.WITH_AMOUNT,
              amount: dto.amount,
              currency: dto.currency,
              paymentSplitsData: dto.paymentSplits,
              platformOrderFee: dto.platformOrderFee,
              marketplaceOrderFee: dto.marketplaceOrderFee,
            }
          : {
              updateType: PaymentIntentUpdateType.WITHOUT_AMOUNT,
            }),
      }),
    );

    return new ClientPaymentIntentResponseDto(paymentIntent);
  }

  @UseGuards(MarketplacePublishableKeyGuard)
  @Get('client/payment-intents/:paymentIntentId')
  async findClientPaymentIntent(
    @Param('paymentIntentId') paymentIntentId: string,
    @ReqMarketplace() marketplace: RequestMarketplace,
  ) {
    const paymentIntent = await this.queryBus.execute<FindPaymentIntentByIdQuery, PaymentIntent>(
      new FindPaymentIntentByIdQuery({
        paymentIntentId,
        marketplaceId: marketplace.id,
      }),
    );

    return new ClientPaymentIntentResponseDto(paymentIntent);
  }

  @UseGuards(MarketplaceApiKeyGuard)
  @Get('payments/:paymentId/payment-intent')
  async findPaymentIntentByPaymentId(
    @Param('paymentId') paymentId: string,
    @ReqMarketplace() marketplace: RequestMarketplace,
  ) {
    return this.queryBus.execute<FindPaymentIntentByPaymentIdQuery, ClientPaymentIntentResponseDto>(
      new FindPaymentIntentByPaymentIdQuery({
        paymentId,
        marketplaceId: marketplace.id,
      }),
    );
  }
}
