import { StripeConfig } from '@/config/stripe.config';
import { GatewayPaymentIntent, PaymentIntent } from '@/domain/payment-intent/payment-intent';
import { StripePaymentIntent } from '@/domain/payment-intent/stripe-payment-intent';
import { GatewayType } from '@/domain/shared';

export interface StripePaymentIntentResponseDto {
  stripePaymentIntentSecret: string;
  stripePaymentIntentId: string;
  stripePublishableKey: string;
}

export class ClientPaymentIntentResponseDto {
  paymentIntentId: string;
  amount: number;
  currency: string;
  paymentMethodType: string;
  businessEntityId: string;
  gatewayType: GatewayType;
  gatewayPaymentIntent: StripePaymentIntentResponseDto;

  constructor(paymentIntent: PaymentIntent) {
    const { id, amount, paymentMethodType, businessEntityId, gateway, gatewayPaymentIntent } =
      paymentIntent.getProps();
    const intentAmount = amount.toObject();

    this.paymentIntentId = id;
    this.amount = intentAmount.amount;
    this.currency = intentAmount.currency;
    this.paymentMethodType = paymentMethodType;
    this.businessEntityId = businessEntityId;
    this.gatewayType = gateway.getType();
    this.gatewayPaymentIntent = this.mapGatewayPaymentIntent(gatewayPaymentIntent);
  }

  private mapGatewayPaymentIntent(
    gatewayPaymentIntent: GatewayPaymentIntent,
  ): StripePaymentIntentResponseDto {
    if (gatewayPaymentIntent instanceof StripePaymentIntent) {
      const props = gatewayPaymentIntent.getProps();
      return {
        stripePaymentIntentId: props.idAtGateway,
        stripePaymentIntentSecret: props.stripeClientSecret,
        stripePublishableKey: StripeConfig.publicKey,
      };
    }

    throw new Error('Invalid gateway intent');
  }
}
