import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsPositive,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { AllowedCurrencies, GatewayMetadata, PaymentMethodType } from '@/domain/shared';

class PaymentSplitDataDto {
  @IsPositive()
  amount: number;

  @IsNotEmpty()
  @IsString()
  accountId: string;
}

export class UpdatePaymentIntentRequestDto {
  @IsOptional()
  @IsString()
  businessEntityId: string | null;

  @IsPositive()
  @IsOptional()
  amount?: number;

  @IsNumber()
  @ValidateIf((object) => !!object.amount)
  platformOrderFee?: number;

  @IsNumber()
  @ValidateIf((object) => !!object.amount)
  marketplaceOrderFee?: number;

  @IsEnum(AllowedCurrencies)
  @ValidateIf((object) => !!object.amount)
  currency?: AllowedCurrencies;

  @IsEnum(PaymentMethodType)
  @IsOptional()
  paymentMethodType?: PaymentMethodType;

  @ValidateNested({ each: true })
  @ValidateIf((object) => !!object.amount)
  @Type(() => PaymentSplitDataDto)
  @ArrayMinSize(1)
  paymentSplits?: PaymentSplitDataDto[];

  @IsObject()
  @IsOptional()
  metadata?: GatewayMetadata | null;

  @IsString()
  @IsOptional()
  statementDescriptor?: string | null;
}
