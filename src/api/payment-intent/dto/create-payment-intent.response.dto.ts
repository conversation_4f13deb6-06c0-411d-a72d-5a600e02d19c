import { PaymentIntent } from '../../../domain/payment-intent/payment-intent';

export class CreatePaymentIntentResponseDto {
  paymentIntentId: string;
  amount: number;
  currency: string;
  paymentMethodType: string;

  constructor(paymentIntent: PaymentIntent) {
    const { id, amount, paymentMethodType } = paymentIntent.getProps();
    const intentAmount = amount.toObject();

    this.paymentIntentId = id;
    this.amount = intentAmount.amount;
    this.currency = intentAmount.currency;
    this.paymentMethodType = paymentMethodType;
  }
}
