import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

import { CreateCustomerCommand } from '@/application/customer/commands/create-customer';
import { Customer } from '@/domain/customer/customer';
import {
  MarketplaceApiKeyGuard,
  ReqMarketplace,
  RequestMarketplace,
} from '@/infrastructure/marketplace';

import { CreateCustomerRequestDto, CreateCustomerResponseDto } from './dto';

@Controller()
export class CustomerController {
  constructor(private readonly commandBus: CommandBus) {}

  @UseGuards(MarketplaceApiKeyGuard)
  @Post('customers')
  async create(
    @Body() dto: CreateCustomerRequestDto,
    @ReqMarketplace() marketplace: RequestMarketplace,
  ) {
    const customer = await this.commandBus.execute<CreateCustomerCommand, Customer>(
      new CreateCustomerCommand({
        name: dto.name,
        email: dto.email,
        marketplaceId: marketplace.id,
      }),
    );

    return new CreateCustomerResponseDto(customer);
  }
}
