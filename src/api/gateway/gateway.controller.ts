import { Controller, Get, Post, Query, RawBodyRequest, Req } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { RabbitSubscribe } from '@sw-web/nestjs-core/rabbitmq';
import Stripe from 'stripe';

import { RetrieveFingerprintCommand } from '@/application/gateway/commands/retrieve-fingerprint';
import { StripeWebhookService } from '@/application/gateway/services/stripe-webhook';
import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { GatewayType } from '@/domain/shared';

import { RetrieveFingerprintRequestDto } from './dto/retrieve-fingerprint.request.dto';

const { gatewayWebhook: gatewayWebhookExchange } = RABBIT_MQ_EXCHANGES;

const { stripe: stripeRoutingKey } = gatewayWebhookExchange.routingKeys;

@Controller()
export class GatewayController {
  constructor(
    private readonly stripeWebhookService: StripeWebhookService,
    private readonly commandBus: CommandBus,
  ) {}

  @Post('gateways/stripe/webhook')
  async create(@Req() req: RawBodyRequest<Request>) {
    await this.stripeWebhookService.validateAndEnqueueWebhook(req.rawBody, req);
  }

  @RabbitSubscribe({
    exchange: gatewayWebhookExchange.name,
    routingKey: stripeRoutingKey.name,
    queue: stripeRoutingKey.queues.stripeWebhook.name,
  })
  async processWebhook(event: Stripe.Event) {
    await this.stripeWebhookService.processWebhook(event);
  }

  @Get('gateways/stripe/retrieve-fingerprint')
  async retrieveFingerprint(@Query() dto: RetrieveFingerprintRequestDto) {
    return this.commandBus.execute<RetrieveFingerprintCommand, { fingerprint: string | null }>(
      new RetrieveFingerprintCommand({
        paymentMethodIdAtGateway: dto.paymentMethodIdAtGateway,
        gatewayType: GatewayType.STRIPE,
      }),
    );
  }
}
