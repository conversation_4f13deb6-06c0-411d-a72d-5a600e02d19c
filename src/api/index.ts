import { AccountController } from './account';
import { CustomerController } from './customer';
import { FeeSettingsController } from './fee-settings';
import { GatewayController } from './gateway';
import { HealthController } from './health';
import { MarketplaceController } from './marketplace';
import { PaymentController } from './payment';
import { PaymentIntentController } from './payment-intent';
import { PayoutController } from './payout';
import { RefundController } from './refund';
import { WebhookController } from './webhook/webhook.controller';

export const CONTROLLERS = [
  HealthController,
  PaymentIntentController,
  GatewayController,
  MarketplaceController,
  CustomerController,
  AccountController,
  WebhookController,
  PaymentController,
  RefundController,
  PayoutController,
  FeeSettingsController,
];
