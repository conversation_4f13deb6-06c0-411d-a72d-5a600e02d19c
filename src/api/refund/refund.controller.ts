import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { RabbitSubscribe } from '@sw-web/nestjs-core/rabbitmq';

import { CreateRefundCommand } from '@/application/payment/commands/create-refund';
import { ProcessRefundFailureCommand } from '@/application/payment/commands/process-refund-failure';
import { ProcessDisputePaymentSplitRefundsCommand } from '@/application/payment-split/commands/process-dispute-payment-split-refunds';
import { ProcessPaymentSplitRefundsCommand } from '@/application/payment-split/commands/process-payment-split-refunds';
import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import {
  RefundFailedIntegrationEvent,
  RefundIssuedIntegrationEvent,
} from '@/domain/payment/integration-events';
import { DisputeRefundIssuedIntegrationEvent } from '@/domain/payment/integration-events/dispute-refund-issued.integration-event';
import { Refund } from '@/domain/payment/refund';
import { MarketplaceApiKeyGuard } from '@/infrastructure/marketplace';

import { CreateRefundRequestDto, CreateRefundResponseDto } from './dto';

const { payment: paymentExchange } = RABBIT_MQ_EXCHANGES;

const { refundIssued: refundIssuedRoutingKey, refundFailed: refundFailedRoutingKey } =
  paymentExchange.routingKeys;

@Controller()
export class RefundController {
  constructor(private readonly commandBus: CommandBus) {}

  @RabbitSubscribe({
    exchange: paymentExchange.name,
    routingKey: refundIssuedRoutingKey.name,
    queue: refundIssuedRoutingKey.queues.processDisputePaymentSplitRefunds.name,
  })
  async processDisputePaymentSplitRefunds(event: DisputeRefundIssuedIntegrationEvent) {
    await this.commandBus.execute(
      new ProcessDisputePaymentSplitRefundsCommand({
        disputeId: event.aggregateId,
      }),
    );
  }

  @RabbitSubscribe({
    exchange: paymentExchange.name,
    routingKey: refundIssuedRoutingKey.name,
    queue: refundIssuedRoutingKey.queues.processPaymentSplitRefunds.name,
  })
  async processPaymentSplitRefunds(event: RefundIssuedIntegrationEvent) {
    await this.commandBus.execute(
      new ProcessPaymentSplitRefundsCommand({
        refundId: event.refundId,
        paymentId: event.aggregateId,
      }),
    );
  }

  @RabbitSubscribe({
    exchange: paymentExchange.name,
    routingKey: refundFailedRoutingKey.name,
    queue: refundFailedRoutingKey.queues.processRefundFailure.name,
  })
  async processRefundFailure(event: RefundFailedIntegrationEvent) {
    await this.commandBus.execute(
      new ProcessRefundFailureCommand({ refundId: event.refundId, paymentId: event.aggregateId }),
    );
  }

  @UseGuards(MarketplaceApiKeyGuard)
  @Post('refunds')
  async create(@Body() dto: CreateRefundRequestDto) {
    const refund = await this.commandBus.execute<CreateRefundCommand, Refund>(
      new CreateRefundCommand({
        paymentId: dto.paymentId,
        amount: dto.amount,
        refundSplits: dto.refundSplits,
        currency: dto.currency,
        metadata: dto.metadata,
        marketplaceOrderFee: dto.marketplaceOrderFee,
        platformOrderFee: dto.platformOrderFee,
      }),
    );

    return new CreateRefundResponseDto(refund);
  }
}
