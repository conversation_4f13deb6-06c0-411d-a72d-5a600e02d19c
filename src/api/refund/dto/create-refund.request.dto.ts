import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsPositive,
  IsString,
  ValidateNested,
} from 'class-validator';

import { AllowedCurrencies, GatewayMetadata } from '@/domain/shared';

class RefundSplitDataDto {
  @IsPositive()
  amount: number;

  @IsString()
  @IsNotEmpty()
  accountId: string;
}

export class CreateRefundRequestDto {
  @IsString()
  @IsNotEmpty()
  paymentId: string;

  @IsPositive()
  amount: number;

  @IsNumber()
  marketplaceOrderFee: number;

  @IsNumber()
  platformOrderFee: number;

  @IsEnum(AllowedCurrencies)
  currency: AllowedCurrencies;

  @ValidateNested({ each: true })
  @ArrayMinSize(0)
  @Type(() => RefundSplitDataDto)
  refundSplits: RefundSplitDataDto[];

  @IsObject()
  @IsOptional()
  metadata?: GatewayMetadata | null;
}
