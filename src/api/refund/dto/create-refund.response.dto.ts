import { Refund } from '@/domain/payment/refund';

export class CreateRefundResponseDto {
  id: string;
  amount: number;
  currency: string;
  status: string;
  isManual: boolean;
  createdAt: Date;
  updatedAt: Date;

  constructor(refund: Refund) {
    const props = refund.getProps();
    const { amount, currency } = props.amount.toObject();

    this.id = props.id;
    this.amount = amount;
    this.currency = currency;
    this.status = props.status;
    this.isManual = props.isManual;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
  }
}
