import { CreateAccount<PERSON>ommandHandler } from './commands/create-account';
import { LinkGatewayAccountCommandHandler } from './commands/link-gateway-account';
import { AccountApplicationService } from './services';

export const ACCOUNT_COMMAND_HANDLERS = [
  Create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  LinkGatewayAccountCommandHandler,
];

export const ACCOUNT_APPLICATION_SERVICES = [AccountApplicationService];