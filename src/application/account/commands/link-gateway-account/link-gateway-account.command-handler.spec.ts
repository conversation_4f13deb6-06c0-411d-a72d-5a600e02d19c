import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import { Account } from '@/domain/account/account';
import { ACCOUNT_REPOSITORY_TOKEN, GATEWAY_REPOSITORY_TOKEN, GATEWAY_SERVICE_FACTORY_TOKEN } from '@/domain/di-tokens';
import { Gateway as GatewayEntity } from '@/domain/gateway/gateway';
import { Gateway, GatewayType } from '@/domain/shared';
import { AccountRepositoryMock } from '@/test/mocks/repositories/account-repository.mock';
import { GatewayRepositoryMock } from '@/test/mocks/repositories/gateway-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import {
  GatewayAccountMock,
  GatewayServiceFactoryMock,
  MockGatewayServiceAdapter,
} from '@/test/mocks/services/gateway-service-factory.mock';

import { LinkGatewayAccountCommand, LinkGatewayAccountCommandHandler } from '.';

describe('LinkGatewayAccountCommandHandler', () => {
  let handler: LinkGatewayAccountCommandHandler;
  let db: DatabaseServiceMock;
  let gatewayRepository: GatewayRepositoryMock;
  let accountRepository: AccountRepositoryMock;
  let gatewayServiceFactory: GatewayServiceFactoryMock;
  let account: Account;
  let commandProps: LinkGatewayAccountCommand['props'];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LinkGatewayAccountCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: GATEWAY_REPOSITORY_TOKEN, useClass: GatewayRepositoryMock },
        { provide: ACCOUNT_REPOSITORY_TOKEN, useClass: AccountRepositoryMock },
        { provide: GATEWAY_SERVICE_FACTORY_TOKEN, useClass: GatewayServiceFactoryMock },
      ],
    }).compile();

    handler = module.get<LinkGatewayAccountCommandHandler>(LinkGatewayAccountCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    gatewayRepository = module.get<GatewayRepositoryMock>(GATEWAY_REPOSITORY_TOKEN);
    accountRepository = module.get<AccountRepositoryMock>(ACCOUNT_REPOSITORY_TOKEN);
    gatewayServiceFactory = module.get<GatewayServiceFactoryMock>(GATEWAY_SERVICE_FACTORY_TOKEN);
    [account] = accountRepository.db;
    commandProps = {
      accountId: account.getId(),
      gatewayType: GatewayType.STRIPE,
      gatewayAccountId: `${account.getId()}-${GatewayType.STRIPE}`,
      marketplaceId: undefined,
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if account is not found', async () => {
    const accountId = 'non-existing-account-id';
    const command = new LinkGatewayAccountCommand({ ...commandProps, accountId });
    const findAccountSpy = jest.spyOn(accountRepository, 'findById');

    await expect(handler.execute(command)).rejects.toThrow(new Error('Account not found'));
    expect(findAccountSpy).toHaveBeenCalledWith(accountId);
  });

  it('should fail if gateway is not found', async () => {
    const gatewayType = 'non-existing-gateway-type' as GatewayType;
    const command = new LinkGatewayAccountCommand({ ...commandProps, gatewayType });
    const findByGatewayTypeSpy = jest.spyOn(gatewayRepository, 'findByGatewayType');

    await expect(handler.execute(command)).rejects.toThrow(new Error('Gateway not found'));
    expect(findByGatewayTypeSpy).toHaveBeenCalledWith(gatewayType);
  });

  it('should fail if gateway account does not exist', async () => {
    const command = new LinkGatewayAccountCommand({
      ...commandProps, gatewayAccountId: 'non-existing-gateway-account-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Account does not exist'));
  });

  it('should create a Gateway with proper id and type', async () => {
    const command = new LinkGatewayAccountCommand(commandProps);
    const findByGatewayTypeSpy = jest.spyOn(gatewayRepository, 'findByGatewayType');
    const getGatewaySpy = jest.spyOn(gatewayServiceFactory, 'getGateway');

    await handler.execute(command);

    expect(findByGatewayTypeSpy).toHaveBeenCalledWith(commandProps.gatewayType);
    const gatewayEntity = await findByGatewayTypeSpy.mock.results[0].value as GatewayEntity;
    expect(gatewayEntity).toBeInstanceOf(GatewayEntity);
    const gateway = getGatewaySpy.mock.calls[0][0];
    expect(gateway).toBeInstanceOf(Gateway);
    expect(gateway.getGatewayId()).toEqual(gatewayEntity.getId());
    expect(gateway.getType()).toEqual(gatewayEntity.getProps().type);
  });

  it('should link gateway account to account in transaction', async () => {
    const command = new LinkGatewayAccountCommand(commandProps);
    const getGatewaySpy = jest.spyOn(gatewayServiceFactory, 'getGateway');
    const getGatewayAccountSpy = jest.spyOn(MockGatewayServiceAdapter.prototype, 'getAccount');
    const linkedGatewayAccountId = 'linked-gateway-account-id';
    account.linkGatewayAccount = jest.fn().mockReturnValue(linkedGatewayAccountId);
    accountRepository.update = jest.fn();

    await handler.execute(command);

    expect(getGatewaySpy).toHaveBeenCalled();
    expect(getGatewayAccountSpy).toHaveBeenCalled();
    expect(account.linkGatewayAccount).not.toHaveBeenCalled();
    expect(accountRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    const result = await db.invokeNextTransaction();

    const gateway = getGatewaySpy.mock.calls[0][0];
    const gatewayAccount = await getGatewayAccountSpy.mock.results[0].value as GatewayAccountMock;
    expect(account.linkGatewayAccount).toHaveBeenCalledWith(gatewayAccount.id, gateway);
    expect(accountRepository.update).toHaveBeenCalledWith(account);
    expect(result).toEqual({ gatewayAccountId: linkedGatewayAccountId, accountId: account.getId() });
  });
});
