import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { AccountRepositoryPort } from '@/domain/account/ports';
import {
  ACCOUNT_REPOSITORY_TOKEN,
  GATEWAY_REPOSITORY_TOKEN,
  GATEWAY_SERVICE_FACTORY_TOKEN,
} from '@/domain/di-tokens';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { Gateway, GatewayServiceFactoryPort, GatewayType } from '@/domain/shared';

import { LinkGatewayAccountCommand } from './link-gateway-account.command';

@CommandHandler(LinkGatewayAccountCommand)
export class LinkGatewayAccountCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(GATEWAY_REPOSITORY_TOKEN)
    private readonly gatewayRepository: GatewayRepositoryPort,
    @Inject(ACCOUNT_REPOSITORY_TOKEN)
    private readonly accountRepository: AccountRepositoryPort,
    @Inject(GATEWAY_SERVICE_FACTORY_TOKEN)
    private readonly gatewayServiceFactory: GatewayServiceFactoryPort,
  ) {}

  async execute(
    command: LinkGatewayAccountCommand,
  ): Promise<{ gatewayAccountId: string; accountId: string }> {
    const { gatewayAccountId, gatewayType, accountId } = command.getProps();

    const account = await this.accountRepository.findById(accountId);

    if (!account) {
      throw new Error('Account not found'); // todo: custom error
    }

    const gateway = await this.getGateway(gatewayType);

    const gatewayAccount = await this.getGatewayAccount(gateway, gatewayAccountId);

    return this.db.$transaction(async () => {
      const linkedGatewayAccountId = account.linkGatewayAccount(gatewayAccount.id, gateway);

      await this.accountRepository.update(account);

      return {
        gatewayAccountId: linkedGatewayAccountId,
        accountId: account.getId(),
      };
    });
  }

  private async getGatewayAccount(gateway: Gateway, gatewayAccountId: string) {
    const gatewayService = this.gatewayServiceFactory.getGateway(gateway);

    return gatewayService.getAccount(gatewayAccountId).catch(() => {
      throw new Error('Account does not exist');
    });
  }

  private async getGateway(gatewayType: GatewayType) {
    const gateway = await this.gatewayRepository.findByGatewayType(gatewayType);

    if (!gateway) {
      throw new Error('Gateway not found'); // todo: custom error
    }

    return new Gateway(gateway.getId(), gateway.getProps().type);
  }
}
