import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { Account } from '@/domain/account/account';
import { AccountRepositoryPort } from '@/domain/account/ports';
import { MARKETPLACE_REPOSITORY_TOKEN, ACCOUNT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { MarketplaceRepositoryPort } from '@/domain/marketplace/ports';
import { AllowedCurrencies, Currency } from '@/domain/shared';

import { CreateAccountCommand } from './create-account.command';

@CommandHandler(CreateAccountCommand)
export class CreateAccountCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(MARKETPLACE_REPOSITORY_TOKEN)
    private readonly marketplaceRepository: MarketplaceRepositoryPort,
    @Inject(ACCOUNT_REPOSITORY_TOKEN)
    private readonly accountRepository: AccountRepositoryPort,
  ) {}

  async execute(command: CreateAccountCommand): Promise<Account> {
    const { marketplaceId, name, email } = command.getProps();

    const marketplace = await this.marketplaceRepository.findById(marketplaceId);

    if (!marketplace) {
      throw new Error('Entity not found'); // todo: custom error
    }

    return this.db.$transaction(async () => {
      const account = Account.create({
        name,
        marketplaceId,
        currency: new Currency(AllowedCurrencies.USD),
        email,
      });

      await this.accountRepository.create(account);

      return account;
    });
  }
}
