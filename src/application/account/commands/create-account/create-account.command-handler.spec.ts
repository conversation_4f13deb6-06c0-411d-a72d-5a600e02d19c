import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import { Account } from '@/domain/account/account';
import { MARKETPLACE_REPOSITORY_TOKEN, ACCOUNT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Currency } from '@/domain/shared';
import { AccountRepositoryMock } from '@/test/mocks/repositories/account-repository.mock';
import { MarketplaceRepositoryMock } from '@/test/mocks/repositories/marketplace-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

import { CreateAccountCommand, CreateAccountCommandHandler } from '.';

describe('CreateAccountCommandHandler', () => {
  let handler: CreateAccountCommandHandler;
  let db: DatabaseServiceMock;
  let marketplaceRepository: MarketplaceRepositoryMock;
  let accountRepository: AccountRepositoryMock;
  let commandProps: CreateAccountCommand['props'];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateAccountCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: MARKETPLACE_REPOSITORY_TOKEN, useClass: MarketplaceRepositoryMock },
        { provide: ACCOUNT_REPOSITORY_TOKEN, useClass: AccountRepositoryMock },
      ],
    }).compile();

    handler = module.get<CreateAccountCommandHandler>(CreateAccountCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    marketplaceRepository = module.get<MarketplaceRepositoryMock>(MARKETPLACE_REPOSITORY_TOKEN);
    accountRepository = module.get<AccountRepositoryMock>(ACCOUNT_REPOSITORY_TOKEN);
    commandProps = {
      marketplaceId: marketplaceRepository.db[0].getId(),
      name: 'Test Account',
      email: '<EMAIL>',
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if marketplace is not found', async () => {
    const marketplaceId = 'missing-id';
    const command = new CreateAccountCommand({ ...commandProps, marketplaceId });
    const findMarketplaceSpy = jest.spyOn(marketplaceRepository, 'findById');

    await expect(handler.execute(command)).rejects.toThrow(new Error('Entity not found'));
    expect(findMarketplaceSpy).toHaveBeenCalledWith(marketplaceId);
  });

  it('should create an account within transaction', async () => {
    const command = new CreateAccountCommand(commandProps);
    const [account] = accountRepository.db;
    const accountCreateSpy = jest.spyOn(Account, 'create').mockReturnValue(account);
    accountRepository.create = jest.fn();

    await handler.execute(command);
    expect(accountCreateSpy).not.toHaveBeenCalled();
    expect(accountRepository.create).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    const result = await db.invokeNextTransaction();

    expect(accountCreateSpy).toHaveBeenCalledWith({
      ...command.getProps(),
      currency: expect.any(Currency),
    });
    expect(accountRepository.create).toHaveBeenCalledWith(account);
    expect(result).toBe(account);
  });
});
