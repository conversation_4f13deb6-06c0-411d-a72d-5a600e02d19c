import { Inject, Injectable } from '@nestjs/common';

import { AccountRepositoryPort } from '@/domain/account/ports';
import { ACCOUNT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Gateway } from '@/domain/shared';

@Injectable()
export class AccountApplicationService {
  constructor(
    @Inject(ACCOUNT_REPOSITORY_TOKEN) private readonly accountRepository: AccountRepositoryPort,
  ) {}

  async getGatewayAccount(accountId: string, gateway: Gateway) {
    const receivingAccount = await this.accountRepository.findById(accountId);

    if (!receivingAccount) {
      throw new Error('Account not found'); // todo: custom error
    }

    const gatewayAccount = receivingAccount
      .getProps()
      .gatewayAccounts.find((x) => x.getProps().gateway.equals(gateway));

    if (!gatewayAccount) {
      throw new Error('Gateway account not found'); // todo: custom error
    }

    return gatewayAccount;
  }
}
