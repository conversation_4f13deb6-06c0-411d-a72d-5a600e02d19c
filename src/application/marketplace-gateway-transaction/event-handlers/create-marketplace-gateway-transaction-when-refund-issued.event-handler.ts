import { Inject } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';

import { CreateMarketplaceGatewayTransactionCommand } from '@/application/marketplace-gateway-transaction/commands/create-marketplace-gateway-transaction';
import { MARKETPLACE_GATEWAY_REPOSITORY_TOKEN, PAYMENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { MarketplaceGatewayRepositoryPort } from '@/domain/marketplace-gateway/ports';
import { RefundIssuedEvent } from '@/domain/payment/events';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { Refund } from '@/domain/payment/refund';

@EventsHandler(RefundIssuedEvent)
export class CreateMarketplaceGatewayTransactionWhenRefundIssuedEventHandler
  implements IEventHandler
{
  constructor(
    private readonly commandBus: CommandBus,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayRepository: MarketplaceGatewayRepositoryPort,
  ) {}

  async handle(event: RefundIssuedEvent) {
    const { aggregateId, refundId } = event.getProps();

    const payment = await this.paymentRepository.findById(aggregateId);

    const marketplaceGateway =
      await this.marketplaceGatewayRepository.findByMarketplaceAndGatewayId({
        marketplaceId: payment.getProps().marketplaceId,
        gatewayId: payment.getProps().gateway.getGatewayId(),
      });

    const refund = payment.getRefund(refundId);

    await this.commandBus.execute(
      new CreateMarketplaceGatewayTransactionCommand({
        amount: this.calculateNetMarketplacePaymentFeeRefunded(refund).negated(),
        marketplaceGatewayId: marketplaceGateway.getId(),
      }),
    );

    await this.commandBus.execute(
      new CreateMarketplaceGatewayTransactionCommand({
        amount: refund.getProps().marketplaceOrderFeeRefunded.negated(),
        marketplaceGatewayId: marketplaceGateway.getId(),
      }),
    );
  }

  calculateNetMarketplacePaymentFeeRefunded(refund: Refund) {
    const { marketplacePaymentFeeRefunded, platformPaymentFeeRefunded } = refund.getProps();

    return marketplacePaymentFeeRefunded.sub(platformPaymentFeeRefunded);
  }
}
