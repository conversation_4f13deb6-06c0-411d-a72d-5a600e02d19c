import { CommandBus } from '@nestjs/cqrs';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';

import { CreateMarketplaceGatewayTransactionCommand } from '@/application/marketplace-gateway-transaction/commands/create-marketplace-gateway-transaction';
import { DisputeFundsWithdrawnDomainEvent } from '@/domain/dispute/events';

@EventsHandler(DisputeFundsWithdrawnDomainEvent)
export class CreateMarketplaceGatewayTransactionWhenDisputeFundsWithdrawnEventHandler
  implements IEventHandler
{
  constructor(private readonly commandBus: CommandBus) {}

  async handle(event: DisputeFundsWithdrawnDomainEvent) {
    const { amount, marketplaceGatewayId, relatedTransactionId } = event.getProps();

    await this.commandBus.execute(
      new CreateMarketplaceGatewayTransactionCommand({
        amount,
        marketplaceGatewayId,
        relatedTransactionId,
      }),
    );
  }
}
