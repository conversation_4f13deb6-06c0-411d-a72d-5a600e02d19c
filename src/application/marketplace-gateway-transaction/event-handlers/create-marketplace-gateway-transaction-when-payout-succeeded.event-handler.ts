import { Inject } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';

import { CreateMarketplaceGatewayTransactionCommand } from '@/application/marketplace-gateway-transaction/commands/create-marketplace-gateway-transaction';
import { PAYOUT_REPOSITORY_TOKEN, MARKETPLACE_GATEWAY_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { MarketplaceGatewayRepositoryPort } from '@/domain/marketplace-gateway/ports';
import { PayoutSucceededDomainEvent } from '@/domain/payout/events';
import { PayoutRepositoryPort } from '@/domain/payout/ports';

@EventsHandler(PayoutSucceededDomainEvent)
export class CreateMarketplaceGatewayTransactionWhenPayoutSucceededEventHandler
  implements IEventHandler
{
  constructor(
    private readonly commandBus: CommandBus,
    @Inject(PAYOUT_REPOSITORY_TOKEN)
    private readonly payoutRepository: PayoutRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayRepository: MarketplaceGatewayRepositoryPort,
  ) {}

  async handle(event: PayoutSucceededDomainEvent) {
    const { aggregateId } = event.getProps();

    const payout = await this.payoutRepository.findById(aggregateId);

    const marketplaceGateway =
      await this.marketplaceGatewayRepository.findByMarketplaceAndGatewayId({
        marketplaceId: payout.getProps().marketplaceId,
        gatewayId: payout.getProps().gateway.getGatewayId(),
      });

    await this.commandBus.execute(
      new CreateMarketplaceGatewayTransactionCommand({
        amount: payout.getProps().amount.negated(),
        marketplaceGatewayId: marketplaceGateway.getId(),
      }),
    );
  }
}
