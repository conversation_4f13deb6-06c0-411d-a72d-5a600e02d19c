import { Inject } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';

import { CreateMarketplaceGatewayTransactionCommand } from '@/application/marketplace-gateway-transaction/commands/create-marketplace-gateway-transaction';
import { MARKETPLACE_GATEWAY_REPOSITORY_TOKEN, PAYMENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { MarketplaceGatewayRepositoryPort } from '@/domain/marketplace-gateway/ports';
import { PaymentSettledEvent } from '@/domain/payment/events';
import { Payment } from '@/domain/payment/payment';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';

@EventsHandler(PaymentSettledEvent)
export class CreateMarketplaceGatewayTransactionWhenPaymentSettledEventHandler
  implements IEventHandler
{
  constructor(
    private readonly commandBus: CommandBus,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayRepository: MarketplaceGatewayRepositoryPort,
  ) {}

  async handle(event: PaymentSettledEvent) {
    const { aggregateId } = event.getProps();

    const payment = await this.paymentRepository.findById(aggregateId);

    const marketplaceGateway =
      await this.marketplaceGatewayRepository.findByMarketplaceAndGatewayId({
        marketplaceId: payment.getProps().marketplaceId,
        gatewayId: payment.getProps().gateway.getGatewayId(),
      });

    await this.commandBus.execute(
      new CreateMarketplaceGatewayTransactionCommand({
        amount: this.calculateNetMarketplacePaymentFee(payment),
        marketplaceGatewayId: marketplaceGateway.getId(),
      }),
    );

    await this.commandBus.execute(
      new CreateMarketplaceGatewayTransactionCommand({
        amount: payment.getProps().marketplaceOrderFee,
        marketplaceGatewayId: marketplaceGateway.getId(),
      }),
    );
  }

  calculateNetMarketplacePaymentFee(payment: Payment) {
    const { marketplacePaymentFee, platformPaymentFee } = payment.getProps();

    return marketplacePaymentFee.sub(platformPaymentFee);
  }
}
