import { Inject } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';

import { CreateMarketplaceGatewayTransactionCommand } from '@/application/marketplace-gateway-transaction/commands/create-marketplace-gateway-transaction';
import { MARKETPLACE_GATEWAY_REPOSITORY_TOKEN, PAYOUT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { MarketplaceGatewayRepositoryPort } from '@/domain/marketplace-gateway/ports';
import { PayoutReversalSucceededDomainEvent } from '@/domain/payout/events';
import { PayoutRepositoryPort } from '@/domain/payout/ports';

@EventsHandler(PayoutReversalSucceededDomainEvent)
export class CreateMarketplaceGatewayTransactionWhenPayoutReversalSucceededEventHandler
  implements IEventHandler
{
  constructor(
    private readonly commandBus: CommandBus,
    @Inject(PAYOUT_REPOSITORY_TOKEN)
    private readonly payoutRepository: PayoutRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayRepository: MarketplaceGatewayRepositoryPort,
  ) {}

  async handle(event: PayoutReversalSucceededDomainEvent) {
    const { aggregateId, reversalId } = event.getProps();

    const payout = await this.payoutRepository.findById(aggregateId);

    const marketplaceGateway =
      await this.marketplaceGatewayRepository.findByMarketplaceAndGatewayId({
        marketplaceId: payout.getProps().marketplaceId,
        gatewayId: payout.getProps().gateway.getGatewayId(),
      });

    const payoutReversal = payout.getReversal(reversalId);

    await this.commandBus.execute(
      new CreateMarketplaceGatewayTransactionCommand({
        amount: payoutReversal.getProps().amount,
        marketplaceGatewayId: marketplaceGateway.getId(),
      }),
    );
  }
}
