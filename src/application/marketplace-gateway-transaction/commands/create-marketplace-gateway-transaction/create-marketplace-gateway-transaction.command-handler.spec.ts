import { Test, TestingModule } from '@nestjs/testing';

import {
  CreateMarketplaceGatewayTransactionCommand,
  CreateMarketplaceGatewayTransactionCommandHandler
} from '@/application/marketplace-gateway-transaction/commands/create-marketplace-gateway-transaction';
import { DatabaseService } from '@/database';
import { MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import {
  MarketplaceGatewayTransaction
} from '@/domain/marketplace-gateway-transaction/marketplace-gateway-transaction';
import { Money, AllowedCurrencies, Currency } from '@/domain/shared';
import {
  MarketplaceGatewayTransactionRepositoryMock
} from '@/test/mocks/repositories/marketplace-gateway-transaction-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

describe('CreateMarketplaceGatewayTransactionCommandHandler', () => {
  let handler: CreateMarketplaceGatewayTransactionCommandHandler;
  let db: DatabaseServiceMock;
  let marketplaceGatewayTransactionRepository: MarketplaceGatewayTransactionRepositoryMock;
  let commandProps: CreateMarketplaceGatewayTransactionCommand['props'];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateMarketplaceGatewayTransactionCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        {
          provide: MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN,
          useClass: MarketplaceGatewayTransactionRepositoryMock
        },
      ],
    }).compile();

    handler = module.get<CreateMarketplaceGatewayTransactionCommandHandler>(CreateMarketplaceGatewayTransactionCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    marketplaceGatewayTransactionRepository =
      module.get<MarketplaceGatewayTransactionRepositoryMock>(MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN);
    commandProps = {
      amount: new Money(1, new Currency(AllowedCurrencies.USD)),
      marketplaceGatewayId: 'marketplace-gateway-id',
      relatedTransactionId: 'related-transaction-id',
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should create a marketplace gateway transaction', async () => {
    const command = new CreateMarketplaceGatewayTransactionCommand(commandProps);
    const [transaction] = marketplaceGatewayTransactionRepository.db;
    const createTransactionSpy = jest.spyOn(MarketplaceGatewayTransaction, 'create').mockReturnValue(transaction);
    marketplaceGatewayTransactionRepository.create = jest.fn();

    await handler.execute(command);

    expect(createTransactionSpy).not.toHaveBeenCalled();
    expect(marketplaceGatewayTransactionRepository.create).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    expect(createTransactionSpy).toHaveBeenCalledWith({
      amount: commandProps.amount,
      marketplaceGatewayId: commandProps.marketplaceGatewayId,
      transactionId: commandProps.relatedTransactionId,
    })
    expect(marketplaceGatewayTransactionRepository.create).toHaveBeenCalledWith(transaction);
  });
});
