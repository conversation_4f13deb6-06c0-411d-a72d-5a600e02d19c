import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import {
  MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import {
  MarketplaceGatewayTransaction
} from '@/domain/marketplace-gateway-transaction/marketplace-gateway-transaction';
import { MarketplaceGatewayTransactionRepositoryPort } from '@/domain/marketplace-gateway-transaction/ports';

import { CreateMarketplaceGatewayTransactionCommand } from './create-marketplace-gateway-transaction.command';

@CommandHandler(CreateMarketplaceGatewayTransactionCommand)
export class CreateMarketplaceGatewayTransactionCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayTransactionRepositoryPort:
      MarketplaceGatewayTransactionRepositoryPort,
  ) {}

  async execute(command: CreateMarketplaceGatewayTransactionCommand): Promise<void> {
    const {
      amount,
      marketplaceGatewayId,
      relatedTransactionId,
    } = command.getProps();

    await this.db.$transaction(async () => {
      const marketplaceGatewayTransaction = MarketplaceGatewayTransaction.create({
        amount,
        marketplaceGatewayId,
        transactionId: relatedTransactionId,
      });

      await this.marketplaceGatewayTransactionRepositoryPort.create(marketplaceGatewayTransaction);
    });
  }
}
