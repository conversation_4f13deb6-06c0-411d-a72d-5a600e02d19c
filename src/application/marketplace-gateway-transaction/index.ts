import { CreateMarketplaceGatewayTransaction<PERSON>ommandHandler } from './commands/create-marketplace-gateway-transaction';
import {
  CreateMarketplaceGatewayTransactionWhenDisputeFundsWithdrawnEventHandler,
  CreateMarketplaceGatewayTransactionWhenPaymentSettledEventHandler,
  CreateMarketplaceGatewayTransactionWhenPayoutReversalFailedEventHandler,
  CreateMarketplaceGatewayTransactionWhenPayoutReversalSucceededEventHandler,
  CreateMarketplaceGatewayTransactionWhenPayoutSucceededEventHandler,
  CreateMarketplaceGatewayTransactionWhenRefundIssuedEventHandler,
} from './event-handlers';

export const MARKETPLACE_GATEWAY_TRANSACTION_COMMAND_HANDLERS = [
  CreateMarketplaceGatewayTransactionCommandHandler,
];

export const MARKETPLACE_GATEWAY_TRANSACTION_EVENT_HANDLERS = [
  CreateMarketplaceGatewayTransactionWhenDisputeF<PERSON>Withdrawn<PERSON><PERSON><PERSON>and<PERSON>,
  CreateMarketplaceGatewayTransactionWhenPaymentSettledEventHandler,
  CreateMarketplaceGatewayTransactionWhenPayoutReversalSucceededEventHandler,
  CreateMarketplaceGatewayTransactionWhenPayoutSucceededEventHandler,
  CreateMarketplaceGatewayTransactionWhenPayoutReversalFailedEventHandler,
  CreateMarketplaceGatewayTransactionWhenRefundIssuedEventHandler,
];
