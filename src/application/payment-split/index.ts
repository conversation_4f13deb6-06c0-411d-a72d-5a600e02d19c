import { ProcessDisputePaymentSplitRefundsCommandHandler } from '@/application/payment-split/commands/process-dispute-payment-split-refunds';

import { CreateManualPaymentSplitRefundCommandHandler } from './commands/create-manual-payment-split-refund';
import { ProcessPaymentSplitRefundsCommandHandler } from './commands/process-payment-split-refunds';
import { ProcessPaymentSplitsCommandHandler } from './commands/process-payment-splits';

export const PAYMENT_SPLIT_COMMAND_HANDLERS = [
  ProcessPaymentSplitsCommandHandler,
  ProcessPaymentSplitRefunds<PERSON>ommandHandler,
  CreateManualPaymentSplitRefundCommandHandler,
];

export const PAYMENT_SPLIT_EVENT_HANDLERS = [ProcessDisputePaymentSplitRefundsCommandHandler];

export const PAYMENT_SPLIT_APPLICATION_SERVICES = [];
