import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';

import { AccountApplicationService } from '@/application/account/services';
import { AccountRepositoryPort } from '@/domain/account/ports';
import {
  ACCOUNT_REPOSITORY_TOKEN,
  GATEWAY_SERVICE_FACTORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { Payment } from '@/domain/payment/payment';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';
import { GatewayServiceFactoryPort, GatewayServicePort } from '@/domain/shared';
import { GatewayTransferSource, GatewayTransferStatus } from '@/domain/shared/interfaces';

import { ProcessPaymentSplitsCommand } from './process-payment-splits.command';

@CommandHandler(ProcessPaymentSplitsCommand)
export class ProcessPaymentSplitsCommandHandler implements ICommandHandler {
  constructor(
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(PAYMENT_SPLIT_REPOSITORY_TOKEN)
    private readonly paymentSplitRepository: PaymentSplitRepositoryPort,
    @Inject(ACCOUNT_REPOSITORY_TOKEN)
    private readonly accountRepository: AccountRepositoryPort,
    @Inject(GATEWAY_SERVICE_FACTORY_TOKEN)
    private readonly gatewayServiceFactory: GatewayServiceFactoryPort,
    private readonly accountApplicationService: AccountApplicationService,
  ) {}

  async execute(command: ProcessPaymentSplitsCommand): Promise<PaymentSplit[]> {
    const { paymentId } = command.getProps();

    const payment = await this.paymentRepository.findById(paymentId);

    if (!payment) {
      throw new Error('Payment not found'); // todo: custom error
    }

    const gatewayService = this.gatewayServiceFactory.getGateway(payment.getProps().gateway);

    const paymentSplits = await this.paymentSplitRepository.findByPaymentId(paymentId);

    await this.paymentSplitRepository.transaction(async () => Promise.all(
      paymentSplits.map(async (paymentSplit) => {
        await this.processPaymentSplit(payment, paymentSplit, gatewayService);
        await this.paymentSplitRepository.update(paymentSplit);
      })
    ));

    return paymentSplits;
  }

  private async processPaymentSplit(
    payment: Payment,
    paymentSplit: PaymentSplit,
    gatewayService: GatewayServicePort,
  ) {
    const { accountId, amount, gateway } = paymentSplit.getProps();

    const receivingGatewayAccount = await this.accountApplicationService.getGatewayAccount(
      accountId,
      gateway
    );

    const receivingGatewayAccountId = receivingGatewayAccount.getProps().idAtGateway;

    const gatewayTransfer = await gatewayService.createOutboundTransfer({
      amount,
      receivingGatewayAccountId,
      idempotencyKey: this.generateIdempotencyKey(paymentSplit),
      source: GatewayTransferSource.PAYMENT_SPLIT,
      sourceId: paymentSplit.getId(),
      paymentIntentId: payment.getProps().paymentIntentId,
    });

    if (gatewayTransfer.status === GatewayTransferStatus.SETTLED) {
      paymentSplit.markSettled();
    } else {
      paymentSplit.markProcessing();
    }
  }

  private generateIdempotencyKey(paymentSplit: PaymentSplit) {
    return paymentSplit.getId();
  }
}
