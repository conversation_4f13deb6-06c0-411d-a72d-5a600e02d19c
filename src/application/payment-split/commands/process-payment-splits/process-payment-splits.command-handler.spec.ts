import { Test, TestingModule } from '@nestjs/testing';

import { AccountApplicationService } from '@/application/account/services';
import { DatabaseService } from '@/database';
import { GatewayAccount } from '@/domain/account/gateway-account';
import {
  GATEWAY_SERVICE_FACTORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
  ACCOUNT_REPOSITORY_TOKEN
} from '@/domain/di-tokens';
import { Payment } from '@/domain/payment/payment';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import {
  GatewayTransfer,
  GatewayTransferSource,
  GatewayTransferStatus,
} from '@/domain/shared/interfaces';
import { AccountRepositoryMock } from '@/test/mocks/repositories/account-repository.mock';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { PaymentSplitRepositoryMock } from '@/test/mocks/repositories/payment-split-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import {
  GatewayServiceFactoryMock,
  MockGatewayServiceAdapter,
} from '@/test/mocks/services/gateway-service-factory.mock';

import { ProcessPaymentSplitsCommand, ProcessPaymentSplitsCommandHandler } from '.';

describe('ProcessPaymentSplitsCommandHandler', () => {
  let handler: ProcessPaymentSplitsCommandHandler;
  let db: DatabaseServiceMock;
  let paymentRepository: PaymentRepositoryMock;
  let paymentSplitRepository: PaymentSplitRepositoryMock;
  let gatewayServiceFactory: GatewayServiceFactoryMock;
  let accountApplicationService: AccountApplicationService;
  let commandProps: ProcessPaymentSplitsCommand['props'];
  let payment: Payment;
  let paymentSplits: PaymentSplit[];
  let markSettledSpy: jest.SpyInstance;
  let markProcessingSpy: jest.SpyInstance;
  let getGatewayAccountSpy: jest.SpyInstance;
  let createOutboundTransferSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessPaymentSplitsCommandHandler,
        AccountApplicationService,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: PAYMENT_SPLIT_REPOSITORY_TOKEN, useClass: PaymentSplitRepositoryMock },
        { provide: ACCOUNT_REPOSITORY_TOKEN, useClass: AccountRepositoryMock },
        { provide: GATEWAY_SERVICE_FACTORY_TOKEN, useClass: GatewayServiceFactoryMock },
      ],
    }).compile();

    handler = module.get<ProcessPaymentSplitsCommandHandler>(ProcessPaymentSplitsCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    paymentSplitRepository = module.get<PaymentSplitRepositoryMock>(PAYMENT_SPLIT_REPOSITORY_TOKEN);
    gatewayServiceFactory = module.get<GatewayServiceFactoryMock>(GATEWAY_SERVICE_FACTORY_TOKEN);
    accountApplicationService = module.get<AccountApplicationService>(AccountApplicationService);
    [payment] = paymentRepository.db;
    paymentSplits = await paymentSplitRepository.findByPaymentId(payment.getId());
    commandProps = {
      paymentId: payment.getId(),
    };
    markSettledSpy = jest.spyOn(PaymentSplit.prototype, 'markSettled').mockReturnValue();
    markProcessingSpy = jest.spyOn(PaymentSplit.prototype, 'markProcessing').mockReturnValue();
    getGatewayAccountSpy = jest.spyOn(accountApplicationService, 'getGatewayAccount');
    createOutboundTransferSpy = jest
      .spyOn(MockGatewayServiceAdapter.prototype, 'createOutboundTransfer')
      .mockResolvedValue({
        status: GatewayTransferStatus.SETTLED,
      } as GatewayTransfer);
    paymentSplitRepository.transaction = jest.fn().mockImplementation(db.$transaction.bind(db));
    paymentSplitRepository.update = jest.fn();

    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payment is not found', async () => {
    const command = new ProcessPaymentSplitsCommand({
      paymentId: 'invalid-payment-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Payment not found'));
  });

  it('should seek gateway service and payment splits based on payment', async () => {
    const command = new ProcessPaymentSplitsCommand(commandProps);
    gatewayServiceFactory.getGateway = jest.fn();
    paymentSplitRepository.findByPaymentId = jest.fn();

    await expect(handler.execute(command)).rejects.toThrow();

    expect(gatewayServiceFactory.getGateway).toHaveBeenCalledWith(payment.getProps().gateway);
    expect(paymentSplitRepository.findByPaymentId).toHaveBeenCalledWith(payment.getId());
  });

  it('should create outbound transfer for each payment split', async () => {
    const command = new ProcessPaymentSplitsCommand(commandProps);

    await handler.execute(command);

    const gatewayAccounts = await Promise.all(
      getGatewayAccountSpy.mock.results.map((r) => r.value)
    ) as GatewayAccount[];
    paymentSplits.forEach((paymentSplit, i) => {
      expect(createOutboundTransferSpy).toHaveBeenCalledWith({
        amount: paymentSplit.getProps().amount,
        receivingGatewayAccountId: gatewayAccounts[i].getProps().idAtGateway,
        idempotencyKey: paymentSplit.getId(),
        source: GatewayTransferSource.PAYMENT_SPLIT,
        sourceId: paymentSplit.getId(),
        paymentIntentId: payment.getProps().paymentIntentId,
      });
    });
  });

  it('should trigger a mark settled for each settled transfer', async () => {
    const command = new ProcessPaymentSplitsCommand(commandProps);

    await handler.execute(command);

    expect(paymentSplits.length).toBeGreaterThan(0);
    paymentSplits.forEach(() => {
      expect(markSettledSpy).toHaveBeenCalled();
      expect(markProcessingSpy).not.toHaveBeenCalled();
    });
  });

  it('should trigger a mark processing for each non-settled transfer', async () => {
    createOutboundTransferSpy.mockResolvedValue({
      status: GatewayTransferStatus.PENDING,
    } as GatewayTransfer);
    const command = new ProcessPaymentSplitsCommand(commandProps);

    await handler.execute(command);

    expect(paymentSplits.length).toBeGreaterThan(0);
    paymentSplits.forEach(() => {
      expect(markSettledSpy).not.toHaveBeenCalled();
      expect(markProcessingSpy).toHaveBeenCalled();
    });
  });

  it('should update payment splits after processing', async () => {
    const command = new ProcessPaymentSplitsCommand(commandProps);

    await handler.execute(command);

    paymentSplits.forEach((paymentSplit) => {
      expect(paymentSplitRepository.update).toHaveBeenCalledWith(paymentSplit);
    });
  });

  it('should operate within a transaction', async () => {
    const command = new ProcessPaymentSplitsCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(getGatewayAccountSpy).not.toHaveBeenCalled();
    expect(createOutboundTransferSpy).not.toHaveBeenCalled();
    expect(paymentSplitRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    const numOfPaymentSplits = paymentSplits.length;
    expect(getGatewayAccountSpy).toHaveBeenCalledTimes(numOfPaymentSplits);
    expect(createOutboundTransferSpy).toHaveBeenCalledTimes(numOfPaymentSplits);
    expect(paymentSplitRepository.update).toHaveBeenCalledTimes(numOfPaymentSplits);
  });
});
