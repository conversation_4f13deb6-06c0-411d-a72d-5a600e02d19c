import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import {
  GATEWAY_TRANSFER_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';
import { GatewayTransferRepositoryPort, Money } from '@/domain/shared';
import { GatewayTransferSource, GatewayTransferStatus } from '@/domain/shared/interfaces';

import { CreateManualPaymentSplitRefundCommand } from './create-manual-payment-split-refund.command';

@CommandHandler(CreateManualPaymentSplitRefundCommand)
export class CreateManualPaymentSplitRefundCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(PAYMENT_SPLIT_REPOSITORY_TOKEN)
    private readonly paymentSplitRepository: PaymentSplitRepositoryPort,
    @Inject(GATEWAY_TRANSFER_REPOSITORY_TOKEN)
    private readonly gatewayTransferRepository: GatewayTransferRepositoryPort,
  ) {}

  async execute(command: CreateManualPaymentSplitRefundCommand): Promise<void> {
    const { paymentSplitId, amount, gatewayTransferId, gatewayAccountId, status } =
      command.getProps();
    const paymentSplit = await this.paymentSplitRepository.findById(paymentSplitId);

    if (!paymentSplit) {
      throw new Error('Invalid payment split id'); // todo: custom error
    }

    return this.db.$transaction(async () => {
      const refundAmount = new Money(amount, paymentSplit.getProps().amount.getCurrency());

      const paymentSplitRefundId = paymentSplit.createRefund({
        isManualRefund: true,
        refundAmount,
      });

      await this.gatewayTransferRepository.create({
        idAtGateway: gatewayTransferId,
        destinationAccountId: gatewayAccountId,
        gatewayType: paymentSplit.getProps().gateway.getType(),
        source: GatewayTransferSource.REFUND_SPLIT,
        sourceId: paymentSplitRefundId,
        idempotencyKey: '', // todo: make nullable
        amount: refundAmount,
        status: GatewayTransferStatus.SETTLED,
      });

      if (status === GatewayTransferStatus.SETTLED) {
        paymentSplit.markRefundIssued(paymentSplitRefundId);
      }

      await this.paymentSplitRepository.update(paymentSplit);
    });
  }
}
