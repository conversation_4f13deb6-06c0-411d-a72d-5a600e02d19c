import { Test, TestingModule } from '@nestjs/testing';

import {
  CreateManualPaymentSplitRefundCommand,
  CreateManualPaymentSplitRefundCommandHandler,
} from '@/application/payment-split/commands/create-manual-payment-split-refund';
import { DatabaseService } from '@/database';
import {
  GATEWAY_TRANSFER_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { Money } from '@/domain/shared';
import { GatewayTransferSource, GatewayTransferStatus } from '@/domain/shared/interfaces';
import {
  GatewayTransferRepositoryMock,
} from '@/test/mocks/repositories/gateway-transfer-repository.mock';
import {
  PaymentSplitRepositoryMock,
} from '@/test/mocks/repositories/payment-split-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

describe('CreateManualPaymentSplitRefundCommandHandler', () => {
  let handler: CreateManualPaymentSplitRefundCommandHandler;
  let db: DatabaseServiceMock;
  let paymentSplitRepository: PaymentSplitRepositoryMock;
  let gatewayTransferRepository: GatewayTransferRepositoryMock;
  let commandProps: CreateManualPaymentSplitRefundCommand['props'];
  let paymentSplit: PaymentSplit;
  const refundId = 'refund-id';

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateManualPaymentSplitRefundCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: PAYMENT_SPLIT_REPOSITORY_TOKEN, useClass: PaymentSplitRepositoryMock },
        { provide: GATEWAY_TRANSFER_REPOSITORY_TOKEN, useClass: GatewayTransferRepositoryMock },
      ],
    }).compile();

    handler = module.get<CreateManualPaymentSplitRefundCommandHandler>(CreateManualPaymentSplitRefundCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    paymentSplitRepository = module.get<PaymentSplitRepositoryMock>(PAYMENT_SPLIT_REPOSITORY_TOKEN);
    gatewayTransferRepository = module.get<GatewayTransferRepositoryMock>(GATEWAY_TRANSFER_REPOSITORY_TOKEN);
    [paymentSplit] = paymentSplitRepository.db;
    commandProps = {
      amount: 10,
      paymentSplitId: paymentSplit.getId(),
      gatewayTransferId: 'gateway-transfer-id',
      gatewayAccountId: 'gateway-account-id',
      status: GatewayTransferStatus.SETTLED,
    };
    paymentSplit.createRefund = jest.fn().mockReturnValue(refundId);
    paymentSplit.markRefundIssued = jest.fn();
    gatewayTransferRepository.create = jest.fn();
    paymentSplitRepository.update = jest.fn();
    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payment split is not found', async () => {
    const command = new CreateManualPaymentSplitRefundCommand({
      ...commandProps,
      paymentSplitId: 'invalid-payment-split-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Invalid payment split id'));
  });

  it('should create a payment split refund', async () => {
    const command = new CreateManualPaymentSplitRefundCommand(commandProps);

    await handler.execute(command);

    expect(paymentSplit.createRefund).toHaveBeenCalledWith({
      isManualRefund: true,
      refundAmount: new Money(commandProps.amount, paymentSplit.getProps().amount.getCurrency()),
    });
  });

  it('should create a gateway transfer for the refund', async () => {
    const command = new CreateManualPaymentSplitRefundCommand(commandProps);

    await handler.execute(command);

    expect(gatewayTransferRepository.create).toHaveBeenCalledWith({
      idAtGateway: commandProps.gatewayTransferId,
      destinationAccountId: commandProps.gatewayAccountId,
      gatewayType: paymentSplit.getProps().gateway.getType(),
      source: GatewayTransferSource.REFUND_SPLIT,
      sourceId: refundId,
      idempotencyKey: '',
      amount: new Money(commandProps.amount, paymentSplit.getProps().amount.getCurrency()),
      status: GatewayTransferStatus.SETTLED,
    });
  });

  it('should mark the refund as issued if the status is settled', async () => {
    const command = new CreateManualPaymentSplitRefundCommand(commandProps);

    await handler.execute(command);

    expect(paymentSplit.markRefundIssued).toHaveBeenCalledWith(refundId);
    expect(paymentSplitRepository.update).toHaveBeenCalledWith(paymentSplit);
    const [markRefundIssuedInvOrder] = (paymentSplit.markRefundIssued as jest.Mock).mock.invocationCallOrder;
    const [updateInvOrder] = (paymentSplitRepository.update as jest.Mock).mock.invocationCallOrder;
    expect(markRefundIssuedInvOrder).toBeLessThan(updateInvOrder);
  });

  it('shouldn\'t mark the refund as issued if the status is not settled', async () => {
    const command = new CreateManualPaymentSplitRefundCommand({
      ...commandProps,
      status: GatewayTransferStatus.PENDING,
    });

    await handler.execute(command);

    expect(paymentSplit.markRefundIssued).not.toHaveBeenCalled();
    expect(paymentSplitRepository.update).toHaveBeenCalledWith(paymentSplit);
  });

  it('should update the payment split', async () => {
    const command = new CreateManualPaymentSplitRefundCommand(commandProps);

    await handler.execute(command);

    expect(paymentSplit.createRefund).toHaveBeenCalled();
    expect(paymentSplitRepository.update).toHaveBeenCalledWith(paymentSplit);
    const [createRefundInvOrder] = (paymentSplit.createRefund as jest.Mock).mock.invocationCallOrder;
    const [updateInvOrder] = (paymentSplitRepository.update as jest.Mock).mock.invocationCallOrder;
    expect(createRefundInvOrder).toBeLessThan(updateInvOrder);
  });

  it('should operate within a transaction', async () => {
    const command = new CreateManualPaymentSplitRefundCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(paymentSplit.createRefund).not.toHaveBeenCalled();
    expect(gatewayTransferRepository.create).not.toHaveBeenCalled();
    expect(paymentSplit.markRefundIssued).not.toHaveBeenCalled();
    expect(paymentSplitRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    expect(paymentSplit.createRefund).toHaveBeenCalled();
    expect(gatewayTransferRepository.create).toHaveBeenCalled();
    expect(paymentSplit.markRefundIssued).toHaveBeenCalled();
    expect(paymentSplitRepository.update).toHaveBeenCalled();
  });
});
