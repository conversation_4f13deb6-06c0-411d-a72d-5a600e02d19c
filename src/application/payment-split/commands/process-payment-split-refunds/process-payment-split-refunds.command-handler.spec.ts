import { Test, TestingModule } from '@nestjs/testing';

import { AccountApplicationService } from '@/application/account/services';
import { DatabaseService } from '@/database';
import { GatewayAccount } from '@/domain/account/gateway-account';
import {
  GATEWAY_SERVICE_FACTORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
  ACCOUNT_REPOSITORY_TOKEN
} from '@/domain/di-tokens';
import { Payment } from '@/domain/payment/payment';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import {
  GatewayTransfer,
  GatewayTransferSource,
  GatewayTransferStatus,
} from '@/domain/shared/interfaces';
import { AccountRepositoryMock } from '@/test/mocks/repositories/account-repository.mock';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { PaymentSplitRepositoryMock } from '@/test/mocks/repositories/payment-split-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import {
  GatewayServiceFactoryMock,
  MockGatewayServiceAdapter,
} from '@/test/mocks/services/gateway-service-factory.mock';

import { ProcessPaymentSplitRefundsCommand, ProcessPaymentSplitRefundsCommandHandler } from '.';

describe('ProcessPaymentSplitRefundsCommandHandler', () => {
  let handler: ProcessPaymentSplitRefundsCommandHandler;
  let db: DatabaseServiceMock;
  let paymentRepository: PaymentRepositoryMock;
  let paymentSplitRepository: PaymentSplitRepositoryMock;
  let gatewayServiceFactory: GatewayServiceFactoryMock;
  let accountApplicationService: AccountApplicationService;
  let commandProps: ProcessPaymentSplitRefundsCommand['props'];
  let payment: Payment;
  let paymentSplits: PaymentSplit[];
  let markRefundIssuedSpy: jest.SpyInstance;
  let markRefundProcessingSpy: jest.SpyInstance;
  let getGatewayAccountSpy: jest.SpyInstance;
  let createInboundTransferSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessPaymentSplitRefundsCommandHandler,
        AccountApplicationService,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: PAYMENT_SPLIT_REPOSITORY_TOKEN, useClass: PaymentSplitRepositoryMock },
        { provide: ACCOUNT_REPOSITORY_TOKEN, useClass: AccountRepositoryMock },
        { provide: GATEWAY_SERVICE_FACTORY_TOKEN, useClass: GatewayServiceFactoryMock },
      ],
    }).compile();

    handler = module.get<ProcessPaymentSplitRefundsCommandHandler>(ProcessPaymentSplitRefundsCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    paymentSplitRepository = module.get<PaymentSplitRepositoryMock>(PAYMENT_SPLIT_REPOSITORY_TOKEN);
    gatewayServiceFactory = module.get<GatewayServiceFactoryMock>(GATEWAY_SERVICE_FACTORY_TOKEN);
    accountApplicationService = module.get<AccountApplicationService>(AccountApplicationService);
    [payment] = paymentRepository.db;
    paymentSplits = await paymentSplitRepository.findByPaymentId(payment.getId());
    commandProps = {
      paymentId: payment.getId(),
      refundId: payment.getProps().refunds[0].getId(),
    };
    markRefundIssuedSpy = jest.spyOn(PaymentSplit.prototype, 'markRefundIssued').mockReturnValue();
    markRefundProcessingSpy = jest.spyOn(PaymentSplit.prototype, 'markRefundProcessing').mockReturnValue();
    getGatewayAccountSpy = jest.spyOn(accountApplicationService, 'getGatewayAccount');
    createInboundTransferSpy = jest
      .spyOn(MockGatewayServiceAdapter.prototype, 'createInboundTransfer')
      .mockResolvedValue({
        status: GatewayTransferStatus.SETTLED,
      } as GatewayTransfer);
    paymentSplitRepository.transaction = jest.fn().mockImplementation(db.$transaction.bind(db));
    paymentSplitRepository.update = jest.fn();

    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payment is not found', async () => {
    const command = new ProcessPaymentSplitRefundsCommand({
      ...commandProps,
      paymentId: 'invalid-payment-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Payment not found'));
  });

  it('should seek gateway service and payment splits based on payment', async () => {
    const command = new ProcessPaymentSplitRefundsCommand(commandProps);
    gatewayServiceFactory.getGateway = jest.fn();
    paymentSplitRepository.findByPaymentId = jest.fn();

    await expect(handler.execute(command)).rejects.toThrow();

    expect(gatewayServiceFactory.getGateway).toHaveBeenCalledWith(payment.getProps().gateway);
    expect(paymentSplitRepository.findByPaymentId).toHaveBeenCalledWith(payment.getId());
  });

  it('should create inbound transfer for each payment split refund matching the refund id', async () => {
    const command = new ProcessPaymentSplitRefundsCommand(commandProps);

    await handler.execute(command);

    const gatewayAccounts = await Promise.all(
      getGatewayAccountSpy.mock.results.map((r) => r.value)
    ) as GatewayAccount[];
    paymentSplits.forEach((paymentSplit) => {
      const refundSplits = paymentSplit.getProps().refunds
        .filter((r) => r.getProps().refundId === commandProps.refundId);

      expect(refundSplits.length).toBeGreaterThan(0);
      refundSplits.forEach((paymentSplitRefund, i) => {
        expect(createInboundTransferSpy).toHaveBeenCalledWith({
          amount: paymentSplitRefund.getProps().amount,
          sendingGatewayAccountId: gatewayAccounts[i].getProps().idAtGateway,
          idempotencyKey: paymentSplitRefund.getId(),
          source: GatewayTransferSource.REFUND_SPLIT,
          sourceId: paymentSplitRefund.getId(),
          originalSourceId: paymentSplit.getId(),
        });
      });
    });
  });

  it('should trigger a mark refund issued for each settled transfer', async () => {
    const command = new ProcessPaymentSplitRefundsCommand(commandProps);

    await handler.execute(command);

    paymentSplits.forEach((paymentSplit) => {
      const refundSplits = paymentSplit.getProps().refunds
        .filter((r) => r.getProps().refundId === commandProps.refundId);

      expect(refundSplits.length).toBeGreaterThan(0);
      refundSplits.forEach((paymentSplitRefund) => {
        expect(markRefundIssuedSpy).toHaveBeenCalledWith(paymentSplitRefund.getId());
        expect(markRefundProcessingSpy).not.toHaveBeenCalled();
      });
    });
  });

  it('should trigger a mark refund processing for each non-settled transfer', async () => {
    const command = new ProcessPaymentSplitRefundsCommand(commandProps);
    createInboundTransferSpy.mockResolvedValue({
      status: GatewayTransferStatus.PENDING,
    } as GatewayTransfer);

    await handler.execute(command);

    paymentSplits.forEach((paymentSplit) => {
      const refundSplits = paymentSplit.getProps().refunds
        .filter((r) => r.getProps().refundId === commandProps.refundId);

      expect(refundSplits.length).toBeGreaterThan(0);
      refundSplits.forEach((paymentSplitRefund) => {
        expect(markRefundIssuedSpy).not.toHaveBeenCalled();
        expect(markRefundProcessingSpy).toHaveBeenCalledWith(paymentSplitRefund.getId());
      });
    });
  });

  it('should update the payment split after processing all refunds', async () => {
    const command = new ProcessPaymentSplitRefundsCommand(commandProps);

    await handler.execute(command);

    paymentSplits.forEach((paymentSplit) => {
      expect(paymentSplitRepository.update).toHaveBeenCalledWith(paymentSplit);
    });
  });

  it('should operate within a transaction', async () => {
    const command = new ProcessPaymentSplitRefundsCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(getGatewayAccountSpy).not.toHaveBeenCalled();
    expect(createInboundTransferSpy).not.toHaveBeenCalled();
    expect(paymentSplitRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    const numOfPaymentSplitRefunds = paymentSplits
      .flatMap((ps) => ps.getProps().refunds)
      .filter((r) => r.getProps().refundId === commandProps.refundId)
      .length;
    expect(getGatewayAccountSpy).toHaveBeenCalledTimes(numOfPaymentSplitRefunds);
    expect(createInboundTransferSpy).toHaveBeenCalledTimes(numOfPaymentSplitRefunds);
    expect(paymentSplitRepository.update).toHaveBeenCalledTimes(paymentSplits.length);
  });
});
