import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { AccountApplicationService } from '@/application/account/services';
import { AccountRepositoryPort } from '@/domain/account/ports';
import {
  ACCOUNT_REPOSITORY_TOKEN,
  GATEWAY_SERVICE_FACTORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { PaymentSplitRefund } from '@/domain/payment-split/payment-split-refund';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';
import { GatewayServiceFactoryPort, GatewayServicePort } from '@/domain/shared';
import { GatewayTransferSource, GatewayTransferStatus } from '@/domain/shared/interfaces';

import { ProcessPaymentSplitRefundsCommand } from './process-payment-split-refunds.command';

@CommandHandler(ProcessPaymentSplitRefundsCommand)
export class ProcessPaymentSplitRefundsCommandHandler implements ICommandHandler {
  constructor(
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(PAYMENT_SPLIT_REPOSITORY_TOKEN)
    private readonly paymentSplitRepository: PaymentSplitRepositoryPort,
    @Inject(ACCOUNT_REPOSITORY_TOKEN)
    private readonly accountRepository: AccountRepositoryPort,
    @Inject(GATEWAY_SERVICE_FACTORY_TOKEN)
    private readonly gatewayServiceFactory: GatewayServiceFactoryPort,
    private readonly accountApplicationService: AccountApplicationService,
  ) {}

  async execute(command: ProcessPaymentSplitRefundsCommand): Promise<PaymentSplit[]> {
    const { paymentId, refundId } = command.getProps();

    const payment = await this.paymentRepository.findById(paymentId);

    if (!payment) {
      throw new Error('Payment not found'); // todo: custom error
    }

    const gatewayService = this.gatewayServiceFactory.getGateway(payment.getProps().gateway);

    const paymentSplits = await this.paymentSplitRepository.findByPaymentId(paymentId);

    await this.processRefundForPaymentSplits(paymentSplits, refundId, gatewayService);

    return paymentSplits;
  }

  private async processRefundForPaymentSplits(
    paymentSplits: PaymentSplit[],
    refundId: string,
    gatewayService: GatewayServicePort,
  ) {
    await this.paymentSplitRepository.transaction(async () =>
      Promise.all(
        paymentSplits.map(async (paymentSplit) => {
          await this.processPaymentSplitRefunds(paymentSplit, refundId, gatewayService);
          await this.paymentSplitRepository.update(paymentSplit);
        }),
      ),
    );
  }

  private async processPaymentSplitRefunds(
    paymentSplit: PaymentSplit,
    refundId: string,
    gatewayService: GatewayServicePort,
  ) {
    const refundSplits = this.getPaymentSplitRefunds(paymentSplit, refundId);

    await Promise.all(
      refundSplits.map(async (refundSplit) =>
        this.processPaymentSplitRefund(paymentSplit, refundSplit, gatewayService),
      ),
    );
  }

  private getPaymentSplitRefunds(
    paymentSplit: PaymentSplit,
    refundId: string,
  ): PaymentSplitRefund[] {
    return paymentSplit
      .getProps()
      .refunds.filter((refund) => refund.getProps().refundId === refundId);
  }

  private async processPaymentSplitRefund(
    paymentSplit: PaymentSplit,
    paymentSplitRefund: PaymentSplitRefund,
    gatewayService: GatewayServicePort,
  ) {
    const { accountId, gateway } = paymentSplit.getProps();
    const { amount } = paymentSplitRefund.getProps();

    const sendingGatewayAccount = await this.accountApplicationService.getGatewayAccount(
      accountId,
      gateway,
    );
    const sendingGatewayAccountId = sendingGatewayAccount.getProps().idAtGateway;

    const gatewayTransfer = await gatewayService.createInboundTransfer({
      amount,
      sendingGatewayAccountId,
      idempotencyKey: this.generateIdempotencyKey(paymentSplitRefund),
      source: GatewayTransferSource.REFUND_SPLIT,
      sourceId: paymentSplitRefund.getId(),
      originalSourceId: paymentSplit.getId(),
    });

    if (gatewayTransfer.status === GatewayTransferStatus.SETTLED) {
      paymentSplit.markRefundIssued(paymentSplitRefund.getId());
    } else {
      paymentSplit.markRefundProcessing(paymentSplitRefund.getId());
    }
  }

  private generateIdempotencyKey(refundSplit: PaymentSplitRefund) {
    return refundSplit.getId();
  }
}
