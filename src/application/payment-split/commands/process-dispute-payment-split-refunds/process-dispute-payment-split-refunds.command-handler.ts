import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { AccountApplicationService } from '@/application/account/services';
import { AccountRepositoryPort } from '@/domain/account/ports';
import {
  ACCOUNT_REPOSITORY_TOKEN, DISPUTE_REPOSITORY_TOKEN,
  GATEWAY_SERVICE_FACTORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { Dispute } from '@/domain/dispute/dispute';
import { DisputeRepositoryPort } from '@/domain/dispute/ports';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { PaymentSplitRefund } from '@/domain/payment-split/payment-split-refund';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';
import { GatewayServiceFactoryPort, GatewayServicePort, Money } from '@/domain/shared';
import { GatewayTransferSource, GatewayTransferStatus } from '@/domain/shared/interfaces';

import { ProcessDisputePaymentSplitRefundsCommand } from './process-dispute-payment-split-refunds.command';

@CommandHandler(ProcessDisputePaymentSplitRefundsCommand)
export class ProcessDisputePaymentSplitRefundsCommandHandler implements ICommandHandler {
  constructor(
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(PAYMENT_SPLIT_REPOSITORY_TOKEN)
    private readonly paymentSplitRepository: PaymentSplitRepositoryPort,
    @Inject(ACCOUNT_REPOSITORY_TOKEN)
    private readonly accountRepository: AccountRepositoryPort,
    @Inject(GATEWAY_SERVICE_FACTORY_TOKEN)
    private readonly gatewayServiceFactory: GatewayServiceFactoryPort,
    @Inject(DISPUTE_REPOSITORY_TOKEN)
    private readonly disputeRepository: DisputeRepositoryPort,
    private readonly accountApplicationService: AccountApplicationService,
  ) {}

  async execute(command: ProcessDisputePaymentSplitRefundsCommand)    {
    const { disputeId } = command.getProps();

    const dispute = await this.disputeRepository.findById(disputeId);

    if (!dispute) {
      throw new Error('Dispute not found');
    }

    const payment = await this.paymentRepository.findByPaymentIntentId(dispute.getProps().paymentIntentId);

    if (!payment) {
      throw new Error('Payment not found'); // todo: custom error
    }

    const gatewayService = this.gatewayServiceFactory.getGateway(payment.getProps().gateway);

    const paymentSplits = await this.paymentSplitRepository.findByPaymentId(payment.getId());

    await this.processPaymentSplitTransferReversal(paymentSplits, dispute, gatewayService);

    return paymentSplits;
  }

  private createPaymentSplitRefund(
    paymentSplit: PaymentSplit,
    refundAmount: Money,
    disputeId: string
  ): PaymentSplitRefund {
    const paymentSplitRefundId = paymentSplit.createRefund({
      refundAmount,
      isManualRefund: false,
      disputeId,
    });

    return paymentSplit.getPaymentSplitRefund(paymentSplitRefundId);
  }

  private async processPaymentSplitTransferReversal(
    paymentSplits: PaymentSplit[],
    dispute: Dispute,
    gatewayService: GatewayServicePort,
  ): Promise<void> {
    const totalSplitsAmount = paymentSplits.reduce(
      (acc, split) => acc.add(split.getProps().amount),
      Money.zeroFrom(paymentSplits[0].getProps().amount)
    );

    await this.paymentSplitRepository.transaction(async () => Promise.all(
      paymentSplits.map(async (paymentSplit) => {
        const { accountId, gateway } = paymentSplit.getProps();

        const chargeBackAmount = this.calculateChargeBackAmount(
          paymentSplit.getProps().amount,
          dispute.getProps().amount,
          totalSplitsAmount
        );

        const refundAmount = this.calculateChargeBackAmount(
          paymentSplit.getProps().amount,
          dispute.getProps().amount,
          totalSplitsAmount
        );

        const paymentSplitRefund = this.createPaymentSplitRefund(
          paymentSplit,
          refundAmount,
          dispute.getId()
        );

        const sendingGatewayAccount = await this.accountApplicationService.getGatewayAccount(
          accountId,
          gateway
        );

        const gatewayTransfer = await gatewayService.createInboundTransfer({
          amount: chargeBackAmount,
          sendingGatewayAccountId: sendingGatewayAccount.getProps().idAtGateway,
          idempotencyKey: this.createIdempotencyKey(dispute.getId(), paymentSplit.getId()),
          source: GatewayTransferSource.REFUND_SPLIT,
          sourceId: paymentSplitRefund.getId(),
          originalSourceId: paymentSplit.getId(),
        });

        if (gatewayTransfer.status === GatewayTransferStatus.SETTLED) {
          paymentSplit.markRefundIssued(paymentSplitRefund.getId());
        } else {
          paymentSplit.markRefundProcessing(paymentSplitRefund.getId());
        }

        await this.paymentSplitRepository.update(paymentSplit);
      })
    ));
  }

  private createIdempotencyKey(disputeId: string, paymentSplitId: string): string {
    return `dispute-${disputeId}-payment-split-${paymentSplitId}`;
  }

  private calculateChargeBackAmount(
    paymentAmount: Money,
    disputeAmount: Money,
    totalSplitsAmount: Money
  ) {
    const splitPercentage = paymentAmount.div(totalSplitsAmount.getProps().amount.toNumber());

    return disputeAmount.mul(splitPercentage.getProps().amount.toNumber());
  }
}
