import { Test, TestingModule } from '@nestjs/testing';

import { AccountApplicationService } from '@/application/account/services';
import { DatabaseService } from '@/database';
import { GatewayAccount } from '@/domain/account/gateway-account';
import {
  GATEWAY_SERVICE_FACTORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
  ACCOUNT_REPOSITORY_TOKEN,
  DISPUTE_REPOSITORY_TOKEN
} from '@/domain/di-tokens';
import { Dispute } from '@/domain/dispute/dispute';
import { Payment } from '@/domain/payment/payment';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import {
  PaymentSplitRefund,
} from '@/domain/payment-split/payment-split-refund';
import { Money } from '@/domain/shared';
import {
  GatewayTransfer,
  GatewayTransferSource,
  GatewayTransferStatus,
} from '@/domain/shared/interfaces';
import { AccountRepositoryMock } from '@/test/mocks/repositories/account-repository.mock';
import { DisputeRepositoryMock } from '@/test/mocks/repositories/dispute-repository.mock';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { PaymentSplitRepositoryMock } from '@/test/mocks/repositories/payment-split-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import {
  GatewayServiceFactoryMock,
  MockGatewayServiceAdapter,
} from '@/test/mocks/services/gateway-service-factory.mock';

import { ProcessDisputePaymentSplitRefundsCommand, ProcessDisputePaymentSplitRefundsCommandHandler } from '.';

describe('ProcessDisputePaymentSplitRefundsCommandHandler', () => {
  let handler: ProcessDisputePaymentSplitRefundsCommandHandler;
  let db: DatabaseServiceMock;
  let accountApplicationService: AccountApplicationService;
  let paymentRepository: PaymentRepositoryMock;
  let paymentSplitRepository: PaymentSplitRepositoryMock;
  let disputeRepository: DisputeRepositoryMock;
  let gatewayServiceFactory: GatewayServiceFactoryMock;
  let commandProps: ProcessDisputePaymentSplitRefundsCommand['props'];
  let dispute: Dispute;
  let payment: Payment;
  let paymentSplits: PaymentSplit[];
  let paymentSplitRefunds: PaymentSplitRefund[] = [];
  let createRefundSpy: jest.SpyInstance;
  let getPaymentSplitRefundSpy: jest.SpyInstance;
  let markRefundIssuedSpy: jest.SpyInstance;
  let markRefundProcessingSpy: jest.SpyInstance;
  let createInboundTransferSpy: jest.SpyInstance;
  let getGatewayAccountSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessDisputePaymentSplitRefundsCommandHandler,
        AccountApplicationService,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: PAYMENT_SPLIT_REPOSITORY_TOKEN, useClass: PaymentSplitRepositoryMock },
        { provide: ACCOUNT_REPOSITORY_TOKEN, useClass: AccountRepositoryMock },
        { provide: GATEWAY_SERVICE_FACTORY_TOKEN, useClass: GatewayServiceFactoryMock },
        { provide: DISPUTE_REPOSITORY_TOKEN, useClass: DisputeRepositoryMock },
      ],
    }).compile();

    handler = module.get<ProcessDisputePaymentSplitRefundsCommandHandler>(ProcessDisputePaymentSplitRefundsCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    accountApplicationService = module.get<AccountApplicationService>(AccountApplicationService);
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    paymentSplitRepository = module.get<PaymentSplitRepositoryMock>(PAYMENT_SPLIT_REPOSITORY_TOKEN);
    disputeRepository = module.get<DisputeRepositoryMock>(DISPUTE_REPOSITORY_TOKEN);
    gatewayServiceFactory = module.get<GatewayServiceFactoryMock>(GATEWAY_SERVICE_FACTORY_TOKEN);
    [dispute] = disputeRepository.db;
    [payment] = paymentRepository.db;
    paymentSplits = await paymentSplitRepository.findByPaymentId(payment.getId());
    commandProps = {
      disputeId: dispute.getId(),
    };
    createRefundSpy = jest.spyOn(PaymentSplit.prototype, 'createRefund');
    getPaymentSplitRefundSpy = jest.spyOn(PaymentSplit.prototype, 'getPaymentSplitRefund');
    markRefundIssuedSpy = jest.spyOn(PaymentSplit.prototype, 'markRefundIssued').mockReturnValue();
    markRefundProcessingSpy = jest.spyOn(PaymentSplit.prototype, 'markRefundProcessing').mockReturnValue();
    paymentSplitRefunds = paymentSplits.map((split, i) => ({
      getId: () => `refund-id-${i}`,
    } as PaymentSplitRefund));
    paymentSplitRefunds.forEach((refund) => {
      createRefundSpy.mockReturnValueOnce(refund.getId());
      getPaymentSplitRefundSpy.mockReturnValueOnce(refund);
    });
    getGatewayAccountSpy = jest.spyOn(accountApplicationService, 'getGatewayAccount');
    createInboundTransferSpy = jest
      .spyOn(MockGatewayServiceAdapter.prototype, 'createInboundTransfer')
      .mockResolvedValue({
        status: GatewayTransferStatus.SETTLED,
      } as GatewayTransfer);
    paymentSplitRepository.transaction = jest.fn().mockImplementation(db.$transaction.bind(db));
    paymentSplitRepository.update = jest.fn();

    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if dispute is not found', async () => {
    const command = new ProcessDisputePaymentSplitRefundsCommand({
      disputeId: 'invalid-dispute-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Dispute not found'));
  });

  it('should fail if payment is not found', async () => {
    const command = new ProcessDisputePaymentSplitRefundsCommand(commandProps);
    paymentRepository.findByPaymentIntentId = jest.fn().mockResolvedValue(null);

    await expect(handler.execute(command)).rejects.toThrow(new Error('Payment not found'));
    expect(paymentRepository.findByPaymentIntentId).toHaveBeenCalledWith(dispute.getProps().paymentIntentId);
  });

  it('should seek gateway service and payment splits based on payment', async () => {
    const command = new ProcessDisputePaymentSplitRefundsCommand(commandProps);
    gatewayServiceFactory.getGateway = jest.fn();
    paymentSplitRepository.findByPaymentId = jest.fn();

    await expect(handler.execute(command)).rejects.toThrow();

    expect(gatewayServiceFactory.getGateway).toHaveBeenCalledWith(payment.getProps().gateway);
    expect(paymentSplitRepository.findByPaymentId).toHaveBeenCalledWith(payment.getId());
  });

  it('should create payment split refunds for each split', async () => {
    const command = new ProcessDisputePaymentSplitRefundsCommand(commandProps);

    await handler.execute(command);

    const totalSplitsAmount = paymentSplits.reduce(
      (acc, split) => acc.add(split.getProps().amount),
      Money.zeroFrom(paymentSplits[0].getProps().amount)
    );
    paymentSplits.forEach((split, i) => {
      const disputeId = dispute.getId();
      const splitPercentage = split.getProps().amount.div(totalSplitsAmount.getProps().amount.toNumber());
      const refundAmount = dispute.getProps().amount.mul(splitPercentage.getProps().amount.toNumber());
      expect(createRefundSpy).toHaveBeenCalledWith({
        disputeId,
        refundAmount,
        isManualRefund: false,
      });
      expect(getPaymentSplitRefundSpy).toHaveBeenCalledWith(paymentSplitRefunds[i].getId());
    });
  });

  it('should create inbound transfer for each split', async () => {
    const command = new ProcessDisputePaymentSplitRefundsCommand(commandProps);

    await handler.execute(command);

    const gatewayAccounts = await Promise.all(getGatewayAccountSpy.mock.results
      .map((r) => r.value)) as GatewayAccount[];
    const totalSplitsAmount = paymentSplits.reduce(
      (acc, split) => acc.add(split.getProps().amount),
      Money.zeroFrom(paymentSplits[0].getProps().amount)
    );
    paymentSplits.forEach((split, i) => {
      const splitId = split.getId();
      const splitPercentage = split.getProps().amount.div(totalSplitsAmount.getProps().amount.toNumber());
      const chargeBackAmount = dispute.getProps().amount.mul(splitPercentage.getProps().amount.toNumber());
      expect(createInboundTransferSpy).toHaveBeenCalledWith({
        amount: chargeBackAmount,
        sendingGatewayAccountId: gatewayAccounts[i].getProps().idAtGateway,
        idempotencyKey: `dispute-${dispute.getId()}-payment-split-${splitId}`,
        source: GatewayTransferSource.REFUND_SPLIT,
        sourceId: paymentSplitRefunds[i].getId(),
        originalSourceId: splitId,
      });
    });
  });

  it('should trigger a mark refund issued for each split on a settled transfer', async () => {
    const command = new ProcessDisputePaymentSplitRefundsCommand(commandProps);

    await handler.execute(command);

    paymentSplits.forEach((split, i) => {
      expect(markRefundIssuedSpy).toHaveBeenCalledWith(paymentSplitRefunds[i].getId());
      expect(markRefundProcessingSpy).not.toHaveBeenCalled();
      expect(paymentSplitRepository.update).toHaveBeenCalledWith(split);
    });
    expect(paymentSplits.length).toBeGreaterThan(0);
  });

  it('should trigger a mark refund processing for each split on a non-settled transfer', async () => {
    const command = new ProcessDisputePaymentSplitRefundsCommand(commandProps);
    createInboundTransferSpy.mockResolvedValue({
      status: GatewayTransferStatus.PENDING,
    } as GatewayTransfer);

    await handler.execute(command);

    paymentSplits.forEach((split, i) => {
      expect(markRefundProcessingSpy).toHaveBeenCalledWith(paymentSplitRefunds[i].getId());
      expect(markRefundIssuedSpy).not.toHaveBeenCalled();
      expect(paymentSplitRepository.update).toHaveBeenCalledWith(split);
    });
    expect(paymentSplits.length).toBeGreaterThan(0);
  });

  it('should operate within a transaction', async () => {
    const command = new ProcessDisputePaymentSplitRefundsCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(createRefundSpy).not.toHaveBeenCalled();
    expect(getPaymentSplitRefundSpy).not.toHaveBeenCalled();
    expect(getGatewayAccountSpy).not.toHaveBeenCalled();
    expect(createInboundTransferSpy).not.toHaveBeenCalled();
    expect(paymentSplitRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    const numOfSplits = paymentSplits.length;
    expect(createRefundSpy).toHaveBeenCalledTimes(numOfSplits);
    expect(getPaymentSplitRefundSpy).toHaveBeenCalledTimes(numOfSplits);
    expect(getGatewayAccountSpy).toHaveBeenCalledTimes(numOfSplits);
    expect(createInboundTransferSpy).toHaveBeenCalledTimes(numOfSplits);
    expect(paymentSplitRepository.update).toHaveBeenCalledTimes(numOfSplits);
  });
});
