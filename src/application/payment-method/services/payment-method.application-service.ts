import { Inject, Injectable } from '@nestjs/common';

import { DatabaseService } from '@/database';
import { PAYMENT_METHOD_REPOSITORY_TOKEN, GATEWAY_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { GatewayPaymentMethod } from '@/domain/payment-method/gateway-payment-method';
import { PaymentMethod } from '@/domain/payment-method/payment-method';
import { PaymentMethodRepositoryPort } from '@/domain/payment-method/ports';
import { Gateway, GatewayType, PaymentMethodType } from '@/domain/shared';

interface CreatePaymentMethodParams {
  type: PaymentMethodType;
  cardLast4: string | null;
  cardExpiryYear: number | null;
  cardExpiryMonth: number | null;
  gatewayPaymentMethod: {
    gatewayType: GatewayType;
    idAtGateway: string;
    fingerprint: string | null;
  };
}

@Injectable()
export class PaymentMethodApplicationService {
  constructor(
    private readonly db: DatabaseService,
    @Inject(PAYMENT_METHOD_REPOSITORY_TOKEN)
    private readonly paymentMethodRepository: PaymentMethodRepositoryPort,
    @Inject(GATEWAY_REPOSITORY_TOKEN)
    private readonly gatewayRepository: GatewayRepositoryPort,
  ) {}

  async createOrGet(params: CreatePaymentMethodParams) {
    const { gatewayPaymentMethod, type, cardExpiryMonth, cardExpiryYear, cardLast4 } = params;

    const gateway = await this.gatewayRepository.findByGatewayType(
      gatewayPaymentMethod.gatewayType,
    );

    if (!gateway) {
      throw new Error('Gateway not found'); // todo: custom error
    }

    const existingPaymentMethod = gatewayPaymentMethod.fingerprint
      ? await this.paymentMethodRepository.findByFingerprint(gatewayPaymentMethod.fingerprint)
      : null;

    if (existingPaymentMethod) {
      return existingPaymentMethod;
    }

    return this.db.$transaction(async () => {
      const paymentMethod = PaymentMethod.create({
        type,
        cardExpiryMonth,
        cardExpiryYear,
        cardLast4,
        gatewayPaymentMethod: GatewayPaymentMethod.create({
          idAtGateway: gatewayPaymentMethod.idAtGateway,
          fingerprint: gatewayPaymentMethod.fingerprint,
          gateway: new Gateway(gateway.getId(), gateway.getProps().type),
        }),
      });

      await this.paymentMethodRepository.create(paymentMethod);

      return paymentMethod;
    });
  }
}
