import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { PAYMENT_METHOD_REPOSITORY_TOKEN, GATEWAY_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { GatewayPaymentMethod } from '@/domain/payment-method/gateway-payment-method';
import { PaymentMethod } from '@/domain/payment-method/payment-method';
import { PaymentMethodRepositoryPort } from '@/domain/payment-method/ports';
import { Gateway } from '@/domain/shared';

import { CreatePaymentMethodCommand } from './create-payment-method.command';

@CommandHandler(CreatePaymentMethodCommand)
export class CreatePaymentMethodCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(PAYMENT_METHOD_REPOSITORY_TOKEN)
    private readonly paymentMethodRepository: PaymentMethodRepositoryPort,
    @Inject(GATEWAY_REPOSITORY_TOKEN)
    private readonly gatewayRepository: GatewayRepositoryPort,
  ) {}

  async execute(command: CreatePaymentMethodCommand): Promise<PaymentMethod> {
    const { gatewayPaymentMethod, type, cardExpiryMonth, cardExpiryYear, cardLast4 } =
      command.getProps();

    const gateway = await this.gatewayRepository.findByGatewayType(
      gatewayPaymentMethod.gatewayType,
    );

    if (!gateway) {
      throw new Error('Gateway not found'); // todo: custom error
    }

    return this.db.$transaction(async () => {
      const paymentMethod = PaymentMethod.create({
        type,
        cardExpiryMonth,
        cardExpiryYear,
        cardLast4,
        gatewayPaymentMethod: GatewayPaymentMethod.create({
          idAtGateway: gatewayPaymentMethod.idAtGateway,
          fingerprint: gatewayPaymentMethod.fingerprint,
          gateway: new Gateway(gateway.getId(), gateway.getProps().type),
        }),
      });

      await this.paymentMethodRepository.create(paymentMethod);

      return paymentMethod;
    });
  }
}
