import { Command } from '@/core/ddd';
import { GatewayType, PaymentMethodType } from '@/domain/shared';

type Props = {
  type: PaymentMethodType;
  cardLast4: string | null;
  cardExpiryYear: number | null;
  cardExpiryMonth: number | null;
  gatewayPaymentMethod: {
    gatewayType: GatewayType;
    idAtGateway: string;
    fingerprint: string | null;
  };
};

export class CreatePaymentMethodCommand extends Command<Props> {}
