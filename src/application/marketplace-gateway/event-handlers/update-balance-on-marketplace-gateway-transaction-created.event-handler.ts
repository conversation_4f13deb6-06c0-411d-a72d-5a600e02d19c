import { CommandBus } from '@nestjs/cqrs';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';

import { UpdateMarketplaceGatewayBalanceCommand } from '@/application/marketplace-gateway/commands/update-balance';
import { MarketplaceGatewayTransactionCreatedDomainEvent } from '@/domain/marketplace-gateway-transaction/events';

@EventsHandler(MarketplaceGatewayTransactionCreatedDomainEvent)
export class UpdateBalanceOnMarketplaceGatewayTransactionCreatedEventHandler
  implements IEventHandler
{
  constructor(private readonly commandBus: CommandBus) {}

  async handle(event: MarketplaceGatewayTransactionCreatedDomainEvent) {
    const { aggregateId } = event.getProps();

    await this.commandBus.execute(
      new UpdateMarketplaceGatewayBalanceCommand({
        marketplaceGatewayTransactionId: aggregateId,
      }),
    );
  }
}
