import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import {
  MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { MarketplaceGatewayRepositoryPort } from '@/domain/marketplace-gateway/ports';
import { MarketplaceGatewayTransactionRepositoryPort } from '@/domain/marketplace-gateway-transaction/ports';

import { UpdateMarketplaceGatewayBalanceCommand } from './update-marketplace-gateway-balance.command';

@CommandHandler(UpdateMarketplaceGatewayBalanceCommand)
export class UpdateMarketplaceGatewayBalanceCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayRepository: MarketplaceGatewayRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayTransactionRepository:
      MarketplaceGatewayTransactionRepositoryPort,
  ) { }

  async execute(command: UpdateMarketplaceGatewayBalanceCommand): Promise<void> {
    const { marketplaceGatewayTransactionId } = command.getProps();

    const marketplaceGatewayTransaction =
      await this.marketplaceGatewayTransactionRepository.findById(marketplaceGatewayTransactionId);

    if (!marketplaceGatewayTransaction) {
      throw new Error('Marketplace Gateway Transaction not found');
    }

    await this.db.$transaction(async () => {
      const marketplaceGateway = await this.marketplaceGatewayRepository.findById(
        marketplaceGatewayTransaction.getProps().marketplaceGatewayId,
      );

      marketplaceGateway.addToBalance(marketplaceGatewayTransaction.getProps().amount);

      await this.marketplaceGatewayRepository.update(marketplaceGateway);
    });
  }
}
