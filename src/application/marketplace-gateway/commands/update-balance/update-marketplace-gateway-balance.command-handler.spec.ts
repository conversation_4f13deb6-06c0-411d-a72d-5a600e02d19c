import { Test, TestingModule } from '@nestjs/testing';

import {
  UpdateMarketplaceGatewayBalanceCommand,
  UpdateMarketplaceGatewayBalanceCommandHandler
} from '@/application/marketplace-gateway/commands/update-balance';
import { DatabaseService } from '@/database';
import {
  MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN
} from '@/domain/di-tokens';
import {
  MarketplaceGatewayTransaction
} from '@/domain/marketplace-gateway-transaction/marketplace-gateway-transaction';
import { MarketplaceGatewayRepositoryMock } from '@/test/mocks/repositories/marketplace-gateway-repository.mock';
import {
  MarketplaceGatewayTransactionRepositoryMock
} from '@/test/mocks/repositories/marketplace-gateway-transaction-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

describe('UpdateMarketplaceGatewayBalanceCommandHandler', () => {
  let handler: UpdateMarketplaceGatewayBalanceCommandHandler;
  let db: DatabaseServiceMock;
  let marketplaceGatewayRepository: MarketplaceGatewayRepositoryMock;
  let marketplaceGatewayTransactionRepository: MarketplaceGatewayTransactionRepositoryMock;
  let commandProps: UpdateMarketplaceGatewayBalanceCommand['props'];
  let marketplaceGatewayTransaction: MarketplaceGatewayTransaction;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateMarketplaceGatewayBalanceCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: MARKETPLACE_GATEWAY_REPOSITORY_TOKEN, useClass: MarketplaceGatewayRepositoryMock },
        {
          provide: MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN,
          useClass: MarketplaceGatewayTransactionRepositoryMock
        },
      ],
    }).compile();

    handler = module.get<UpdateMarketplaceGatewayBalanceCommandHandler>(UpdateMarketplaceGatewayBalanceCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    marketplaceGatewayRepository = module.get<MarketplaceGatewayRepositoryMock>(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN);
    marketplaceGatewayTransactionRepository =
      module.get<MarketplaceGatewayTransactionRepositoryMock>(MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN);
    [marketplaceGatewayTransaction] = marketplaceGatewayTransactionRepository.db;
    commandProps = {
      marketplaceGatewayTransactionId: marketplaceGatewayTransaction.getId(),
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if marketplace gateway transaction not found', async () => {
    const command = new UpdateMarketplaceGatewayBalanceCommand({
      marketplaceGatewayTransactionId: 'invalid-id'
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Marketplace Gateway Transaction not found'));
  });

  it('should update the marketplace gateway balance in a transaction', async () => {
    const command = new UpdateMarketplaceGatewayBalanceCommand(commandProps);
    const [marketplaceGateway] = marketplaceGatewayRepository.db;
    marketplaceGateway.addToBalance = jest.fn();
    marketplaceGatewayRepository.findById = jest.fn().mockResolvedValue(marketplaceGateway);
    marketplaceGatewayRepository.update = jest.fn();

    await handler.execute(command);

    expect(marketplaceGatewayRepository.findById).not.toHaveBeenCalled();
    expect(marketplaceGateway.addToBalance).not.toHaveBeenCalled();
    expect(marketplaceGatewayRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    const transactionProps = marketplaceGatewayTransaction.getProps();
    expect(marketplaceGatewayRepository.findById).toHaveBeenCalledWith(transactionProps.marketplaceGatewayId);
    expect(marketplaceGateway.addToBalance).toHaveBeenCalledWith(transactionProps.amount);
    expect(marketplaceGatewayRepository.update).toHaveBeenCalledWith(marketplaceGateway);
  });
});
