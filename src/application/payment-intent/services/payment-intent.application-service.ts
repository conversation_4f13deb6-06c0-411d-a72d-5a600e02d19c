import { Inject, Injectable } from '@nestjs/common';

import { AccountRepositoryPort } from '@/domain/account/ports';
import { ACCOUNT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import {
  AccountDoesNotExistError,
  GatewayAccountDoesNotExistError,
} from '@/domain/payment-intent/errors';
import { GatewayType } from '@/domain/shared';

@Injectable()
export class PaymentIntentApplicationService {
  constructor(
    @Inject(ACCOUNT_REPOSITORY_TOKEN) private readonly accountRepository: AccountRepositoryPort,
  ) {}

  async validateAccountsExist(
    marketplaceId: string,
    gatewayType: GatewayType,
    paymentSplitsData: { accountId: string }[],
  ) {
    const accountIds = paymentSplitsData.map((split) => split.accountId);

    const accounts = await this.accountRepository.findByMarketplaceAndAccountIds(
      marketplaceId,
      accountIds,
    );

    accountIds.forEach((accountId) => {
      const account = accounts.find((x) => accountId === x.getId());

      if (!account) {
        throw new AccountDoesNotExistError(`Account ${accountId} does not exist`);
      }

      const gatewayAccount = account
        .getProps()
        .gatewayAccounts.find((x) => x.getProps().gateway.getType() === gatewayType);

      if (!gatewayAccount) {
        throw new GatewayAccountDoesNotExistError(
          `${gatewayType.toUpperCase()} gateway account for ${accountId} does not exist`,
        );
      }
    });
  }
}
