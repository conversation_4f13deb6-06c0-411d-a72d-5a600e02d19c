import { Command } from '@/core/ddd';
import {
  GatewayType,
  AllowedCurrencies,
  PaymentMethodType,
  GatewayMetadata,
} from '@/domain/shared';

type PaymentSplitData = {
  accountId: string;
  amount: number;
};

type Props = {
  marketplaceId: string;
  gatewayType: GatewayType;
  amount: number;
  currency: AllowedCurrencies;
  customerId: string;
  paymentMethodType: PaymentMethodType;
  paymentSplitsData: PaymentSplitData[];
  businessEntityId: string;
  statementDescriptor?: string;
  metadata?: GatewayMetadata;
  marketplaceOrderFee: number;
  platformOrderFee: number;
  feeSettingsId?: string | null;
};

export class CreatePaymentIntentCommand extends Command<Props> {}
