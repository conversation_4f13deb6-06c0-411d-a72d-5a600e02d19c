import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import {
  FEE_SETTINGS_REPOSITORY_TOKEN,
  GATEWAY_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { FeeSettingsRepositoryPort } from '@/domain/fee-settings/ports';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { MarketplaceGatewayRepositoryPort } from '@/domain/marketplace-gateway/ports';
import { PaymentIntent } from '@/domain/payment-intent/payment-intent';
import { PaymentSplit } from '@/domain/payment-intent/payment-split.value-object';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';
import { PaymentIntentDomainService } from '@/domain/payment-intent/services';
import { GatewayType, Money } from '@/domain/shared';

import { CreatePaymentIntentCommand } from './create-payment-intent.command';
import { PaymentIntentApplicationService } from '../../services';

@CommandHandler(CreatePaymentIntentCommand)
export class CreatePaymentIntentCommandHandler implements ICommandHandler {
  constructor(
    private readonly paymentIntentDomainService: PaymentIntentDomainService,
    private readonly db: DatabaseService,
    @Inject(GATEWAY_REPOSITORY_TOKEN)
    private readonly gatewayRepository: GatewayRepositoryPort,
    @Inject(PAYMENT_INTENT_REPOSITORY_TOKEN)
    private readonly paymentIntentRepository: PaymentIntentRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayRepository: MarketplaceGatewayRepositoryPort,
    @Inject(FEE_SETTINGS_REPOSITORY_TOKEN)
    private readonly feeSettingsRepository: FeeSettingsRepositoryPort,
    private readonly paymentIntentAppService: PaymentIntentApplicationService,
  ) {}

  async execute(command: CreatePaymentIntentCommand): Promise<PaymentIntent> {
    const {
      marketplaceId,
      gatewayType,
      amount,
      currency,
      paymentMethodType,
      customerId,
      paymentSplitsData,
      businessEntityId,
      statementDescriptor,
      metadata,
      platformOrderFee,
      marketplaceOrderFee,
    } = command.getProps();

    const gateway = await this.getGateway(gatewayType);

    const marketplaceGateway = await this.getMarketplaceGateway(marketplaceId, gateway.getId());

    await this.paymentIntentAppService.validateAccountsExist(
      marketplaceId,
      gatewayType,
      paymentSplitsData,
    );

    const feeSettings = await this.getFeeSettings(
      marketplaceGateway.getId(),
      command.getProps().feeSettingsId,
    );

    return this.db.$transaction(async () => {
      const paymentIntent = await this.paymentIntentDomainService.create({
        marketplaceId,
        gateway,
        customerId,
        amount: Money.from(amount, currency),
        paymentMethodType,
        paymentSplits: paymentSplitsData.map(
          (splitData) =>
            new PaymentSplit({
              accountId: splitData.accountId,
              amount: Money.from(splitData.amount, currency),
            }),
        ),
        businessEntityId,
        statementDescriptor,
        metadata,
        platformPaymentFeeFixed: feeSettings.getProps().platformFeeFixed,
        platformPaymentFeePercentage: feeSettings.getProps().platformFeePercentage,
        marketplacePaymentFeeFixed: feeSettings.getProps().marketplaceFeeFixed,
        marketplacePaymentFeePercentage: feeSettings.getProps().marketplaceFeePercentage,
        providerFeeFixed: gateway.getProps().feeFixed,
        providerFeePercentage: gateway.getProps().feePercentage,
        marketplaceOrderFee: Money.from(marketplaceOrderFee, currency),
        platformOrderFee: Money.from(platformOrderFee, currency),
        feeSettingsId: feeSettings.getId(),
      });

      await this.paymentIntentRepository.create(paymentIntent);

      return paymentIntent;
    });
  }

  async getMarketplaceGateway(marketplaceId: string, gatewayId: string) {
    const marketplaceGateway =
      await this.marketplaceGatewayRepository.findByMarketplaceAndGatewayId({
        marketplaceId,
        gatewayId,
      });

    if (!marketplaceGateway) {
      throw new Error('Marketplace gateway is not found');
    }

    return marketplaceGateway;
  }

  async getGateway(gatewayType: GatewayType) {
    const gateway = await this.gatewayRepository.findByGatewayType(gatewayType);

    if (!gateway) {
      throw new Error('Invalid gateway type'); // todo: custom error
    }

    return gateway;
  }

  async getFeeSettings(marketplaceGatewayId: string, feeSettingsId?: string | null) {
    const all = await this.feeSettingsRepository.findByMarketplaceGatewayId(marketplaceGatewayId);

    const target = feeSettingsId
      ? all.find((fs) => fs.getId() === feeSettingsId)
      : all.find((fs) => fs.getProps().isDefault);

    if (!target) {
      throw new Error(feeSettingsId ? 'Fee settings not found' : 'Default fee settings not found');
    }

    return target;
  }
}
