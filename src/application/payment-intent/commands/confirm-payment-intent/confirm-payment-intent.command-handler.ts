import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { PAYMENT_INTENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PaymentIntent } from '@/domain/payment-intent/payment-intent';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';
import { PaymentIntentDomainService } from '@/domain/payment-intent/services';

import { ConfirmPaymentIntentCommand } from './confirm-payment-intent.command';

@CommandHandler(ConfirmPaymentIntentCommand)
export class ConfirmPaymentIntentCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(PAYMENT_INTENT_REPOSITORY_TOKEN)
    private readonly paymentIntentRepository: PaymentIntentRepositoryPort,
    private readonly paymentIntentDomainService: PaymentIntentDomainService,
  ) {}

  async execute(command: ConfirmPaymentIntentCommand): Promise<PaymentIntent> {
    const { paymentIntentId, paymentMethodId } = command.getProps();

    return this.db.$transaction(async () => {
      const paymentIntent = await this.paymentIntentRepository.findById(paymentIntentId);

      await this.paymentIntentDomainService.confirm({
        paymentIntent,
        paymentMethodId,
      });

      await this.paymentIntentRepository.update(paymentIntent);

      return paymentIntent;
    });
  }
}
