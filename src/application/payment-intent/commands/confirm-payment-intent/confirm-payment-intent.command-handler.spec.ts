import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import { PAYMENT_INTENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PaymentIntent } from '@/domain/payment-intent/payment-intent';
import { PaymentIntentDomainService } from '@/domain/payment-intent/services';
import { PaymentIntentRepositoryMock } from '@/test/mocks/repositories/payment-intent-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import { PaymentIntentDomainServiceMock } from '@/test/mocks/services/payment-intent-domain-service.mock';

import { ConfirmPaymentIntentCommand, ConfirmPaymentIntentCommandHandler } from '.';

describe('ConfirmPaymentIntentCommandHandler', () => {
  let handler: ConfirmPaymentIntentCommandHandler;
  let db: DatabaseServiceMock;
  let paymentIntentRepository: PaymentIntentRepositoryMock;
  let paymentIntentDomainService: PaymentIntentDomainService;
  let commandProps: ConfirmPaymentIntentCommand['props'];
  let paymentIntent: PaymentIntent;
  let findPaymentIntentSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfirmPaymentIntentCommandHandler,
        { provide: PAYMENT_INTENT_REPOSITORY_TOKEN, useClass: PaymentIntentRepositoryMock },
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: PaymentIntentDomainService, useClass: PaymentIntentDomainServiceMock },
      ],
    }).compile();

    handler = module.get<ConfirmPaymentIntentCommandHandler>(ConfirmPaymentIntentCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    paymentIntentRepository = module.get<PaymentIntentRepositoryMock>(
      PAYMENT_INTENT_REPOSITORY_TOKEN,
    );
    paymentIntentDomainService = module.get<PaymentIntentDomainService>(PaymentIntentDomainService);
    [paymentIntent] = paymentIntentRepository.db;
    commandProps = {
      paymentIntentId: paymentIntent.getId(),
      paymentMethodId: 'payment-method-id',
    };
    findPaymentIntentSpy = jest.spyOn(paymentIntentRepository, 'findById');
    paymentIntentDomainService.confirm = jest.fn();
    paymentIntentRepository.update = jest.fn();

    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should seek payment intent by id', async () => {
    const command = new ConfirmPaymentIntentCommand(commandProps);

    await handler.execute(command);

    expect(findPaymentIntentSpy).toHaveBeenCalledWith(commandProps.paymentIntentId);
  });

  it('should confirm and update payment intent', async () => {
    const command = new ConfirmPaymentIntentCommand(commandProps);

    await handler.execute(command);

    expect(paymentIntentDomainService.confirm).toHaveBeenCalledWith({
      paymentIntent,
      paymentMethodId: commandProps.paymentMethodId,
    });
    expect(paymentIntentRepository.update).toHaveBeenCalledWith(paymentIntent);
    const [confirmInvOrder] = (paymentIntentDomainService.confirm as jest.Mock).mock
      .invocationCallOrder;
    const [updateInvOrder] = (paymentIntentRepository.update as jest.Mock).mock.invocationCallOrder;
    expect(confirmInvOrder).toBeLessThan(updateInvOrder);
  });

  it('should operate within a transaction', async () => {
    const command = new ConfirmPaymentIntentCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(findPaymentIntentSpy).not.toHaveBeenCalled();
    expect(paymentIntentDomainService.confirm).not.toHaveBeenCalled();
    expect(paymentIntentRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    const result = await db.invokeNextTransaction();

    expect(findPaymentIntentSpy).toHaveBeenCalledWith(commandProps.paymentIntentId);
    expect(paymentIntentDomainService.confirm).toHaveBeenCalledWith({
      paymentIntent,
      paymentMethodId: commandProps.paymentMethodId,
    });
    expect(paymentIntentRepository.update).toHaveBeenCalledWith(paymentIntent);
    expect(result).toBe(paymentIntent);
  });
});
