import { Test, TestingModule } from '@nestjs/testing';

import { PaymentIntentApplicationService } from '@/application/payment-intent/services';
import { DatabaseService } from '@/database';
import {
  GATEWAY_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { Gateway } from '@/domain/gateway/gateway';
import { MarketplaceGateway } from '@/domain/marketplace-gateway/marketplace-gateway';
import { PaymentIntent, PaymentIntentUpdateType } from '@/domain/payment-intent/payment-intent';
import { PaymentSplit } from '@/domain/payment-intent/payment-split.value-object';
import { PaymentIntentDomainService } from '@/domain/payment-intent/services';
import { AllowedCurrencies, Money } from '@/domain/shared';
import { GatewayRepositoryMock } from '@/test/mocks/repositories/gateway-repository.mock';
import {
  MarketplaceGatewayRepositoryMock,
} from '@/test/mocks/repositories/marketplace-gateway-repository.mock';
import {
  PaymentIntentRepositoryMock,
} from '@/test/mocks/repositories/payment-intent-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import {
  PaymentIntentApplicationServiceMock,
} from '@/test/mocks/services/payment-intent-application-service.mock';
import {
  PaymentIntentDomainServiceMock,
} from '@/test/mocks/services/payment-intent-domain-service.mock';

import {
  UpdatePaymentIntentCommand,
  UpdatePaymentIntentCommandHandler,
  UpdateWithAmountProps,
} from '.';

describe('UpdatePaymentIntentCommandHandler', () => {
  let handler: UpdatePaymentIntentCommandHandler;
  let gatewayRepository: GatewayRepositoryMock;
  let marketplaceGatewayRepository: MarketplaceGatewayRepositoryMock;
  let paymentIntentRepository: PaymentIntentRepositoryMock;
  let paymentIntentDomainService: PaymentIntentDomainService;
  let paymentIntentAppService: PaymentIntentApplicationService;
  let commandProps: UpdatePaymentIntentCommand['props'];
  let gateway: Gateway;
  let marketplaceGateway: MarketplaceGateway;
  let paymentIntent: PaymentIntent;
  let findMarketplaceGatewaySpy: jest.SpyInstance;
  let paymentIntentDomainServiceUpdateSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdatePaymentIntentCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: PaymentIntentDomainService, useClass: PaymentIntentDomainServiceMock },
        { provide: PaymentIntentApplicationService, useClass: PaymentIntentApplicationServiceMock },
        { provide: GATEWAY_REPOSITORY_TOKEN, useClass: GatewayRepositoryMock },
        { provide: PAYMENT_INTENT_REPOSITORY_TOKEN, useClass: PaymentIntentRepositoryMock },
        { provide: MARKETPLACE_GATEWAY_REPOSITORY_TOKEN, useClass: MarketplaceGatewayRepositoryMock },
      ],
    }).compile();

    handler = module.get<UpdatePaymentIntentCommandHandler>(UpdatePaymentIntentCommandHandler);
    gatewayRepository = module.get<GatewayRepositoryMock>(GATEWAY_REPOSITORY_TOKEN);
    marketplaceGatewayRepository = module.get<MarketplaceGatewayRepositoryMock>(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN);
    paymentIntentRepository = module.get<PaymentIntentRepositoryMock>(PAYMENT_INTENT_REPOSITORY_TOKEN);
    paymentIntentDomainService = module.get<PaymentIntentDomainService>(PaymentIntentDomainService);
    paymentIntentAppService = module.get<PaymentIntentApplicationService>(PaymentIntentApplicationService);
    [gateway] = gatewayRepository.db;
    [marketplaceGateway] = marketplaceGatewayRepository.db;
    [paymentIntent] = paymentIntentRepository.db;
    commandProps = {
      amount: 10,
      businessEntityId: 'business-entity-id',
      currency: AllowedCurrencies.USD,
      marketplaceId: marketplaceGateway.getProps().marketplaceId,
      marketplaceOrderFee: 1,
      metadata: { key: 'value' },
      paymentIntentId: paymentIntent.getId(),
      paymentMethodType: paymentIntent.getProps().paymentMethodType,
      paymentSplitsData: [
        { accountId: 'account-id-0', amount: 5 },
        { accountId: 'account-id-1', amount: 5 },
      ],
      platformOrderFee: 1,
      statementDescriptor: 'statement-descriptor',
      updateType: PaymentIntentUpdateType.WITH_AMOUNT,
    };
    findMarketplaceGatewaySpy = jest.spyOn(marketplaceGatewayRepository, 'findByMarketplaceAndGatewayId');
    paymentIntentDomainServiceUpdateSpy = jest.spyOn(paymentIntentDomainService, 'update').mockResolvedValue(paymentIntent);
    paymentIntentAppService.validateAccountsExist = jest.fn();
    paymentIntentRepository.update = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payment intent is not found', async () => {
    const command = new UpdatePaymentIntentCommand({
      ...commandProps,
      paymentIntentId: 'invalid-payment-intent-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Payment intent does not exist'));
  });

  it('should fail if gateway is not found', async () => {
    const command = new UpdatePaymentIntentCommand(commandProps);
    gatewayRepository.findById = jest.fn().mockResolvedValue(null);

    await expect(handler.execute(command)).rejects.toThrow(new Error('Gateway does not exist'));
  });

  it('should seek marketplace gateway by payment intent marketplace id', async () => {
    const command = new UpdatePaymentIntentCommand(commandProps);

    await handler.execute(command);

    expect(findMarketplaceGatewaySpy).toHaveBeenCalledWith({
      marketplaceId: paymentIntent.getProps().marketplaceId,
      gatewayId: gateway.getId(),
    });
  });

  it('should fail if marketplace gateway is not found', async () => {
    const command = new UpdatePaymentIntentCommand(commandProps);
    findMarketplaceGatewaySpy.mockResolvedValue(null);

    await expect(handler.execute(command)).rejects.toThrow(new Error('Marketplace gateway is not found'));
  });

  it('shouldn\'t validate payment splits if update type without amount', async () => {
    const command = new UpdatePaymentIntentCommand({
      ...commandProps,
      updateType: PaymentIntentUpdateType.WITHOUT_AMOUNT,
    });

    await handler.execute(command);

    expect(paymentIntentAppService.validateAccountsExist).not.toHaveBeenCalled();
  });

  it('should validate payment splits if update type with amount', async () => {
    const command = new UpdatePaymentIntentCommand(commandProps);

    await handler.execute(command);

    expect(paymentIntentAppService.validateAccountsExist).toHaveBeenCalledWith(
      paymentIntent.getProps().marketplaceId,
      paymentIntent.getProps().gateway.getType(),
      (commandProps as UpdateWithAmountProps).paymentSplitsData,
    );
  });

  it('should build correct update params for payment intent without amount', async () => {
    const command = new UpdatePaymentIntentCommand({
      ...commandProps,
      updateType: PaymentIntentUpdateType.WITHOUT_AMOUNT,
    });

    await handler.execute(command);

    expect(paymentIntentDomainServiceUpdateSpy).toHaveBeenCalledWith(paymentIntent, {
      updateType: PaymentIntentUpdateType.WITHOUT_AMOUNT,
      gateway,
      paymentMethodType: commandProps.paymentMethodType,
      businessEntityId: commandProps.businessEntityId,
      metadata: commandProps.metadata,
      statementDescriptor: commandProps.statementDescriptor,
    });
  });

  it('should build correct update params for payment intent with amount', async () => {
    const command = new UpdatePaymentIntentCommand(commandProps);

    await handler.execute(command);

    const props = commandProps as UpdateWithAmountProps;
    expect(paymentIntentDomainServiceUpdateSpy).toHaveBeenCalledWith(paymentIntent, {
      updateType: PaymentIntentUpdateType.WITH_AMOUNT,
      gateway,
      paymentMethodType: commandProps.paymentMethodType,
      businessEntityId: commandProps.businessEntityId,
      metadata: commandProps.metadata,
      statementDescriptor: commandProps.statementDescriptor,
      platformPaymentFeeFixed: marketplaceGateway.getProps().platformPaymentFeeFixed,
      platformPaymentFeePercentage: marketplaceGateway.getProps().platformPaymentFeePercentage,
      marketplacePaymentFeeFixed: marketplaceGateway.getProps().marketplacePaymentFeeFixed,
      marketplacePaymentFeePercentage: marketplaceGateway.getProps().marketplacePaymentFeePercentage,
      providerFeeFixed: gateway.getProps().feeFixed,
      providerFeePercentage: gateway.getProps().feePercentage,
      amount: Money.from(props.amount, props.currency),
      marketplaceOrderFee: Money.from(props.marketplaceOrderFee, props.currency),
      platformOrderFee: Money.from(props.platformOrderFee, props.currency),
      paymentSplits: props.paymentSplitsData.map((splitData) => new PaymentSplit({
        accountId: splitData.accountId,
        amount: Money.from(splitData.amount, props.currency),
      })),
    });
    expect(props.paymentSplitsData.length).toBeGreaterThan(0);
  });

  it('should update and return payment intent', async () => {
    const command = new UpdatePaymentIntentCommand(commandProps);

    const result = await handler.execute(command);

    expect(paymentIntentDomainServiceUpdateSpy).toHaveBeenCalled();
    const updatedPaymentIntent = await paymentIntentDomainServiceUpdateSpy.mock.results[0].value;
    expect(paymentIntentRepository.update).toHaveBeenCalledWith(updatedPaymentIntent);
    expect(result).toBe(updatedPaymentIntent);
  });
});
