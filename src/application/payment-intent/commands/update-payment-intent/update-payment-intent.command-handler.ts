import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import {
  GATEWAY_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { Gateway } from '@/domain/gateway/gateway';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { MarketplaceGateway } from '@/domain/marketplace-gateway/marketplace-gateway';
import { MarketplaceGatewayRepositoryPort } from '@/domain/marketplace-gateway/ports';
import { PaymentIntent, PaymentIntentUpdateType } from '@/domain/payment-intent/payment-intent';
import { PaymentSplit } from '@/domain/payment-intent/payment-split.value-object';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';
import {
  PaymentIntentDomainService,
  UpdatePaymentIntentParams,
} from '@/domain/payment-intent/services';
import { Money } from '@/domain/shared';

import { PaymentSplitData, UpdatePaymentIntentCommand } from './update-payment-intent.command';
import { PaymentIntentApplicationService } from '../../services';

type PaymentIntentFeesParams = {
  platformPaymentFeeFixed: Money;
  platformPaymentFeePercentage: number;
  marketplacePaymentFeeFixed: Money;
  marketplacePaymentFeePercentage: number;
  providerFeeFixed: Money;
  providerFeePercentage: number;
};

@CommandHandler(UpdatePaymentIntentCommand)
export class UpdatePaymentIntentCommandHandler implements ICommandHandler {
  constructor(
    private readonly paymentIntentDomainService: PaymentIntentDomainService,
    private readonly db: DatabaseService,
    @Inject(PAYMENT_INTENT_REPOSITORY_TOKEN)
    private readonly paymentIntentRepository: PaymentIntentRepositoryPort,
    @Inject(GATEWAY_REPOSITORY_TOKEN)
    private readonly gatewayRepository: GatewayRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayRepository: MarketplaceGatewayRepositoryPort,
    private readonly paymentIntentAppService: PaymentIntentApplicationService,
  ) {}

  async execute(command: UpdatePaymentIntentCommand): Promise<PaymentIntent> {
    const paymentIntent = await this.getPaymentIntent(command);

    const gateway = await this.getGateway(paymentIntent);

    const marketplaceGateway = await this.getMarketplaceGateway(
      paymentIntent.getProps().marketplaceId,
      gateway.getId(),
    );

    const commandProps = command.getProps();

    if (commandProps.updateType === PaymentIntentUpdateType.WITH_AMOUNT) {
      await this.validatePaymentSplits(paymentIntent, commandProps.paymentSplitsData);
    }

    return this.updatePaymentIntent({ paymentIntent, commandProps, gateway, marketplaceGateway });
  }

  private async updatePaymentIntent(data: {
    paymentIntent: PaymentIntent;
    commandProps: UpdatePaymentIntentCommand['props'];
    gateway: Gateway;
    marketplaceGateway: MarketplaceGateway;
  }): Promise<PaymentIntent> {
    const updateParams = this.buildUpdateParams(data);

    const updatedPaymentIntent = await this.paymentIntentDomainService.update(
      data.paymentIntent,
      updateParams,
    );

    await this.paymentIntentRepository.update(updatedPaymentIntent);

    return updatedPaymentIntent;
  }

  private buildUpdateParams({
    commandProps,
    gateway,
    marketplaceGateway,
  }: {
    commandProps: UpdatePaymentIntentCommand['props'];
    gateway: Gateway;
    marketplaceGateway: MarketplaceGateway;
  }): UpdatePaymentIntentParams {
    let params: UpdatePaymentIntentParams = {
      updateType: PaymentIntentUpdateType.WITHOUT_AMOUNT,
      gateway,
      paymentMethodType: commandProps.paymentMethodType,
      businessEntityId: commandProps.businessEntityId,
      metadata: commandProps.metadata,
      statementDescriptor: commandProps.statementDescriptor,
    };

    if (commandProps.updateType === PaymentIntentUpdateType.WITH_AMOUNT) {
      params = {
        ...params,
        ...this.mapFees(gateway, marketplaceGateway),
        updateType: PaymentIntentUpdateType.WITH_AMOUNT,
        amount: Money.from(commandProps.amount, commandProps.currency),
        marketplaceOrderFee: Money.from(commandProps.marketplaceOrderFee, commandProps.currency),
        platformOrderFee: Money.from(commandProps.platformOrderFee, commandProps.currency),
        paymentSplits:
          commandProps.paymentSplitsData?.map(
            (splitData) =>
              new PaymentSplit({
                accountId: splitData.accountId,
                amount: Money.from(splitData.amount, commandProps.currency),
              }),
          ) || [],
      };
    }

    return params;
  }

  private mapFees(
    gateway: Gateway,
    marketplaceGateway: MarketplaceGateway,
  ): PaymentIntentFeesParams {
    return {
      platformPaymentFeePercentage: marketplaceGateway.getProps().platformPaymentFeePercentage,
      platformPaymentFeeFixed: marketplaceGateway.getProps().platformPaymentFeeFixed,
      marketplacePaymentFeeFixed: marketplaceGateway.getProps().marketplacePaymentFeeFixed,
      marketplacePaymentFeePercentage:
        marketplaceGateway.getProps().marketplacePaymentFeePercentage,
      providerFeeFixed: gateway.getProps().feeFixed,
      providerFeePercentage: gateway.getProps().feePercentage,
    };
  }

  private async validatePaymentSplits(
    paymentIntent: PaymentIntent,
    paymentSplitsData: PaymentSplitData[],
  ): Promise<void> {
    await this.paymentIntentAppService.validateAccountsExist(
      paymentIntent.getProps().marketplaceId,
      paymentIntent.getProps().gateway.getType(),
      paymentSplitsData,
    );
  }

  private async getPaymentIntent(command: UpdatePaymentIntentCommand): Promise<PaymentIntent> {
    const { paymentIntentId } = command.getProps();
    const paymentIntent = await this.paymentIntentRepository.findById(paymentIntentId);

    if (!paymentIntent) {
      throw new Error('Payment intent does not exist'); // Consider using custom exceptions
    }

    return paymentIntent;
  }

  private async getGateway(paymentIntent: PaymentIntent): Promise<Gateway> {
    const gateway = await this.gatewayRepository.findById(
      paymentIntent.getProps().gateway.getGatewayId(),
    );
    if (!gateway) {
      throw new Error('Gateway does not exist'); // Consider using custom exceptions
    }
    return gateway;
  }

  async getMarketplaceGateway(marketplaceId: string, gatewayId: string) {
    const marketplaceGateway =
      await this.marketplaceGatewayRepository.findByMarketplaceAndGatewayId({
        marketplaceId,
        gatewayId,
      });

    if (!marketplaceGateway) {
      throw new Error('Marketplace gateway is not found');
    }

    return marketplaceGateway;
  }
}
