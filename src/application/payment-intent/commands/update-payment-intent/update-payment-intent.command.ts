import { Command } from '@/core/ddd';
import { PaymentIntentUpdateType } from '@/domain/payment-intent/payment-intent';
import { AllowedCurrencies, GatewayMetadata, PaymentMethodType } from '@/domain/shared';

export type PaymentSplitData = {
  accountId: string;
  amount: number;
};

export type UpdateWithoutAmountProps = {
  updateType: PaymentIntentUpdateType.WITHOUT_AMOUNT;
  paymentIntentId: string;
  marketplaceId: string;
  paymentMethodType?: PaymentMethodType;
  businessEntityId?: string;
  metadata?: GatewayMetadata;
  statementDescriptor?: string;
};

export type UpdateWithAmountProps = Omit<UpdateWithoutAmountProps, 'updateType'> & {
  updateType: PaymentIntentUpdateType.WITH_AMOUNT;
  amount: number;
  currency: AllowedCurrencies;
  marketplaceOrderFee: number;
  platformOrderFee: number;
  paymentSplitsData: PaymentSplitData[];
};

type Props = UpdateWithAmountProps | UpdateWithoutAmountProps;

export class UpdatePaymentIntentCommand extends Command<Props> {}
