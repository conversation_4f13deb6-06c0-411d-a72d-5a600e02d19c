import { ConfirmPaymentIntentCommandHandler } from './commands/confirm-payment-intent';
import { CreatePaymentIntentCommandHandler } from './commands/create-payment-intent';
import { UpdatePaymentIntentCommandHandler } from './commands/update-payment-intent';
import { FindPaymentIntentByIdQueryHandler } from './queries/find-payment-intent-by-id.query';
import { FindPaymentIntentByPaymentIdQueryHandler } from './queries/find-payment-intent-by-payment-id.query';
import { PaymentIntentApplicationService } from './services';

export const PAYMENT_INTENT_COMMAND_HANDLERS = [
  CreatePaymentIntentCommandHandler,
  ConfirmPaymentIntentCommandHandler,
  UpdatePaymentIntentCommandHandler,
];

export const PAYMENT_INTENT_QUERY_HANDLERS = [
  FindPaymentIntentByIdQueryHand<PERSON>,
  FindPaymentIntentByPaymentIdQueryHandler,
];

export const PAYMENT_INTENT_APPLICATION_SERVICES = [PaymentIntentApplicationService];
