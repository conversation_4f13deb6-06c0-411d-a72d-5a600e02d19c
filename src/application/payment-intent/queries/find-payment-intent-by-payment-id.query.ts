import { Inject } from '@nestjs/common';
import { <PERSON>Q<PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';

import { ClientPaymentIntentResponseDto } from '@/api/payment-intent/dto';
import { PAYMENT_INTENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';

export class FindPaymentIntentByPaymentIdQuery {
  readonly paymentId: string;
  readonly marketplaceId: string;

  constructor(props: FindPaymentIntentByPaymentIdQuery) {
    this.marketplaceId = props.marketplaceId;
    this.paymentId = props.paymentId;
  }
}

@QueryHandler(FindPaymentIntentByPaymentIdQuery)
export class FindPaymentIntentByPaymentIdQueryHandler implements IQueryHandler {
  constructor(
    @Inject(PAYMENT_INTENT_REPOSITORY_TOKEN)
    private readonly paymentIntentRepository: PaymentIntentRepositoryPort,
  ) {}

  async execute(query: FindPaymentIntentByPaymentIdQuery) {
    const paymentIntent = await this.paymentIntentRepository.findByPaymentIdAndMarketplaceId(
      query.paymentId,
      query.marketplaceId,
    );

    return new ClientPaymentIntentResponseDto(paymentIntent);
  }
}
