import { Inject } from '@nestjs/common';
import { IQ<PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { PAYMENT_INTENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';

export class FindPaymentIntentByIdQuery {
  readonly paymentIntentId: string;
  readonly marketplaceId: string;

  constructor(props: FindPaymentIntentByIdQuery) {
    this.marketplaceId = props.marketplaceId;
    this.paymentIntentId = props.paymentIntentId;
  }
}

@QueryHandler(FindPaymentIntentByIdQuery)
export class FindPaymentIntentByIdQueryHandler implements IQueryHandler {
  constructor(
    @Inject(PAYMENT_INTENT_REPOSITORY_TOKEN)
    private readonly paymentIntentRepository: PaymentIntentRepositoryPort,
  ) {}

  async execute(query: FindPaymentIntentByIdQuery) {
    return this.paymentIntentRepository.findByIdAndMarketplaceId(
      query.paymentIntentId,
      query.marketplaceId,
    );
  }
}
