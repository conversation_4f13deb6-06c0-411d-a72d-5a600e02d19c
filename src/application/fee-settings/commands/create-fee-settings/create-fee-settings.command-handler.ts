import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import {
  FEE_SETTINGS_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { FeeSettings } from '@/domain/fee-settings/fee-settings';
import { FeeSettingsRepositoryPort } from '@/domain/fee-settings/ports';
import { FeeSettingsDomainService } from '@/domain/fee-settings/services/fee-settings.domain-service';
import { MarketplaceGatewayRepositoryPort } from '@/domain/marketplace-gateway/ports';
import { Money } from '@/domain/shared';

import { CreateFeeSettingsCommand } from './create-fee-settings.command';

@CommandHandler(CreateFeeSettingsCommand)
export class CreateFeeSettingsCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayRepository: MarketplaceGatewayRepositoryPort,
    @Inject(FEE_SETTINGS_REPOSITORY_TOKEN)
    private readonly feeSettingsRepository: FeeSettingsRepositoryPort,
    private readonly feeSettingsDomainService: FeeSettingsDomainService,
  ) {}

  async execute(command: CreateFeeSettingsCommand): Promise<FeeSettings> {
    const props = command.getProps();

    const marketplaceGateway = await this.marketplaceGatewayRepository.findById(
      props.marketplaceGatewayId,
    );

    if (!marketplaceGateway) {
      throw new Error('Entity not found'); // todo: custom error
    }

    const existingFeeSettings = await this.feeSettingsRepository.findByMarketplaceGatewayId(
      props.marketplaceGatewayId,
    );

    const feeSettings = this.feeSettingsDomainService.createFeeSettings({
      marketplaceGatewayFeeSettings: existingFeeSettings,
      platformFeeFixed: Money.from(props.platformFeeFixed, props.currency),
      platformFeePercentage: props.platformFeePercentage,
      marketplaceFeeFixed: Money.from(props.marketplaceFeeFixed, props.currency),
      marketplaceFeePercentage: props.marketplaceFeePercentage,
      marketplaceGatewayId: props.marketplaceGatewayId,
      isDefault: props.isDefault,
    });

    return this.db.$transaction(async () => {
      await this.feeSettingsRepository.create(feeSettings);

      return feeSettings;
    });
  }
}
