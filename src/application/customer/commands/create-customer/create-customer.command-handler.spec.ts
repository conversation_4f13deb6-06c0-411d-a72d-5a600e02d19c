import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import { Customer } from '@/domain/customer/customer';
import { MARKETPLACE_REPOSITORY_TOKEN, CUSTOMER_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { CustomerRepositoryMock } from '@/test/mocks/repositories/customer-repository.mock';
import { MarketplaceRepositoryMock } from '@/test/mocks/repositories/marketplace-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

import { CreateCustomerCommand, CreateCustomerCommandHandler } from '.';

describe('CreateCustomerCommandHandler', () => {
  let handler: CreateCustomerCommandHandler;
  let db: DatabaseServiceMock;
  let marketplaceRepository: MarketplaceRepositoryMock;
  let customerRepository: CustomerRepositoryMock;
  let commandProps: CreateCustomerCommand['props'];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateCustomerCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: MARKETPLACE_REPOSITORY_TOKEN, useClass: MarketplaceRepositoryMock },
        { provide: CUSTOMER_REPOSITORY_TOKEN, useClass: CustomerRepositoryMock },
      ],
    }).compile();

    handler = module.get<CreateCustomerCommandHandler>(CreateCustomerCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    marketplaceRepository = module.get<MarketplaceRepositoryMock>(MARKETPLACE_REPOSITORY_TOKEN);
    customerRepository = module.get<CustomerRepositoryMock>(CUSTOMER_REPOSITORY_TOKEN);
    commandProps = {
      marketplaceId: marketplaceRepository.db[0].getId(),
      name: 'Test Customer',
      email: '<EMAIL>',
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if marketplace is not found', async () => {
    const marketplaceId = 'missing-id';
    const command = new CreateCustomerCommand({ ...commandProps, marketplaceId });
    const findMarketplaceSpy = jest.spyOn(marketplaceRepository, 'findById');

    await expect(handler.execute(command)).rejects.toThrow(new Error('Entity not found'));
    expect(findMarketplaceSpy).toHaveBeenCalledWith(marketplaceId);
  });

  it('should create a customer within transaction', async () => {
    const command = new CreateCustomerCommand(commandProps);
    const [customer] = customerRepository.db;
    const customerCreateSpy = jest.spyOn(Customer, 'create').mockReturnValue(customer);
    customerRepository.create = jest.fn();

    await handler.execute(command);

    expect(customerCreateSpy).not.toHaveBeenCalled();
    expect(customerRepository.create).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    const result = await db.invokeNextTransaction();

    expect(customerCreateSpy).toHaveBeenCalledWith(command.getProps());
    expect(customerRepository.create).toHaveBeenCalledWith(customer);
    expect(result).toBe(customer);
  });
});
