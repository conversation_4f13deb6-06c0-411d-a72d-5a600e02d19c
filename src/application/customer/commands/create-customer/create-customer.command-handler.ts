import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { Customer } from '@/domain/customer/customer';
import { CustomerRepositoryPort } from '@/domain/customer/ports';
import { MARKETPLACE_REPOSITORY_TOKEN, CUSTOMER_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { MarketplaceRepositoryPort } from '@/domain/marketplace/ports';

import { CreateCustomerCommand } from './create-customer.command';

@CommandHandler(CreateCustomerCommand)
export class CreateCustomerCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(MARKETPLACE_REPOSITORY_TOKEN)
    private readonly marketplaceRepository: MarketplaceRepositoryPort,
    @Inject(CUSTOMER_REPOSITORY_TOKEN)
    private readonly customerRepository: CustomerRepositoryPort,
  ) {}

  async execute(command: CreateCustomerCommand): Promise<Customer> {
    const { email, marketplaceId, name } = command.getProps();

    const marketplace = await this.marketplaceRepository.findById(marketplaceId);

    if (!marketplace) {
      throw new Error('Entity not found'); // todo: custom error
    }

    return this.db.$transaction(async () => {
      const customer = Customer.create({
        name,
        email,
        marketplaceId,
      });

      await this.customerRepository.create(customer);

      return customer;
    });
  }
}
