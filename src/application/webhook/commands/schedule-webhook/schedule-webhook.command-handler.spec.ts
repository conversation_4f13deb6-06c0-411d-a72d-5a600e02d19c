import { Test, TestingModule } from '@nestjs/testing';

import { WebhookData } from '@/application/webhook/interfaces/webhook-event.interface';
import { DatabaseService } from '@/database';
import {
  GATEWAY_REPOSITORY_TOKEN,
  MARKETPLACE_REPOSITORY_TOKEN,
  WEBHOOK_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { Gateway } from '@/domain/gateway/gateway';
import { Marketplace } from '@/domain/marketplace/marketplace';
import { Gateway as SharedGateway } from '@/domain/shared';
import { Webhook, WebhookObject, WebhookType } from '@/domain/webhook/webhook';
import { GatewayRepositoryMock } from '@/test/mocks/repositories/gateway-repository.mock';
import { MarketplaceRepositoryMock } from '@/test/mocks/repositories/marketplace-repository.mock';
import { WebhookRepositoryMock } from '@/test/mocks/repositories/webhook-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

import { ScheduleWebhookCommand } from './schedule-webhook.command';
import { ScheduleWebhookCommandHandler } from './schedule-webhook.command-handler';

describe('ScheduleWebhookCommandHandler', () => {
  let handler: ScheduleWebhookCommandHandler;
  let db: DatabaseServiceMock;
  let webhookRepository: WebhookRepositoryMock;
  let gatewayRepository: GatewayRepositoryMock;
  let marketplaceRepository: MarketplaceRepositoryMock;
  let commandProps: ScheduleWebhookCommand['props'];
  let gateway: Gateway;
  let marketplace: Marketplace;
  let webhook: Webhook;
  let webhookCreateSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduleWebhookCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: WEBHOOK_REPOSITORY_TOKEN, useClass: WebhookRepositoryMock },
        { provide: GATEWAY_REPOSITORY_TOKEN, useClass: GatewayRepositoryMock },
        { provide: MARKETPLACE_REPOSITORY_TOKEN, useClass: MarketplaceRepositoryMock },
      ],
    }).compile();

    handler = module.get<ScheduleWebhookCommandHandler>(ScheduleWebhookCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    webhookRepository = module.get<WebhookRepositoryMock>(WEBHOOK_REPOSITORY_TOKEN);
    gatewayRepository = module.get<GatewayRepositoryMock>(GATEWAY_REPOSITORY_TOKEN);
    marketplaceRepository = module.get<MarketplaceRepositoryMock>(MARKETPLACE_REPOSITORY_TOKEN);
    [gateway] = gatewayRepository.db;
    [marketplace] = marketplaceRepository.db;
    [webhook] = webhookRepository.db;
    commandProps = {
      gatewayId: gateway.getId(),
      marketplaceId: marketplace.getId(),
      type: WebhookType.DISPUTE_CREATED,
      object: WebhookObject.DISPUTE,
      data: { id: 'dispute-id' } as WebhookData,
    };
    webhookCreateSpy = jest.spyOn(Webhook, 'create').mockReturnValue(webhook);
    webhookRepository.create = jest.fn();
    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if gateway is not found', async () => {
    const command = new ScheduleWebhookCommand({
      ...commandProps,
      gatewayId: 'invalid-gateway-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Entity not found'));
  });

  it('should fail if marketplace is not found', async () => {
    const command = new ScheduleWebhookCommand({
      ...commandProps,
      marketplaceId: 'invalid-marketplace-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Entity not found'));
  });

  it('should create a webhook', async () => {
    const command = new ScheduleWebhookCommand(commandProps);

    await handler.execute(command);

    expect(webhookCreateSpy).toHaveBeenCalledWith({
      data: commandProps.data,
      object: commandProps.object,
      type: commandProps.type,
      url: marketplace.getProps().webhookEndpoint.getProps().webhookUrl,
      marketplaceId: commandProps.marketplaceId,
      gateway: new SharedGateway(gateway.getId(), gateway.getProps().type),
    });
    expect(webhookRepository.create).toHaveBeenCalledWith(webhook);
  });

  it('should operate within a transaction', async () => {
    const command = new ScheduleWebhookCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(webhookCreateSpy).not.toHaveBeenCalled();
    expect(webhookRepository.create).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    expect(webhookCreateSpy).toHaveBeenCalled();
    expect(webhookRepository.create).toHaveBeenCalled();
  });
});
