import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import {
  GATEWAY_REPOSITORY_TOKEN,
  MARKETPLACE_REPOSITORY_TOKEN,
  WEBHOOK_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { MarketplaceRepositoryPort } from '@/domain/marketplace/ports/marketplace-repository.port';
import { Gateway } from '@/domain/shared';
import { WebhookRepositoryPort } from '@/domain/webhook/ports/webhook-repository.port';
import { Webhook } from '@/domain/webhook/webhook';

import { ScheduleWebhookCommand } from './schedule-webhook.command';

@CommandHandler(ScheduleWebhookCommand)
export class ScheduleWebhookCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(WEBHOOK_REPOSITORY_TOKEN) private readonly webhookRepository: WebhookRepositoryPort,
    @Inject(GATEWAY_REPOSITORY_TOKEN) private readonly gatewayRepository: GatewayRepositoryPort,
    @Inject(MARKETPLACE_REPOSITORY_TOKEN)
    private readonly marketplaceRepository: MarketplaceRepositoryPort,
  ) {}

  async execute(command: ScheduleWebhookCommand): Promise<void> {
    const { marketplaceId, gatewayId, type, data, object } = command.getProps();

    const gateway = await this.gatewayRepository.findById(gatewayId);

    if (!gateway) {
      throw new Error('Entity not found'); // todo: custom error
    }

    const marketplace = await this.marketplaceRepository.findById(marketplaceId);

    if (!marketplace) {
      throw new Error('Entity not found'); // todo: custom error
    }

    return this.db.$transaction(async () => {
      const { webhookEndpoint } = marketplace.getProps();

      const { webhookUrl } = webhookEndpoint.getProps();

      const webhook = Webhook.create({
        data,
        object,
        type,
        url: webhookUrl,
        marketplaceId,
        gateway: new Gateway(gateway.getId(), gateway.getProps().type),
      });

      await this.webhookRepository.create(webhook);
    });
  }
}
