import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import {
  MARKETPLACE_REPOSITORY_TOKEN,
  WEBHOOK_REPOSITORY_TOKEN,
  WEBHOOK_SENDER_SERVICE_TOKEN,
} from '@/domain/di-tokens';
import { MarketplaceRepositoryPort } from '@/domain/marketplace/ports/marketplace-repository.port';
import { WebhookRepositoryPort } from '@/domain/webhook/ports/webhook-repository.port';
import { WebhookSenderServicePort } from '@/domain/webhook/ports/webhook-sender-service.port';

import { SendWebhookCommand } from './send-webhook.command';

@CommandHandler(SendWebhookCommand)
export class SendWebhookCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(WEBHOOK_SENDER_SERVICE_TOKEN) private readonly webhookSender: WebhookSenderServicePort,
    @Inject(WEBHOOK_REPOSITORY_TOKEN) private readonly webhookRepository: WebhookRepositoryPort,
    @Inject(MARKETPLACE_REPOSITORY_TOKEN)
    private readonly marketplaceRepository: MarketplaceRepositoryPort,
  ) {}

  async execute(command: SendWebhookCommand): Promise<void> {
    const { webhookId } = command.getProps();

    const webhook = await this.webhookRepository.findById(webhookId);

    if (!webhook) {
      throw new Error('Entity not found'); // todo: custom error
    }

    const marketplace = await this.marketplaceRepository.findById(webhook.getProps().marketplaceId);

    if (!marketplace) {
      throw new Error('Entity not found'); // todo: custom error
    }

    const webhookSecret = marketplace.getProps().webhookEndpoint.getWebhookSecret();

    const response = await this.webhookSender.send(webhook, webhookSecret);

    return this.db.$transaction(async () => {
      if (response.hasFailed) {
        webhook.recordFailure(response.failReason);
      } else {
        webhook.markAsDelivered();
      }

      if (response.hasFailed && webhook.canRetry()) {
        webhook.scheduleRetry();
      }

      await this.webhookRepository.update(webhook);
    });
  }
}
