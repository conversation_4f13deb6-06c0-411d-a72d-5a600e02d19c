import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import {
  MARKETPLACE_REPOSITORY_TOKEN,
  WEBHOOK_REPOSITORY_TOKEN,
  WEBHOOK_SENDER_SERVICE_TOKEN
} from '@/domain/di-tokens';
import { Marketplace } from '@/domain/marketplace/marketplace';
import { WebhookSenderServicePort } from '@/domain/webhook/ports';
import { Webhook } from '@/domain/webhook/webhook';
import { MarketplaceRepositoryMock } from '@/test/mocks/repositories/marketplace-repository.mock';
import { WebhookRepositoryMock } from '@/test/mocks/repositories/webhook-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import { WebhookSenderServiceMock } from '@/test/mocks/services/webhook-sender-service.mock';

import { SendWebhookCommand } from './send-webhook.command';
import { SendWebhookCommandHandler } from './send-webhook.command-handler';

describe('SendWebhookCommandHandler', () => {
  let handler: SendWebhookCommandHandler;
  let db: DatabaseServiceMock;
  let webhookRepository: WebhookRepositoryMock;
  let marketplaceRepository: MarketplaceRepositoryMock;
  let webhookSender: WebhookSenderServicePort;
  let commandProps: SendWebhookCommand['props'];
  let webhook: Webhook;
  let marketplace: Marketplace;
  let webhookSenderSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SendWebhookCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: WEBHOOK_REPOSITORY_TOKEN, useClass: WebhookRepositoryMock },
        { provide: MARKETPLACE_REPOSITORY_TOKEN, useClass: MarketplaceRepositoryMock },
        { provide: WEBHOOK_SENDER_SERVICE_TOKEN, useClass: WebhookSenderServiceMock },
      ],
    }).compile();

    handler = module.get<SendWebhookCommandHandler>(SendWebhookCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    webhookRepository = module.get<WebhookRepositoryMock>(WEBHOOK_REPOSITORY_TOKEN);
    marketplaceRepository = module.get<MarketplaceRepositoryMock>(MARKETPLACE_REPOSITORY_TOKEN);
    webhookSender = module.get<WebhookSenderServicePort>(WEBHOOK_SENDER_SERVICE_TOKEN);
    [webhook] = webhookRepository.db;
    [marketplace] = marketplaceRepository.db;
    commandProps = {
      webhookId: webhook.getId(),
    };
    webhookSenderSpy = jest.spyOn(webhookSender, 'send').mockResolvedValue({
      hasFailed: false,
    });
    webhook.recordFailure = jest.fn();
    webhook.markAsDelivered = jest.fn();
    webhook.scheduleRetry = jest.fn();
    webhook.canRetry = jest.fn().mockReturnValue(false);
    webhookRepository.update = jest.fn();

    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if webhook is not found', async () => {
    const command = new SendWebhookCommand({
      webhookId: 'invalid-webhook-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Entity not found'));
  });

  it('should fail if marketplace is not found', async () => {
    const command = new SendWebhookCommand(commandProps);
    webhookRepository.findById = jest.fn().mockResolvedValue(new Webhook({
      id: webhook.getId(),
      props: {
        ...webhook.getProps(),
        marketplaceId: 'invalid-marketplace-id',
      },
    }));

    await expect(handler.execute(command)).rejects.toThrow(new Error('Entity not found'));
  });

  it('should send a webhook', async () => {
    const command = new SendWebhookCommand(commandProps);

    await handler.execute(command);

    expect(webhookSenderSpy).toHaveBeenCalledWith(
      webhook,
      marketplace.getProps().webhookEndpoint.getWebhookSecret()
    );
  });

  it('should mark webhook as delivered if it was sent successfully', async () => {
    const command = new SendWebhookCommand(commandProps);

    await handler.execute(command);

    expect(webhook.markAsDelivered).toHaveBeenCalled();
    expect(webhook.recordFailure).not.toHaveBeenCalled();
    expect(webhook.scheduleRetry).not.toHaveBeenCalled();
    expect(webhookRepository.update).toHaveBeenCalledWith(webhook);
  });

  it('should record failure if webhook was not sent successfully', async () => {
    const command = new SendWebhookCommand(commandProps);
    const failReason = 'Mock failure';
    webhookSenderSpy.mockResolvedValue({
      hasFailed: true,
      failReason,
    });

    await handler.execute(command);

    expect(webhook.recordFailure).toHaveBeenCalledWith(failReason);
    expect(webhook.markAsDelivered).not.toHaveBeenCalled();
    expect(webhook.scheduleRetry).not.toHaveBeenCalled();
    expect(webhookRepository.update).toHaveBeenCalledWith(webhook);
  });

  it('should schedule retry if webhook can retry', async () => {
    const command = new SendWebhookCommand(commandProps);
    const failReason = 'Mock failure';
    webhookSenderSpy.mockResolvedValue({
      hasFailed: true,
      failReason,
    });
    webhook.canRetry = jest.fn().mockReturnValue(true);

    await handler.execute(command);

    expect(webhook.recordFailure).toHaveBeenCalledWith(failReason);
    expect(webhook.scheduleRetry).toHaveBeenCalled();
    expect(webhook.markAsDelivered).not.toHaveBeenCalled();
    expect(webhookRepository.update).toHaveBeenCalledWith(webhook);
  });

  it('should operate within a transaction', async () => {
    const command = new SendWebhookCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(webhookRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    expect(webhookRepository.update).toHaveBeenCalled();
  });
});
