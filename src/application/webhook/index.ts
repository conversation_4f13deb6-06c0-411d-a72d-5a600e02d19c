import { ScheduleWebhookCommandHandler } from './commands/schedule-webhook/schedule-webhook.command-handler';
import { SendWebhookCommandHandler } from './commands/send-webhook/send-webhook.command-handler';
import {
  ScheduleWebhookOnPaymentSettledEventHandler,
  ScheduleWebhookOnPaymentSplitUpdatedEventHandler,
  ScheduleWebhookOnRefundSplitUpdatedEventHandler,
  ScheduleWebhookOnRefundUpdatedEventHandler,
  ScheduleWebhookOnDisputeEventHandler
} from './event-handlers';

export const WEBHOOK_COMMAND_HANDLERS = [ScheduleWebhookCommandHandler, SendWebhookCommandHandler];

export const WEBHOOK_EVENT_HANDLERS = [
  ScheduleWebhookOnPaymentSettledEventHandler,
  ScheduleWebhookOnRefundUpdatedEventHandler,
  ScheduleWebhookOnPaymentSplitUpdatedEventHandler,
  ScheduleWebhookOnRefundSplitUpdated<PERSON><PERSON><PERSON>and<PERSON>,
  ScheduleWebhookOnDisputeEventHandler
];
