import { PaymentStatus } from '@/domain/payment/payment';
import { RefundStatus } from '@/domain/payment/refund';
import { PaymentSplitStatus } from '@/domain/payment-split/payment-split';
import { PaymentSplitRefundStatus } from '@/domain/payment-split/payment-split-refund';
import { AllowedCurrencies, PaymentMethodType } from '@/domain/shared';
import { WebhookObject, WebhookType } from '@/domain/webhook/webhook';

export interface WebhookEvent {
  id: string;
  object: WebhookObject;
  type: WebhookType;
  marketplaceId: string;
  data: WebhookData;
}

export type PaymentData = {
  paymentId: string;
  amount: number;
  currency: AllowedCurrencies;
  providerFee: number;
  status: PaymentStatus;
  customerId: string;
  paymentIntentId: string;
  refundedAmount: number;
  marketplaceOrderFee: number;
  paymentMethod: {
    id: string;
    type: PaymentMethodType;
    fingerprintAtGateway: string | null;
    cardLast4: string | null;
    cardExpiryYear: number | null;
    cardExpiryMonth: number | null;
  };
};

export type RefundData = {
  refundId: string;
  paymentId: string;
  amount: number;
  currency: AllowedCurrencies;
  marketplaceOrderFee: number;
  status: RefundStatus;
  isManual: boolean;
  customerId: string;
};

export type PaymentSplitData = {
  paymentId: string;
  paymentSplitId: string;
  amount: number;
  refundedAmount: number;
  currency: AllowedCurrencies;
  status: PaymentSplitStatus;
  accountId: string;
};

export type RefundSplitData = {
  refundId: string;
  paymentId: string;
  refundSplitId: string;
  paymentSplitId: string;
  amount: number;
  currency: AllowedCurrencies;
  status: PaymentSplitRefundStatus;
  accountId: string;
};

export type DisputeData = {
  id: string;
  paymentIntent: string;
  paymentId: string;
  amount: number;
  currency: AllowedCurrencies;
  status: string;
  reason: string;
  evidence: Record<string, any>;
  evidenceDetails: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
};

export type WebhookData =
  | PaymentData
  | RefundData
  | PaymentSplitData
  | RefundSplitData
  | DisputeData;
