import { Inject } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { Events<PERSON><PERSON>ler, IEventHandler } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { PAYMENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import {
  RefundCanceledEvent,
  RefundCreatedEvent,
  RefundFailedEvent,
  RefundIssuedEvent,
} from '@/domain/payment/events';
import { Payment } from '@/domain/payment/payment';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { WebhookObject, WebhookType } from '@/domain/webhook/webhook';

import { ScheduleWebhookCommand } from '../commands/schedule-webhook/schedule-webhook.command';
import { RefundData } from '../interfaces/webhook-event.interface';

@EventsHandler(RefundCreatedEvent, RefundIssuedEvent, RefundFailedEvent, RefundCanceledEvent)
export class ScheduleWebhookOnRefundUpdatedEventHandler implements IEventHandler {
  constructor(
    @Inject(PAYMENT_REPOSITORY_TOKEN) private readonly paymentRepository: PaymentRepositoryPort,
    private readonly commandBus: CommandBus,
    private readonly logger: AppLoggerService,
  ) {}

  async handle(
    event: RefundCreatedEvent | RefundIssuedEvent | RefundFailedEvent | RefundCanceledEvent,
  ) {
    const payment = await this.paymentRepository.findById(event.getProps().aggregateId);

    if (!payment) {
      this.logger.error('Payment is not found for webhook', event);
      return;
    }

    const props = payment.getProps();

    await this.commandBus.execute(
      new ScheduleWebhookCommand({
        type: this.mapWebhookType(event),
        gatewayId: props.gateway.getGatewayId(),
        marketplaceId: props.marketplaceId,
        object: WebhookObject.REFUND,
        data: this.mapToWebhookData(payment, event.getProps().refundId),
      }),
    );
  }

  private mapWebhookType(
    event: RefundCreatedEvent | RefundFailedEvent | RefundIssuedEvent | RefundCanceledEvent,
  ) {
    if (event instanceof RefundCreatedEvent) {
      return WebhookType.REFUND_CREATED;
    }

    if (event instanceof RefundFailedEvent) {
      return WebhookType.REFUND_FAILED;
    }

    if (event instanceof RefundIssuedEvent) {
      return WebhookType.REFUND_ISSUED;
    }

    if (event instanceof RefundCanceledEvent) {
      return WebhookType.REFUND_CANCELED;
    }

    throw new Error('Unsupported event');
  }

  private mapToWebhookData(payment: Payment, refundId: string): RefundData {
    const refund = payment.getRefund(refundId);
    const props = refund.getProps();
    const paymentProps = payment.getProps();

    return {
      paymentId: payment.getId(),
      refundId: props.id,
      amount: props.amount.toNumber(),
      currency: props.amount.getCurrency().getValue(),
      marketplaceOrderFee: props.marketplaceOrderFeeRefunded.toNumber(),
      isManual: props.isManual,
      status: props.status,
      customerId: paymentProps.customerId,
    };
  }
}
