import { Inject } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { PAYMENT_REPOSITORY_TOKEN, PAYMENT_SPLIT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { PaymentSplitCreatedEvent, PaymentSplitSettledEvent } from '@/domain/payment-split/events';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';
import { WebhookObject, WebhookType } from '@/domain/webhook/webhook';

import { ScheduleWebhookCommand } from '../commands/schedule-webhook/schedule-webhook.command';
import { PaymentSplitData } from '../interfaces/webhook-event.interface';

@EventsHandler(PaymentSplitCreatedEvent, PaymentSplitSettledEvent)
export class ScheduleWebhookOnPaymentSplitUpdatedEventHandler implements IEventHandler {
  constructor(
    @Inject(PAYMENT_SPLIT_REPOSITORY_TOKEN)
    private readonly paymentSplitRepository: PaymentSplitRepositoryPort,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    private readonly commandBus: CommandBus,
    private readonly logger: AppLoggerService,
  ) {}

  async handle(event: PaymentSplitCreatedEvent | PaymentSplitSettledEvent) {
    const paymentSplit = await this.paymentSplitRepository.findById(event.getProps().aggregateId);

    if (!paymentSplit) {
      this.logger.error('Payment split is not found for webhook', event);
      return;
    }

    const payment = await this.paymentRepository.findById(paymentSplit.getProps().paymentId);

    if (!payment) {
      this.logger.error('Payment is not found for webhook', event);
      return;
    }

    const props = paymentSplit.getProps();

    await this.commandBus.execute(
      new ScheduleWebhookCommand({
        type: this.mapWebhookType(event),
        gatewayId: props.gateway.getGatewayId(),
        marketplaceId: payment.getProps().marketplaceId,
        object: WebhookObject.PAYMENT_SPLIT,
        data: this.mapToWebhookData(paymentSplit),
      }),
    );
  }

  private mapWebhookType(event: PaymentSplitCreatedEvent | PaymentSplitSettledEvent) {
    if (event instanceof PaymentSplitCreatedEvent) {
      return WebhookType.PAYMENT_SPLIT_CREATED;
    }

    if (event instanceof PaymentSplitSettledEvent) {
      return WebhookType.PAYMENT_SPLIT_SETTLED;
    }

    throw new Error('Unsupported event');
  }

  private mapToWebhookData(paymentSplit: PaymentSplit): PaymentSplitData {
    const props = paymentSplit.getProps();

    return {
      paymentId: props.paymentId,
      amount: props.amount.toNumber(),
      currency: props.amount.getCurrency().getValue(),
      status: props.status,
      accountId: props.accountId,
      refundedAmount: props.refundedAmount.toNumber(),
      paymentSplitId: props.id,
    };
  }
}
