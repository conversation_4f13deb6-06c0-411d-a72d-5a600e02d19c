import { CommandBus } from '@nestjs/cqrs';
import { Test, TestingModule } from '@nestjs/testing';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { DomainEventProps } from '@/core/ddd';
import { PAYMENT_REPOSITORY_TOKEN, PAYMENT_SPLIT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Payment } from '@/domain/payment/payment';
import { PaymentSplitCreatedEvent, PaymentSplitSettledEvent } from '@/domain/payment-split/events';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { WebhookObject, WebhookType } from '@/domain/webhook/webhook';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { PaymentSplitRepositoryMock } from '@/test/mocks/repositories/payment-split-repository.mock';
import { AppLoggerServiceMock } from '@/test/mocks/services/app-logger-service.mock';

import { ScheduleWebhookOnPaymentSplitUpdatedEventHandler } from '.';
import { ScheduleWebhookCommand } from '../commands/schedule-webhook/schedule-webhook.command';

describe('ScheduleWebhookOnPaymentSplitUpdatedEventHandler', () => {
  let handler: ScheduleWebhookOnPaymentSplitUpdatedEventHandler;
  let paymentSplitRepository: PaymentSplitRepositoryMock;
  let paymentRepository: PaymentRepositoryMock;
  let commandBus: CommandBus;
  let logger: AppLoggerService;
  let eventProps: DomainEventProps<unknown>;
  let paymentSplit: PaymentSplit;
  let payment: Payment;
  let commandBusExecuteSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduleWebhookOnPaymentSplitUpdatedEventHandler,
        CommandBus,
        { provide: PAYMENT_SPLIT_REPOSITORY_TOKEN, useClass: PaymentSplitRepositoryMock },
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: AppLoggerService, useClass: AppLoggerServiceMock },
      ],
    }).compile();

    handler = module.get<ScheduleWebhookOnPaymentSplitUpdatedEventHandler>(
      ScheduleWebhookOnPaymentSplitUpdatedEventHandler,
    );
    paymentSplitRepository = module.get<PaymentSplitRepositoryMock>(PAYMENT_SPLIT_REPOSITORY_TOKEN);
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    commandBus = module.get<CommandBus>(CommandBus);
    logger = module.get<AppLoggerService>(AppLoggerService);
    [paymentSplit] = paymentSplitRepository.db;
    [payment] = paymentRepository.db;
    eventProps = {
      aggregateId: paymentSplit.getId(),
    };
    commandBusExecuteSpy = jest.spyOn(commandBus, 'execute').mockResolvedValue(null);
    logger.error = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payment split is not found', async () => {
    const event = new PaymentSplitCreatedEvent({
      aggregateId: 'invalid-payment-split-id',
    });

    await handler.handle(event);

    expect(logger.error).toHaveBeenCalledWith('Payment split is not found for webhook', event);
  });

  it('should fail if payment is not found', async () => {
    const event = new PaymentSplitCreatedEvent(eventProps);

    paymentSplitRepository.findById = jest.fn().mockResolvedValue(
      new PaymentSplit({
        id: paymentSplit.getId(),
        props: {
          ...paymentSplit.getProps(),
          paymentId: 'invalid-payment-id',
        },
      }),
    );

    await handler.handle(event);

    expect(logger.error).toHaveBeenCalledWith('Payment is not found for webhook', event);
  });

  it('should create a command for payment split created event', async () => {
    const event = new PaymentSplitCreatedEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.PAYMENT_SPLIT_CREATED,
        }),
      ),
    );
  });

  it('should create a command for payment split settled event', async () => {
    const event = new PaymentSplitSettledEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.PAYMENT_SPLIT_SETTLED,
        }),
      ),
    );
  });

  it('should execute schedule webhook command with correct data', async () => {
    const event = new PaymentSplitCreatedEvent(eventProps);

    await handler.handle(event);

    const paymentSplitProps = paymentSplit.getProps();
    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand({
        type: WebhookType.PAYMENT_SPLIT_CREATED,
        gatewayId: payment.getProps().gateway.getGatewayId(),
        marketplaceId: payment.getProps().marketplaceId,
        object: WebhookObject.PAYMENT_SPLIT,
        data: {
          paymentId: paymentSplitProps.paymentId,
          amount: paymentSplitProps.amount.toNumber(),
          currency: paymentSplitProps.amount.getCurrency().getValue(),
          status: paymentSplitProps.status,
          accountId: paymentSplitProps.accountId,
          refundedAmount: paymentSplitProps.refundedAmount.toNumber(),
          paymentSplitId: paymentSplitProps.id,
        },
      }),
    );
  });
});
