import { Inject } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { Events<PERSON>andler, IEventHandler } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { PAYMENT_REPOSITORY_TOKEN, PAYMENT_SPLIT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import {
  PaymentSplitRefundCreatedEvent,
  PaymentSplitRefundCanceledEvent,
  PaymentSplitRefundFailedEvent,
  PaymentSplitRefundIssuedEvent,
  PaymentSplitRefundProcessingEvent,
} from '@/domain/payment-split/events';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';
import { WebhookObject, WebhookType } from '@/domain/webhook/webhook';

import { ScheduleWebhookCommand } from '../commands/schedule-webhook/schedule-webhook.command';
import { RefundSplitData } from '../interfaces/webhook-event.interface';

@EventsHandler(
  PaymentSplitRefundCreatedEvent,
  PaymentSplitRefundCanceledEvent,
  PaymentSplitRefundFailedEvent,
  PaymentSplitRefundIssuedEvent,
  PaymentSplitRefundProcessingEvent,
)
export class ScheduleWebhookOnRefundSplitUpdatedEventHandler implements IEventHandler {
  constructor(
    @Inject(PAYMENT_REPOSITORY_TOKEN) private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(PAYMENT_SPLIT_REPOSITORY_TOKEN)
    private readonly paymentSplitRepository: PaymentSplitRepositoryPort,
    private readonly commandBus: CommandBus,
    private readonly logger: AppLoggerService,
  ) {}

  async handle(
    event:
      | PaymentSplitRefundCreatedEvent
      | PaymentSplitRefundCanceledEvent
      | PaymentSplitRefundFailedEvent
      | PaymentSplitRefundIssuedEvent
      | PaymentSplitRefundProcessingEvent,
  ) {
    const paymentSplit = await this.paymentSplitRepository.findById(event.getProps().aggregateId);

    if (!paymentSplit) {
      this.logger.error('Payment split is not found for webhook', event);
      return;
    }

    const payment = await this.paymentRepository.findById(paymentSplit.getProps().paymentId);

    if (!payment) {
      this.logger.error('Payment is not found for webhook', event);
      return;
    }
    const props = payment.getProps();

    await this.commandBus.execute(
      new ScheduleWebhookCommand({
        type: this.mapWebhookType(event),
        gatewayId: props.gateway.getGatewayId(),
        marketplaceId: props.marketplaceId,
        object: WebhookObject.REFUND_SPLIT,
        data: this.mapToWebhookData(paymentSplit, event.getProps().paymentSplitRefundId),
      }),
    );
  }

  private mapWebhookType(
    event:
      | PaymentSplitRefundCreatedEvent
      | PaymentSplitRefundCanceledEvent
      | PaymentSplitRefundFailedEvent
      | PaymentSplitRefundIssuedEvent
      | PaymentSplitRefundProcessingEvent,
  ) {
    if (event instanceof PaymentSplitRefundCreatedEvent) {
      return WebhookType.REFUND_SPLIT_CREATED;
    }

    if (event instanceof PaymentSplitRefundIssuedEvent) {
      return WebhookType.REFUND_SPLIT_SETTLED;
    }

    if (event instanceof PaymentSplitRefundCanceledEvent) {
      return WebhookType.REFUND_SPLIT_CANCELED;
    }

    if (event instanceof PaymentSplitRefundFailedEvent) {
      return WebhookType.REFUND_SPLIT_FAILED;
    }

    if (event instanceof PaymentSplitRefundProcessingEvent) {
      return WebhookType.REFUND_SPLIT_PROCESSING;
    }

    throw new Error('Unsupported event');
  }

  private mapToWebhookData(
    paymentSplit: PaymentSplit,
    paymentSplitRefundId: string,
  ): RefundSplitData {
    const refund = paymentSplit.getPaymentSplitRefund(paymentSplitRefundId);
    const props = refund.getProps();
    const { accountId, id: paymentSplitId, paymentId } = paymentSplit.getProps();

    return {
      paymentId,
      refundId: props.refundId,
      amount: props.amount.toNumber(),
      currency: props.amount.getCurrency().getValue(),
      status: props.status,
      accountId,
      paymentSplitId,
      refundSplitId: props.id,
    };
  }
}
