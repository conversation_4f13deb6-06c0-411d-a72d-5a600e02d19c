import { Inject } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { PAYMENT_METHOD_REPOSITORY_TOKEN, PAYMENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PaymentSettledEvent } from '@/domain/payment/events';
import { Payment } from '@/domain/payment/payment';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { PaymentMethod } from '@/domain/payment-method/payment-method';
import { PaymentMethodRepositoryPort } from '@/domain/payment-method/ports';
import { WebhookObject, WebhookType } from '@/domain/webhook/webhook';

import { ScheduleWebhookCommand } from '../commands/schedule-webhook/schedule-webhook.command';
import { PaymentData } from '../interfaces/webhook-event.interface';

@EventsHandler(PaymentSettledEvent)
export class ScheduleWebhookOnPaymentSettledEventHandler implements IEventHandler {
  constructor(
    @Inject(PAYMENT_REPOSITORY_TOKEN) private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(PAYMENT_METHOD_REPOSITORY_TOKEN)
    private readonly paymentMethodRepository: PaymentMethodRepositoryPort,
    private readonly commandBus: CommandBus,
    private readonly logger: AppLoggerService,
  ) {}

  async handle(event: PaymentSettledEvent) {
    const payment = await this.paymentRepository.findById(event.getProps().aggregateId);

    if (!payment) {
      this.logger.error('Payment is not found for webhook', event);
      return;
    }

    const paymentMethod = await this.paymentMethodRepository.findById(
      payment.getProps().paymentMethodId,
    );

    if (!paymentMethod) {
      this.logger.error('Payment method is not found for webhook', event);
      return;
    }

    const props = payment.getProps();

    await this.commandBus.execute(
      new ScheduleWebhookCommand({
        type: WebhookType.PAYMENT_SETTLED,
        gatewayId: props.gateway.getGatewayId(),
        marketplaceId: props.marketplaceId,
        object: WebhookObject.PAYMENT,
        data: this.mapToWebhookData(payment, paymentMethod),
      }),
    );
  }

  private mapToWebhookData(payment: Payment, paymentMethod: PaymentMethod): PaymentData {
    const props = payment.getProps();

    return {
      paymentId: props.id,
      paymentIntentId: props.paymentIntentId,
      amount: props.amount.toNumber(),
      currency: props.amount.getCurrency().getValue(),
      providerFee: props.providerFee.toNumber(),
      status: props.status,
      customerId: props.customerId,
      refundedAmount: props.refundedAmount.toNumber(),
      marketplaceOrderFee: props.marketplaceOrderFee.toNumber(),
      paymentMethod: this.mapToPaymentMethodWebhookData(paymentMethod),
    };
  }

  private mapToPaymentMethodWebhookData(
    paymentMethod: PaymentMethod,
  ): PaymentData['paymentMethod'] {
    const props = paymentMethod.getProps();
    return {
      id: props.id,
      cardLast4: props.cardLast4,
      type: props.type,
      fingerprintAtGateway: props.gatewayPaymentMethod.getProps().fingerprint,
      cardExpiryMonth: props.cardExpiryMonth,
      cardExpiryYear: props.cardExpiryYear,
    };
  }
}
