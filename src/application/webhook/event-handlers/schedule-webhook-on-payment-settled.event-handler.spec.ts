import { CommandBus } from '@nestjs/cqrs';
import { Test, TestingModule } from '@nestjs/testing';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { PAYMENT_METHOD_REPOSITORY_TOKEN, PAYMENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PaymentSettledEvent } from '@/domain/payment/events';
import { Payment } from '@/domain/payment/payment';
import { PaymentMethod } from '@/domain/payment-method/payment-method';
import { AllowedCurrencies, Currency, Money } from '@/domain/shared';
import { WebhookObject, WebhookType } from '@/domain/webhook/webhook';
import { PaymentMethodRepositoryMock } from '@/test/mocks/repositories/payment-method-repository.mock';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { AppLoggerServiceMock } from '@/test/mocks/services/app-logger-service.mock';

import { ScheduleWebhookOnPaymentSettledEventHandler } from '.';
import { ScheduleWebhookCommand } from '../commands/schedule-webhook/schedule-webhook.command';

type EventProps = {
  aggregateId: string;
  amount: Money;
};

describe('ScheduleWebhookOnPaymentSettledEventHandler', () => {
  let handler: ScheduleWebhookOnPaymentSettledEventHandler;
  let paymentRepository: PaymentRepositoryMock;
  let paymentMethodRepository: PaymentMethodRepositoryMock;
  let commandBus: CommandBus;
  let logger: AppLoggerService;
  let eventProps: EventProps;
  let payment: Payment;
  let paymentMethod: PaymentMethod;
  let commandBusExecuteSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduleWebhookOnPaymentSettledEventHandler,
        CommandBus,
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: PAYMENT_METHOD_REPOSITORY_TOKEN, useClass: PaymentMethodRepositoryMock },
        { provide: AppLoggerService, useClass: AppLoggerServiceMock },
      ],
    }).compile();

    handler = module.get<ScheduleWebhookOnPaymentSettledEventHandler>(
      ScheduleWebhookOnPaymentSettledEventHandler,
    );
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    paymentMethodRepository = module.get<PaymentMethodRepositoryMock>(
      PAYMENT_METHOD_REPOSITORY_TOKEN,
    );
    commandBus = module.get<CommandBus>(CommandBus);
    logger = module.get<AppLoggerService>(AppLoggerService);
    [payment] = paymentRepository.db;
    [paymentMethod] = paymentMethodRepository.db;
    eventProps = {
      aggregateId: payment.getId(),
      amount: new Money(1, new Currency(AllowedCurrencies.USD)),
    };
    commandBusExecuteSpy = jest.spyOn(commandBus, 'execute').mockResolvedValue(null);
    logger.error = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payment is not found', async () => {
    const event = new PaymentSettledEvent({
      ...eventProps,
      aggregateId: 'invalid-payment-id',
    });

    await handler.handle(event);

    expect(logger.error).toHaveBeenCalledWith('Payment is not found for webhook', event);
  });

  it('should execute schedule webhook command with correct data', async () => {
    const event = new PaymentSettledEvent(eventProps);

    await handler.handle(event);

    const paymentProps = payment.getProps();
    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand({
        type: WebhookType.PAYMENT_SETTLED,
        gatewayId: payment.getProps().gateway.getGatewayId(),
        marketplaceId: payment.getProps().marketplaceId,
        object: WebhookObject.PAYMENT,
        data: {
          paymentId: payment.getId(),
          paymentIntentId: paymentProps.paymentIntentId,
          amount: paymentProps.amount.toNumber(),
          currency: paymentProps.amount.getCurrency().getValue(),
          providerFee: paymentProps.providerFee.toNumber(),
          status: paymentProps.status,
          customerId: paymentProps.customerId,
          refundedAmount: paymentProps.refundedAmount.toNumber(),
          marketplaceOrderFee: paymentProps.marketplaceOrderFee.toNumber(),
          paymentMethod: {
            id: paymentMethod.getId(),
            type: paymentMethod.getProps().type,
            fingerprintAtGateway: paymentMethod.getProps().gatewayPaymentMethod.getProps()
              .fingerprint,
            cardLast4: paymentMethod.getProps().cardLast4,
            cardExpiryYear: paymentMethod.getProps().cardExpiryYear,
            cardExpiryMonth: paymentMethod.getProps().cardExpiryMonth,
          },
        },
      }),
    );
  });
});
