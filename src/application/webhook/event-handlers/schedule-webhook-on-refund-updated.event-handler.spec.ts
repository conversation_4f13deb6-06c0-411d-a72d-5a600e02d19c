import { CommandBus } from '@nestjs/cqrs';
import { Test, TestingModule } from '@nestjs/testing';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { PAYMENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import {
  PaymentCreatedEvent,
  RefundCanceledEvent,
  RefundCreatedEvent,
  RefundFailedEvent,
  RefundIssuedEvent,
} from '@/domain/payment/events';
import { Payment } from '@/domain/payment/payment';
import { AllowedCurrencies, Currency, Money } from '@/domain/shared';
import { WebhookObject, WebhookType } from '@/domain/webhook/webhook';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { AppLoggerServiceMock } from '@/test/mocks/services/app-logger-service.mock';

import { ScheduleWebhookOnRefundUpdatedEventHandler } from '.';
import { ScheduleWebhookCommand } from '../commands/schedule-webhook/schedule-webhook.command';

type EventProps = {
  aggregateId: string;
  refundId: string;
  amount: Money;
  refundAmount: Money;
};

describe('ScheduleWebhookOnRefundUpdatedEventHandler', () => {
  let handler: ScheduleWebhookOnRefundUpdatedEventHandler;
  let paymentRepository: PaymentRepositoryMock;
  let commandBus: CommandBus;
  let logger: AppLoggerService;
  let eventProps: EventProps;
  let payment: Payment;
  let commandBusExecuteSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduleWebhookOnRefundUpdatedEventHandler,
        CommandBus,
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: AppLoggerService, useClass: AppLoggerServiceMock },
      ],
    }).compile();

    handler = module.get<ScheduleWebhookOnRefundUpdatedEventHandler>(
      ScheduleWebhookOnRefundUpdatedEventHandler,
    );
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    commandBus = module.get<CommandBus>(CommandBus);
    logger = module.get<AppLoggerService>(AppLoggerService);
    [payment] = paymentRepository.db;
    eventProps = {
      aggregateId: payment.getId(),
      refundId: payment.getProps().refunds[0].getId(),
      amount: new Money(10, new Currency(AllowedCurrencies.USD)),
      refundAmount: new Money(5, new Currency(AllowedCurrencies.USD)),
    };
    commandBusExecuteSpy = jest.spyOn(commandBus, 'execute').mockResolvedValue(null);
    logger.error = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payment is not found', async () => {
    const event = new RefundCreatedEvent({
      ...eventProps,
      aggregateId: 'invalid-payment-id',
    });

    await handler.handle(event);

    expect(logger.error).toHaveBeenCalledWith('Payment is not found for webhook', event);
  });

  it('should create a command for refund created event', async () => {
    const event = new RefundCreatedEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.REFUND_CREATED,
        }),
      ),
    );
  });

  it('should create a command for refund issued event', async () => {
    const event = new RefundIssuedEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.REFUND_ISSUED,
        }),
      ),
    );
  });

  it('should create a command for refund canceled event', async () => {
    const event = new RefundCanceledEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.REFUND_CANCELED,
        }),
      ),
    );
  });

  it('should create a command for refund failed event', async () => {
    const event = new RefundFailedEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.REFUND_FAILED,
        }),
      ),
    );
  });

  it('should fail if event is not an instance of any of the supported events', async () => {
    const event = new PaymentCreatedEvent({ aggregateId: eventProps.aggregateId });

    await expect(handler.handle(event as any)).rejects.toThrow('Unsupported event');
  });

  it('should execute schedule webhook command with correct data', async () => {
    const event = new RefundCreatedEvent(eventProps);

    await handler.handle(event);

    const paymentProps = payment.getProps();
    const refundProps = payment.getRefund(eventProps.refundId).getProps();
    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand({
        type: WebhookType.REFUND_CREATED,
        gatewayId: paymentProps.gateway.getGatewayId(),
        marketplaceId: paymentProps.marketplaceId,
        object: WebhookObject.REFUND,
        data: {
          paymentId: payment.getId(),
          refundId: refundProps.id,
          amount: refundProps.amount.toNumber(),
          marketplaceOrderFee: refundProps.marketplaceOrderFeeRefunded.toNumber(),
          currency: refundProps.amount.getCurrency().getValue(),
          isManual: refundProps.isManual,
          status: refundProps.status,
          customerId: paymentProps.customerId,
        },
      }),
    );
  });
});
