import { CommandBus } from '@nestjs/cqrs';
import { Test, TestingModule } from '@nestjs/testing';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { PAYMENT_REPOSITORY_TOKEN, PAYMENT_SPLIT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Payment } from '@/domain/payment/payment';
import {
  PaymentSplitRefundCreatedEvent,
  PaymentSplitRefundIssuedEvent,
  PaymentSplitRefundCanceledEvent,
  PaymentSplitRefundFailedEvent,
  PaymentSplitRefundProcessingEvent,
  PaymentSplitCreatedEvent,
} from '@/domain/payment-split/events';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { AllowedCurrencies, Currency, Money } from '@/domain/shared';
import { WebhookObject, WebhookType } from '@/domain/webhook/webhook';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { PaymentSplitRepositoryMock } from '@/test/mocks/repositories/payment-split-repository.mock';
import { AppLoggerServiceMock } from '@/test/mocks/services/app-logger-service.mock';

import { ScheduleWebhookOnRefundSplitUpdatedEventHandler } from '.';
import { ScheduleWebhookCommand } from '../commands/schedule-webhook/schedule-webhook.command';

type EventProps = {
  aggregateId: string;
  paymentSplitRefundId: string;
  amount: Money;
  refundId: string;
};

describe('ScheduleWebhookOnRefundSplitUpdatedEventHandler', () => {
  let handler: ScheduleWebhookOnRefundSplitUpdatedEventHandler;
  let paymentSplitRepository: PaymentSplitRepositoryMock;
  let paymentRepository: PaymentRepositoryMock;
  let commandBus: CommandBus;
  let logger: AppLoggerService;
  // Merging the event props for all the events for easier testing
  let eventProps: EventProps;
  let payment: Payment;
  let paymentSplit: PaymentSplit;
  let commandBusExecuteSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduleWebhookOnRefundSplitUpdatedEventHandler,
        CommandBus,
        { provide: PAYMENT_SPLIT_REPOSITORY_TOKEN, useClass: PaymentSplitRepositoryMock },
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: AppLoggerService, useClass: AppLoggerServiceMock },
      ],
    }).compile();

    handler = module.get<ScheduleWebhookOnRefundSplitUpdatedEventHandler>(
      ScheduleWebhookOnRefundSplitUpdatedEventHandler,
    );
    paymentSplitRepository = module.get<PaymentSplitRepositoryMock>(PAYMENT_SPLIT_REPOSITORY_TOKEN);
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    commandBus = module.get<CommandBus>(CommandBus);
    logger = module.get<AppLoggerService>(AppLoggerService);
    [paymentSplit] = paymentSplitRepository.db;
    [payment] = paymentRepository.db;
    eventProps = {
      aggregateId: paymentSplit.getId(),
      paymentSplitRefundId: paymentSplit.getProps().refunds[0].getId(),
      amount: new Money(1, new Currency(AllowedCurrencies.USD)),
      refundId: 'refund-id',
    };
    commandBusExecuteSpy = jest.spyOn(commandBus, 'execute').mockResolvedValue(null);
    logger.error = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payment split is not found', async () => {
    const event = new PaymentSplitRefundCreatedEvent({
      ...eventProps,
      aggregateId: 'invalid-payment-split-id',
    });

    await handler.handle(event);

    expect(logger.error).toHaveBeenCalledWith('Payment split is not found for webhook', event);
  });

  it('should fail if payment is not found', async () => {
    const event = new PaymentSplitRefundCreatedEvent(eventProps);

    paymentSplitRepository.findById = jest.fn().mockResolvedValue(
      new PaymentSplit({
        id: paymentSplit.getId(),
        props: {
          ...paymentSplit.getProps(),
          paymentId: 'invalid-payment-id',
        },
      }),
    );

    await handler.handle(event);

    expect(logger.error).toHaveBeenCalledWith('Payment is not found for webhook', event);
  });

  it('should create a command for payment split refund created event', async () => {
    const event = new PaymentSplitRefundCreatedEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.REFUND_SPLIT_CREATED,
        }),
      ),
    );
  });

  it('should create a command for payment split refund issued event', async () => {
    const event = new PaymentSplitRefundIssuedEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.REFUND_SPLIT_SETTLED,
        }),
      ),
    );
  });

  it('should create a command for payment split refund canceled event', async () => {
    const event = new PaymentSplitRefundCanceledEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.REFUND_SPLIT_CANCELED,
        }),
      ),
    );
  });

  it('should create a command for payment split refund failed event', async () => {
    const event = new PaymentSplitRefundFailedEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.REFUND_SPLIT_FAILED,
        }),
      ),
    );
  });

  it('should create a command for payment split refund processing event', async () => {
    const event = new PaymentSplitRefundProcessingEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.REFUND_SPLIT_PROCESSING,
        }),
      ),
    );
  });

  it('should fail if event is not an instance of any of the supported events', async () => {
    const event = new PaymentSplitCreatedEvent({ aggregateId: eventProps.aggregateId });

    await expect(handler.handle(event as any)).rejects.toThrow(new Error('Unsupported event'));
  });

  it('should execute schedule webhook command with correct data', async () => {
    const event = new PaymentSplitRefundCreatedEvent(eventProps);

    await handler.handle(event);

    const paymentProps = payment.getProps();
    const paymentSplitProps = paymentSplit.getProps();
    const refundProps = paymentSplit
      .getPaymentSplitRefund(eventProps.paymentSplitRefundId)
      .getProps();
    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand({
        type: WebhookType.REFUND_SPLIT_CREATED,
        gatewayId: paymentProps.gateway.getGatewayId(),
        marketplaceId: paymentProps.marketplaceId,
        object: WebhookObject.REFUND_SPLIT,
        data: {
          paymentId: paymentSplitProps.paymentId,
          refundId: refundProps.refundId,
          amount: refundProps.amount.toNumber(),
          currency: refundProps.amount.getCurrency().getValue(),
          status: refundProps.status,
          accountId: paymentSplitProps.accountId,
          paymentSplitId: paymentSplitProps.id,
          refundSplitId: refundProps.id,
        },
      }),
    );
  });
});
