import { CommandBus } from '@nestjs/cqrs';
import { Test, TestingModule } from '@nestjs/testing';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { DomainEventProps } from '@/core/ddd';
import { DISPUTE_REPOSITORY_TOKEN, PAYMENT_INTENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Dispute } from '@/domain/dispute/dispute';
import { DisputeCreatedDomainEvent, DisputeUpdatedDomainEvent } from '@/domain/dispute/events';
import { PaymentIntent } from '@/domain/payment-intent/payment-intent';
import { WebhookObject, WebhookType } from '@/domain/webhook/webhook';
import { DisputeRepositoryMock } from '@/test/mocks/repositories/dispute-repository.mock';
import { PaymentIntentRepositoryMock } from '@/test/mocks/repositories/payment-intent-repository.mock';
import { AppLoggerServiceMock } from '@/test/mocks/services/app-logger-service.mock';

import { ScheduleWebhookOnDisputeEventHandler } from '.';
import { ScheduleWebhookCommand } from '../commands/schedule-webhook/schedule-webhook.command';

describe('ScheduleWebhookOnDisputeEventHandler', () => {
  let handler: ScheduleWebhookOnDisputeEventHandler;
  let commandBus: CommandBus;
  let disputeRepository: DisputeRepositoryMock;
  let paymentIntentRepository: PaymentIntentRepositoryMock;
  let logger: AppLoggerService;
  let eventProps: DomainEventProps<unknown>;
  let dispute: Dispute;
  let paymentIntent: PaymentIntent;
  let commandBusExecuteSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduleWebhookOnDisputeEventHandler,
        CommandBus,
        { provide: DISPUTE_REPOSITORY_TOKEN, useClass: DisputeRepositoryMock },
        { provide: PAYMENT_INTENT_REPOSITORY_TOKEN, useClass: PaymentIntentRepositoryMock },
        { provide: AppLoggerService, useClass: AppLoggerServiceMock },
      ],
    }).compile();

    handler = module.get<ScheduleWebhookOnDisputeEventHandler>(
      ScheduleWebhookOnDisputeEventHandler,
    );
    commandBus = module.get<CommandBus>(CommandBus);
    disputeRepository = module.get<DisputeRepositoryMock>(DISPUTE_REPOSITORY_TOKEN);
    paymentIntentRepository = module.get<PaymentIntentRepositoryMock>(
      PAYMENT_INTENT_REPOSITORY_TOKEN,
    );
    logger = module.get<AppLoggerService>(AppLoggerService);
    [dispute] = disputeRepository.db;
    [paymentIntent] = paymentIntentRepository.db;
    eventProps = {
      aggregateId: dispute.getId(),
    };
    commandBusExecuteSpy = jest.spyOn(commandBus, 'execute').mockResolvedValue(null);
    logger.error = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if dispute is not found', async () => {
    const event = new DisputeCreatedDomainEvent({
      aggregateId: 'invalid-dispute-id',
    });

    await handler.handle(event);

    expect(logger.error).toHaveBeenCalledWith('Dispute is not found for webhook', event);
  });

  it('should fail if paymentIntent is not found', async () => {
    const event = new DisputeCreatedDomainEvent(eventProps);

    disputeRepository.findById = jest.fn().mockResolvedValue(
      new Dispute({
        id: dispute.getId(),
        props: {
          ...dispute.getProps(),
          paymentIntentId: 'invalid-payment-intent-id',
        },
      }),
    );

    await handler.handle(event);

    expect(logger.error).toHaveBeenCalledWith(
      'PaymentIntent is not found for disputeID',
      dispute.getId(),
    );
  });

  it('should create a command for dispute created event', async () => {
    const event = new DisputeCreatedDomainEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.DISPUTE_CREATED,
        }),
      ),
    );
  });

  it('should create a command for dispute updated event', async () => {
    const event = new DisputeUpdatedDomainEvent(eventProps);

    await handler.handle(event);

    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand(
        expect.objectContaining({
          type: WebhookType.DISPUTE_UPDATED,
        }),
      ),
    );
  });

  it('should execute schedule webhook command with correct data', async () => {
    const event = new DisputeCreatedDomainEvent(eventProps);

    await handler.handle(event);

    const disputeProps = dispute.getProps();
    expect(commandBusExecuteSpy).toHaveBeenCalledWith(
      new ScheduleWebhookCommand({
        type: WebhookType.DISPUTE_CREATED,
        gatewayId: paymentIntent.getProps().gateway.getGatewayId(),
        marketplaceId: paymentIntent.getProps().marketplaceId,
        object: WebhookObject.DISPUTE,
        data: {
          id: dispute.getId(),
          paymentId: disputeProps.paymentId,
          paymentIntent: disputeProps.paymentIntentId,
          status: disputeProps.status,
          amount: disputeProps.amount.toNumber(),
          currency: disputeProps.amount.getCurrency().getValue(),
          reason: disputeProps.reason,
          evidence: disputeProps.evidence,
          evidenceDetails: disputeProps.evidenceDetails,
          createdAt: disputeProps.createdAt,
          updatedAt: disputeProps.updatedAt,
        },
      }),
    );
  });
});
