import { Inject } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';
import { AppLoggerService } from '@sw-web/nestjs-core/logger';

import { ScheduleWebhookCommand } from '@/application/webhook/commands/schedule-webhook/schedule-webhook.command';
import { DisputeData } from '@/application/webhook/interfaces/webhook-event.interface';
import { DISPUTE_REPOSITORY_TOKEN, PAYMENT_INTENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Dispute } from '@/domain/dispute/dispute';
import { DisputeUpdatedDomainEvent, DisputeCreatedDomainEvent } from '@/domain/dispute/events';
import { DisputeRepositoryPort } from '@/domain/dispute/ports';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';
import { WebhookObject, WebhookType } from '@/domain/webhook/webhook';

@EventsHandler(DisputeCreatedDomainEvent, DisputeUpdatedDomainEvent)
export class ScheduleWebhookOnDisputeEventHandler implements IEventHandler {
  constructor(
    @Inject(PAYMENT_INTENT_REPOSITORY_TOKEN)
    private readonly paymentIntentRepository: PaymentIntentRepositoryPort,
    @Inject(DISPUTE_REPOSITORY_TOKEN)
    private readonly disputeRepository: DisputeRepositoryPort,
    private readonly commandBus: CommandBus,
    private readonly logger: AppLoggerService,
  ) {}

  async handle(event: DisputeCreatedDomainEvent | DisputeUpdatedDomainEvent) {
    const dispute = await this.disputeRepository.findById(event.getProps().aggregateId);

    if (!dispute) {
      this.logger.error('Dispute is not found for webhook', event);

      return;
    }

    const paymentIntent = await this.paymentIntentRepository.findById(
      dispute.getProps().paymentIntentId,
    );

    if (!paymentIntent) {
      this.logger.error('PaymentIntent is not found for disputeID', dispute.getId());

      return;
    }

    await this.commandBus.execute(
      new ScheduleWebhookCommand({
        type: this.mapWebhookType(event),
        gatewayId: paymentIntent.getProps().gateway.getGatewayId(),
        marketplaceId: paymentIntent.getProps().marketplaceId,
        object: WebhookObject.DISPUTE,
        data: this.mapToWebhookData(dispute),
      }),
    );
  }

  private mapWebhookType(event: DisputeCreatedDomainEvent | DisputeUpdatedDomainEvent) {
    if (event instanceof DisputeCreatedDomainEvent) {
      return WebhookType.DISPUTE_CREATED;
    }

    if (event instanceof DisputeUpdatedDomainEvent) {
      return WebhookType.DISPUTE_UPDATED;
    }

    throw new Error('Unsupported event');
  }

  private mapToWebhookData(dispute: Dispute): DisputeData {
    return {
      id: dispute.getId(),
      paymentIntent: dispute.getProps().paymentIntentId,
      paymentId: dispute.getProps().paymentId,
      status: dispute.getProps().status,
      amount: dispute.getProps().amount.toNumber(),
      currency: dispute.getProps().amount.getCurrency().getValue(),
      reason: dispute.getProps().reason,
      evidence: dispute.getProps().evidence,
      evidenceDetails: dispute.getProps().evidenceDetails,
      createdAt: dispute.getProps().createdAt,
      updatedAt: dispute.getProps().updatedAt,
    };
  }
}
