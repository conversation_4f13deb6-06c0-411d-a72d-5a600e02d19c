import { ACCOUNT_APPLICATION_SERVICES, ACCOUNT_COMMAND_HANDLERS } from './account';
import { CUSTOMER_COMMAND_HANDLERS } from './customer';
import { DEBT_COMMAND_HANDLERS } from './debt';
import { DISPUTE_COMMAND_HANDLERS } from './dispute';
import { FEE_SETTINGS_COMMAND_HANDLERS } from './fee-settings';
import { GATEWAY_APPLICATION_SERVICES, GATEWAY_COMMAND_HANDLERS } from './gateway';
import { MARKETPLACE_COMMAND_HANDLERS } from './marketplace';
import {
  MARKETPLACE_GATEWAY_COMMAND_HANDLERS,
  MARKETPLACE_GATEWAY_EVENT_HANDLERS,
} from './marketplace-gateway';
import {
  MARKETPLACE_GATEWAY_TRANSACTION_COMMAND_HANDLERS,
  MARKETPLACE_GATEWAY_TRANSACTION_EVENT_HANDLERS,
} from './marketplace-gateway-transaction';
import {
  PAYMENT_APPLICATION_SERVICES,
  PAYMENT_COMMAND_HANDLERS,
  PAYMENT_EVENT_HANDLERS,
} from './payment';
import {
  PAYMENT_INTENT_APPLICATION_SERVICES,
  PAYMENT_INTENT_COMMAND_HANDLERS,
  PAYMENT_INTENT_QUERY_HANDLERS,
} from './payment-intent';
import {
  PAYMENT_METHOD_APPLICATION_SERVICES,
  PAYMENT_METHOD_COMMAND_HANDLERS,
} from './payment-method';
import {
  PAYMENT_SPLIT_APPLICATION_SERVICES,
  PAYMENT_SPLIT_COMMAND_HANDLERS,
  PAYMENT_SPLIT_EVENT_HANDLERS,
} from './payment-split';
import { PAYOUT_COMMAND_HANDLERS, PAYOUT_EVENT_HANDLERS } from './payout';
import { WEBHOOK_COMMAND_HANDLERS, WEBHOOK_EVENT_HANDLERS } from './webhook';

const COMMAND_HANDLERS = [
  ...PAYMENT_INTENT_COMMAND_HANDLERS,
  ...MARKETPLACE_COMMAND_HANDLERS,
  ...CUSTOMER_COMMAND_HANDLERS,
  ...ACCOUNT_COMMAND_HANDLERS,
  ...PAYMENT_COMMAND_HANDLERS,
  ...PAYMENT_SPLIT_COMMAND_HANDLERS,
  ...WEBHOOK_COMMAND_HANDLERS,
  ...DEBT_COMMAND_HANDLERS,
  ...DISPUTE_COMMAND_HANDLERS,
  ...MARKETPLACE_GATEWAY_COMMAND_HANDLERS,
  ...PAYOUT_COMMAND_HANDLERS,
  ...MARKETPLACE_GATEWAY_TRANSACTION_COMMAND_HANDLERS,
  ...GATEWAY_COMMAND_HANDLERS,
  ...PAYMENT_METHOD_COMMAND_HANDLERS,
  ...FEE_SETTINGS_COMMAND_HANDLERS,
];

const SERVICES = [
  ...GATEWAY_APPLICATION_SERVICES,
  ...PAYMENT_INTENT_APPLICATION_SERVICES,
  ...PAYMENT_APPLICATION_SERVICES,
  ...PAYMENT_SPLIT_APPLICATION_SERVICES,
  ...ACCOUNT_APPLICATION_SERVICES,
  ...PAYMENT_METHOD_APPLICATION_SERVICES,
];

const EVENT_HANDLERS = [
  ...PAYMENT_EVENT_HANDLERS,
  ...PAYMENT_SPLIT_EVENT_HANDLERS,
  ...WEBHOOK_EVENT_HANDLERS,
  ...MARKETPLACE_GATEWAY_EVENT_HANDLERS,
  ...PAYOUT_EVENT_HANDLERS,
  ...MARKETPLACE_GATEWAY_TRANSACTION_EVENT_HANDLERS,
];

const QUERY_HANDLERS = [...PAYMENT_INTENT_QUERY_HANDLERS];

export const APPLICATION_PROVIDERS = [
  ...COMMAND_HANDLERS,
  ...SERVICES,
  ...EVENT_HANDLERS,
  ...QUERY_HANDLERS,
];
