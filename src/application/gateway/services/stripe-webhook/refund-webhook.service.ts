import { Inject, Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { keyBy } from 'lodash';
import Stripe from 'stripe';

import { STRIPE_GATEWAY_REPOSITORY_TOKEN } from '@/application/di-tokens';
import { CreateManualRefundCommand } from '@/application/payment/commands/create-manual-refund';
import { MarkRefundCanceledCommand } from '@/application/payment/commands/mark-refund-canceled';
import { MarkRefundFailedCommand } from '@/application/payment/commands/mark-refund-failed';
import { MarkRefundIssuedCommand } from '@/application/payment/commands/mark-refund-issued';
import { PAYMENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Payment } from '@/domain/payment/payment';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { Refund, RefundStatus } from '@/domain/payment/refund';
import { AllowedCurrencies, GatewayMetadata, NotSupportedCurrencyError } from '@/domain/shared';

import { STRIPE_API_MAX_LIST_LIMIT } from './stripe-webhook.constant';
import { StripeGatewayRepositoryPort } from '../../ports/stripe-gateway-repository.port';

@Injectable()
export class StripeRefundWebhookService {
  constructor(
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(STRIPE_GATEWAY_REPOSITORY_TOKEN)
    private readonly stripeGatewayRepository: StripeGatewayRepositoryPort,
    private readonly commandBus: CommandBus,
    private readonly stripe: Stripe,
  ) {}

  async handle(event: Stripe.Event) {
    const data = event.data.object;

    switch (event.type) {
      case 'charge.refunded':
        return this.handleChargeRefunded(data as Stripe.Charge);
      case 'charge.refund.updated':
        return this.handleRefundUpdated(data as Stripe.Refund);
      default:
        break;
    }
  }

  private async handleChargeRefunded(stripeCharge: Stripe.Charge) {
    const { data: stripeRefunds } = await this.stripe.refunds.list({
      charge: stripeCharge.id,
      limit: STRIPE_API_MAX_LIST_LIMIT,
    });

    const { paymentIntentId } =
      await this.stripeGatewayRepository.getPaymentIntentIdByGatewayPaymentIntentId(
        stripeCharge.payment_intent as string,
      );

    const payment = await this.paymentRepository.findByPaymentIntentId(paymentIntentId);

    await this.syncStripeRefunds(payment, stripeRefunds);
  }

  private async handleRefundUpdated(stripeRefund: Stripe.Refund) {
    const payment = await this.paymentRepository.findByGatewayRefundId(stripeRefund.id);

    const refund = this.findRefundByStripeRefundId(payment, stripeRefund.id);

    await this.updateRefundIfNeeded(refund, stripeRefund);
  }

  private async syncStripeRefunds(payment: Payment, stripeRefunds: Stripe.Refund[]): Promise<void> {
    const refundsByGatewayRefundId = keyBy(
      payment.getProps().refunds,
      (refund) => refund.getProps().gatewayRefund.getProps().idAtGateway,
    );

    for (const stripeRefund of stripeRefunds) {
      const refund = refundsByGatewayRefundId[stripeRefund.id];

      if (!refund) {
        await this.createManualRefund(payment, stripeRefund);
      } else {
        await this.updateRefundIfNeeded(refund, stripeRefund);
      }
    }
  }

  private findRefundByStripeRefundId(payment: Payment, stripeRefundId: string) {
    const { refunds } = payment.getProps();

    return refunds.find(
      (x) => x.getProps().gatewayRefund.getProps().idAtGateway === stripeRefundId,
    );
  }

  private hasRefundBeenUpdated(refund: Refund, stripeRefund: Stripe.Refund) {
    return refund.getProps().status !== this.mapStripeRefundStatus(stripeRefund.status);
  }

  private async updateRefundIfNeeded(refund: Refund, stripeRefund: Stripe.Refund) {
    if (this.hasRefundBeenUpdated(refund, stripeRefund)) {
      await this.updateRefund(refund, stripeRefund);
    }
  }

  private async updateRefund(refund: Refund, stripeRefund: Stripe.Refund) {
    const newStatus = this.mapStripeRefundStatus(stripeRefund.status);

    switch (newStatus) {
      case RefundStatus.ISSUED:
        return this.commandBus.execute<MarkRefundIssuedCommand, Refund>(
          new MarkRefundIssuedCommand({
            refundId: refund.getId(),
          }),
        );
      case RefundStatus.CANCELED:
        return this.commandBus.execute<MarkRefundCanceledCommand, Refund>(
          new MarkRefundCanceledCommand({
            refundId: refund.getId(),
          }),
        );
      case RefundStatus.FAILED:
        return this.commandBus.execute<MarkRefundFailedCommand, Refund>(
          new MarkRefundFailedCommand({
            refundId: refund.getId(),
          }),
        );
      default:
        throw new Error(`Invalid new status for refund ${newStatus}`);
    }
  }

  private async createManualRefund(payment: Payment, stripeRefund: Stripe.Refund) {
    return this.commandBus.execute<CreateManualRefundCommand, Refund>(
      new CreateManualRefundCommand({
        paymentId: payment.getId(),
        refundSplits: [],
        amount: stripeRefund.amount,
        currency: this.mapToMoneyCurrency(stripeRefund.currency),
        gatewayRefundId: stripeRefund.id,
        status: this.mapStripeRefundStatus(stripeRefund.status),
        metadata: (stripeRefund.metadata || {}) as GatewayMetadata,
      }),
    );
  }

  private mapStripeRefundStatus(stripeRefundStatus: Stripe.Refund['status']): RefundStatus {
    switch (stripeRefundStatus) {
      case 'pending':
        return RefundStatus.PENDING;
      case 'succeeded':
        return RefundStatus.ISSUED;
      case 'canceled':
        return RefundStatus.CANCELED;
      case 'failed':
        return RefundStatus.FAILED;
      case 'requires_action':
        throw new Error('Refund "requires_action" status not implemented');
      default:
        throw new Error('Invalid refund status');
    }
  }

  private mapToMoneyCurrency(currency: string) {
    switch (currency.toLowerCase()) {
      case 'usd':
        return AllowedCurrencies.USD;
      default:
        throw new NotSupportedCurrencyError();
    }
  }
}
