import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import Stripe from 'stripe';

import { CreateManualPaymentSplitRefundCommand } from '@/application/payment-split/commands/create-manual-payment-split-refund';
import {
  GATEWAY_TRANSFER_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';
import { GatewayTransferRepositoryPort } from '@/domain/shared';
import {
  GatewayTransfer,
  GatewayTransferSource,
  GatewayTransferStatus,
} from '@/domain/shared/interfaces';

import { STRIPE_API_MAX_LIST_LIMIT } from './stripe-webhook.constant';

@Injectable()
export class StripeTransferWebhookService {
  constructor(
    @Inject(GATEWAY_TRANSFER_REPOSITORY_TOKEN)
    private readonly gatewayTransferRepository: GatewayTransferRepositoryPort,
    @Inject(PAYMENT_SPLIT_REPOSITORY_TOKEN)
    private readonly paymentSplitRepository: PaymentSplitRepositoryPort,
    private readonly commandBus: CommandBus,
    private readonly stripe: Stripe,
  ) {}

  async handle(event: Stripe.Event) {
    const data = event.data.object;

    const isPlatformTriggeredWebhook = !event.request.id;

    switch (event.type) {
      case 'transfer.reversed':
        return this.handleTransferReversed(data as Stripe.Transfer, isPlatformTriggeredWebhook);
      default:
        break;
    }
  }

  private async handleTransferReversed(
    stripeTransfer: Stripe.Transfer,
    isPlatformTriggeredWebhook: boolean,
  ) {
    const { data: stripeTransferReversals } = await this.stripe.transfers.listReversals(
      stripeTransfer.id,
      {
        limit: STRIPE_API_MAX_LIST_LIMIT,
      },
    );

    const gatewayTransfer = await this.gatewayTransferRepository.findByGatewayTransferId(
      stripeTransfer.id,
    );

    if (!gatewayTransfer) {
      return;
    }

    if (gatewayTransfer.source === GatewayTransferSource.PAYMENT_SPLIT) {
      await this.handlePaymentSplitTransferReversed({
        paymentSplitId: gatewayTransfer.sourceId,
        gatewayTransfer,
        stripeTransferReversals,
        isPlatformTriggeredWebhook,
      });
    }
  }

  private async handlePaymentSplitTransferReversed(params: {
    paymentSplitId: string;
    gatewayTransfer: GatewayTransfer;
    stripeTransferReversals: Stripe.TransferReversal[];
    isPlatformTriggeredWebhook: boolean;
  }) {
    const paymentSplit = await this.paymentSplitRepository.findById(params.paymentSplitId);

    await this.syncStripeTransferReversals({
      paymentSplit,
      gatewayTransfer: params.gatewayTransfer,
      stripeTransferReversals: params.stripeTransferReversals,
      isPlatformTriggeredWebhook: params.isPlatformTriggeredWebhook,
    });
  }

  private async syncStripeTransferReversals(params: {
    paymentSplit: PaymentSplit;
    gatewayTransfer: GatewayTransfer;
    stripeTransferReversals: Stripe.TransferReversal[];
    isPlatformTriggeredWebhook: boolean;
  }): Promise<void> {
    const paymentSplitRefundsByGatewayRefundId =
      await this.getPaymentSplitRefundsByGatewayTransferId(
        params.paymentSplit,
        params.stripeTransferReversals.map(({ id }) => id),
      );

    for (const stripeTransferReversal of params.stripeTransferReversals) {
      const paymentSplitRefund = paymentSplitRefundsByGatewayRefundId[stripeTransferReversal.id];

      if (params.isPlatformTriggeredWebhook && !paymentSplitRefund) {
        await this.createManualPaymentSplitRefund(
          params.paymentSplit,
          params.gatewayTransfer,
          stripeTransferReversal,
        );
      }

      if (!params.isPlatformTriggeredWebhook && !paymentSplitRefund) {
        throw new BadRequestException(
          `Payment split refund not found by transfer reversal id: ${stripeTransferReversal.id}`,
        );
      }
    }
  }

  private async createManualPaymentSplitRefund(
    paymentSplit: PaymentSplit,
    gatewayTransfer: GatewayTransfer,
    stripeTransferReversal: Stripe.TransferReversal,
  ) {
    await this.commandBus.execute(
      new CreateManualPaymentSplitRefundCommand({
        paymentSplitId: paymentSplit.getId(),
        amount: stripeTransferReversal.amount,
        gatewayTransferId: stripeTransferReversal.id,
        status: GatewayTransferStatus.SETTLED,
        gatewayAccountId: gatewayTransfer.destinationAccountId,
      }),
    );
  }

  private async getPaymentSplitRefundsByGatewayTransferId(
    paymentSplit: PaymentSplit,
    stripeTransferReversalIds: string[],
  ) {
    const gatewayTransfers =
      await this.gatewayTransferRepository.findByGatewayTransferIds(stripeTransferReversalIds);

    const { refunds } = paymentSplit.getProps();

    return refunds.reduce((acc, refund) => {
      const gatewayTransfer = gatewayTransfers.find((x) => x.sourceId === refund.getId());

      if (gatewayTransfer) {
        acc[gatewayTransfer.idAtGateway] = refund;
      }

      return acc;
    }, {});
  }
}
