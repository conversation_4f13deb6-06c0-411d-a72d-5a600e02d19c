import { Inject, Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import Strip<PERSON> from 'stripe';

import { STRIPE_GATEWAY_REPOSITORY_TOKEN } from '@/application/di-tokens';
import { ConfirmPaymentIntentCommand } from '@/application/payment-intent/commands/confirm-payment-intent';
import { PaymentMethodApplicationService } from '@/application/payment-method/services';
import { GatewayType, PaymentMethodType } from '@/domain/shared';

import { StripeGatewayRepositoryPort } from '../../ports/stripe-gateway-repository.port';

@Injectable()
export class StripePaymentIntentWebhookService {
  constructor(
    @Inject(STRIPE_GATEWAY_REPOSITORY_TOKEN)
    private readonly stripeGatewayRepository: StripeGatewayRepositoryPort,
    private readonly commandBus: CommandBus,
    private readonly paymentMethodApplicationService: PaymentMethodApplicationService,
  ) {}

  async handle(event: Stripe.Event) {
    const stripePaymentIntent = event.data.object as Stripe.PaymentIntent & {
      charges: Stripe.ApiList<Stripe.Charge>;
    };

    const { paymentIntentId } =
      await this.stripeGatewayRepository.getPaymentIntentIdByGatewayPaymentIntentId(
        stripePaymentIntent.id,
      );

    const stripeCharge = stripePaymentIntent.charges.data[0];

    const stripePaymentMethodDetails = stripeCharge.payment_method_details;
    const stripePaymentMethodId = stripeCharge.payment_method;

    const paymentMethod = await this.createOrGetPaymentMethod(
      stripePaymentMethodId,
      stripePaymentMethodDetails,
    );

    switch (event.type) {
      case 'payment_intent.succeeded':
        return this.confirmPaymentIntent(paymentIntentId, paymentMethod.getId());
      default:
        break;
    }
  }

  private async createOrGetPaymentMethod(
    stripePaymentMethodId: string,
    stripePaymentMethodDetails: Stripe.Charge.PaymentMethodDetails,
  ) {
    return this.paymentMethodApplicationService.createOrGet({
      type: this.mapStripePaymentMethod(
        stripePaymentMethodDetails.type as Stripe.PaymentMethod.Type,
      ),
      cardExpiryMonth: stripePaymentMethodDetails.card.exp_month,
      cardExpiryYear: stripePaymentMethodDetails.card.exp_year,
      cardLast4: stripePaymentMethodDetails.card.last4,
      gatewayPaymentMethod: {
        idAtGateway: stripePaymentMethodId,
        fingerprint: this.getFingerprint(stripePaymentMethodDetails),
        gatewayType: GatewayType.STRIPE,
      },
    });
  }

  private async confirmPaymentIntent(paymentIntentId: string, paymentMethodId: string) {
    return this.commandBus.execute<ConfirmPaymentIntentCommand, void>(
      new ConfirmPaymentIntentCommand({
        paymentIntentId,
        paymentMethodId,
      }),
    );
  }

  private getFingerprint(stripePaymentMethod: Stripe.Charge.PaymentMethodDetails): string | null {
    switch (stripePaymentMethod.type) {
      case 'card':
        return stripePaymentMethod.card?.fingerprint;
      case 'us_bank_account':
        return stripePaymentMethod.us_bank_account?.fingerprint;
      default:
        return null;
    }
  }

  private mapStripePaymentMethod(
    stripePaymentMethodType: Stripe.PaymentMethod.Type,
  ): PaymentMethodType {
    switch (stripePaymentMethodType) {
      case 'card':
        return PaymentMethodType.CARD;
      case 'us_bank_account':
        return PaymentMethodType.ACH;
      default:
        throw new Error('Unsupported payment method');
    }
  }
}
