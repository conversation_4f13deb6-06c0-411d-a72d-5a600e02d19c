/* eslint-disable @typescript-eslint/no-floating-promises */
import { Inject, Injectable } from '@nestjs/common';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';
import Stripe from 'stripe';

import { STRIPE_GATEWAY_REPOSITORY_TOKEN } from '@/application/di-tokens';
import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import { GATEWAY_SERVICE_FACTORY_TOKEN, GATEWAY_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { Gateway, GatewayServiceFactoryPort, GatewayType } from '@/domain/shared';

import { StripeDisputeWebhookService } from './dispute-webhook.service';
import { StripePaymentIntentWebhookService } from './payment-intent-webhook.service';
import { StripeRefundWebhookService } from './refund-webhook.service';
import { StripeTransferWebhookService } from './transfer-webhook.service';
import { StripeGatewayRepositoryPort } from '../../ports/stripe-gateway-repository.port';

const { gatewayWebhook: gatewayWebhookExchange } = RABBIT_MQ_EXCHANGES;
@Injectable()
export class StripeWebhookService {
  constructor(
    private readonly db: DatabaseService,
    private readonly paymentIntentWebhook: StripePaymentIntentWebhookService,
    private readonly stripeDisputeWebhook: StripeDisputeWebhookService,
    private readonly refundWebhook: StripeRefundWebhookService,
    private readonly transferWebhook: StripeTransferWebhookService,
    @Inject(STRIPE_GATEWAY_REPOSITORY_TOKEN)
    private readonly stripeGatewayRepository: StripeGatewayRepositoryPort,
    @Inject(GATEWAY_REPOSITORY_TOKEN)
    private readonly gatewayRepository: GatewayRepositoryPort,
    @Inject(GATEWAY_SERVICE_FACTORY_TOKEN)
    private readonly gatewayServiceFactory: GatewayServiceFactoryPort,
    private readonly rabbitMqClient: RabbitMQClient,
  ) {}

  async validateAndEnqueueWebhook(data: Buffer, req: Request) {
    const event = await this.getValidatedWebhookEvent(data, req);

    await this.db.$transaction(async () => {
      const { isDuplicate } = await this.stripeGatewayRepository.saveWebhookEvent(event);

      const handler = this.getWebhookHandler(event);

      if (!handler || isDuplicate) {
        return;
      }

      await this.rabbitMqClient.publish(
        gatewayWebhookExchange.name,
        gatewayWebhookExchange.routingKeys.stripe.name,
        event,
      );
    });
  }

  async processWebhook(event: Stripe.Event) {
    await this.db.$transaction(async () => {
      const handler = this.getWebhookHandler(event);

      if (!handler) {
        return;
      }

      await handler.handle(event);
    });
  }

  private getWebhookHandler(event: Stripe.Event) {
    if (this.isAccountWebhook(event)) {
      return this.getAccountWebhookHandler(event);
    }

    return this.getRegularWebhookHandler(event);
  }

  private isAccountWebhook(event: Stripe.Event) {
    return !!event.account;
  }

  private getRegularWebhookHandler(event: Stripe.Event) {
    switch (event.type) {
      // Occurs when a PaymentIntent transitions to requires_action state
      case 'payment_intent.requires_action':
      // Occurs when a PaymentIntent has successfully completed payment
      case 'payment_intent.succeeded':
      // Occurs when a PaymentIntent has started processing
      case 'payment_intent.processing':
      // Occurs when a PaymentIntent has failed the attempt to create a payment method or a payment.
      case 'payment_intent.payment_failed':
        return this.paymentIntentWebhook;
      // Occurs when a charge is created (partial, full)
      case 'charge.refunded':
      // Occurs when a refund for charge is updated
      case 'charge.refund.updated':
        return this.refundWebhook;
      // Occurs when transfer is reversed
      case 'transfer.reversed':
        return this.transferWebhook;
      // Dispute events
      // Occurs whenever a customer disputes a charge with their bank (chargeback)
      case 'charge.dispute.created':
      // Occurs when the dispute is resolved and the dispute status changes to won or lost
      case 'charge.dispute.closed':
      // Occurs when funds are reinstated to your account after a dispute is won
      case 'charge.dispute.funds_reinstated':
      // Occurs when funds are removed from your account due to a dispute
      case 'charge.dispute.funds_withdrawn':
      // Occurs when the dispute is updated (usually with evidence)
      case 'charge.dispute.updated':
        return this.stripeDisputeWebhook;
      default:
        break;
    }
  }

  private getAccountWebhookHandler(event: Stripe.Event) {}

  private async getValidatedWebhookEvent(webhookData: Buffer, req: Request) {
    const gateway = await this.gatewayRepository.findByGatewayType(GatewayType.STRIPE);

    if (!gateway) {
      throw Error(`Gateway not setup for gateway ${GatewayType.STRIPE}`); // todo: custom error
    }
    const gatewayService = this.gatewayServiceFactory.getGateway(
      new Gateway(gateway.getId(), gateway.getProps().type),
    );

    return gatewayService.getValidatedWebhookData(req, webhookData) as Stripe.Event;
  }
}
