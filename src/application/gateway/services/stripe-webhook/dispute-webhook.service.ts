import { Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import Strip<PERSON> from 'stripe';

import { CloseDisputeCommand } from '@/application/dispute/commands/close-dispute';
import {
  CreateDisputeCommand,
  CreateDisputeCommandProps,
} from '@/application/dispute/commands/create-dispute';
import { ProcessDisputeFundsRestoredCommand } from '@/application/dispute/commands/dispute-funds-restored';
import { ProcessDisputeFundsWithdrawalCommand } from '@/application/dispute/commands/dispute-funds-withdrawn';
import { UpdateDisputeCommand } from '@/application/dispute/commands/update-dispute';
import { AllowedCurrencies, GatewayType } from '@/domain/shared';

type GatewayCommandProps = Stripe.Dispute & { gatewayType: GatewayType };

@Injectable()
export class StripeDisputeWebhookService {
  constructor(private readonly commandBus: CommandBus) {}

  async handle(event: Stripe.Event) {
    const commandProps = {
      ...(event.data.object as Stripe.Dispute),
      gatewayType: GatewayType.STRIPE,
    };

    switch (event.type) {
      case 'charge.dispute.created':
        return this.createDispute(commandProps);
      case 'charge.dispute.funds_reinstated':
        return this.disputeFundsRestored(commandProps);
      case 'charge.dispute.funds_withdrawn':
        return this.disputeFundsWithdrawn(commandProps);
      case 'charge.dispute.closed':
        return this.closeDispute(commandProps);
      case 'charge.dispute.updated':
        return this.updateDispute(commandProps);
      default:
        break;
    }
  }

  private mapEventToCommandProps(disputeEvent: GatewayCommandProps): CreateDisputeCommandProps {
    return {
      gatewayType: disputeEvent.gatewayType,
      gatewayDisputeId: disputeEvent.id,
      gatewayPaymentIntentId: disputeEvent.payment_intent as string,
      amount: disputeEvent.amount,
      currency: disputeEvent.currency as AllowedCurrencies,
      status: disputeEvent.status,
      reason: disputeEvent.reason,
      evidence: disputeEvent.evidence,
      evidenceDetails: disputeEvent.evidence_details,
    };
  }

  private async createDispute(stripeDispute: GatewayCommandProps) {
    const props = this.mapEventToCommandProps(stripeDispute);

    return this.commandBus.execute<CreateDisputeCommand, void>(new CreateDisputeCommand(props));
  }

  private async updateDispute(stripeDispute: GatewayCommandProps) {
    const props = this.mapEventToCommandProps(stripeDispute);

    return this.commandBus.execute<UpdateDisputeCommand, void>(new UpdateDisputeCommand(props));
  }

  private async closeDispute(stripeDispute: GatewayCommandProps) {
    const props = this.mapEventToCommandProps(stripeDispute);

    return this.commandBus.execute<CloseDisputeCommand, void>(new CloseDisputeCommand(props));
  }

  private async disputeFundsWithdrawn(stripeDispute: GatewayCommandProps) {
    const props = this.mapEventToCommandProps(stripeDispute);

    return this.commandBus.execute<ProcessDisputeFundsWithdrawalCommand, void>(
      new ProcessDisputeFundsWithdrawalCommand({
        ...props,
        transactionsData: stripeDispute.balance_transactions.map((balanceTransaction) => ({
          amount: balanceTransaction.amount,
          fee: balanceTransaction.fee
        }))
      })
    );
  }

  private async disputeFundsRestored(stripeDispute: GatewayCommandProps) {
    const props = this.mapEventToCommandProps(stripeDispute);

    return this.commandBus.execute<ProcessDisputeFundsRestoredCommand, void>(
      new ProcessDisputeFundsRestoredCommand({
        ...props,
        transactionsData: stripeDispute.balance_transactions.map((balanceTransaction) => ({
          amount: balanceTransaction.amount,
          fee: balanceTransaction.fee
        }))
      })
    );
  }
}
