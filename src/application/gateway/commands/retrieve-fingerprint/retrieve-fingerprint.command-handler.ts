import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { GATEWAY_REPOSITORY_TOKEN, GATEWAY_SERVICE_FACTORY_TOKEN } from '@/domain/di-tokens';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { Gateway, GatewayServiceFactoryPort, GatewayType } from '@/domain/shared';

import { RetrieveFingerprintCommand } from './retrieve-fingerprint.command';

@CommandHandler(RetrieveFingerprintCommand)
export class RetrieveFingerprintCommandHandler implements ICommandHandler {
  constructor(
    @Inject(GATEWAY_REPOSITORY_TOKEN)
    private readonly gatewayRepository: GatewayRepositoryPort,
    @Inject(GATEWAY_SERVICE_FACTORY_TOKEN)
    private readonly gatewayServiceFactory: GatewayServiceFactoryPort,
  ) {}

  async execute(command: RetrieveFingerprintCommand): Promise<{ fingerprint: string | null }> {
    const { paymentMethodIdAtGateway, gatewayType } = command.getProps();

    const gateway = await this.getGateway(gatewayType);

    const gatewayService = this.gatewayServiceFactory.getGateway(gateway);

    const fingerprint =
      await gatewayService.getFingerprintByPaymentMethodId(paymentMethodIdAtGateway);

    return { fingerprint };
  }

  private async getGateway(gatewayType: GatewayType) {
    const gateway = await this.gatewayRepository.findByGatewayType(gatewayType);

    if (!gateway) {
      throw new Error('Gateway not found');
    }

    return new Gateway(gateway.getId(), gateway.getProps().type);
  }
}
