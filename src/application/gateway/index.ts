import { RetrieveFingerprintCommandHandler } from './commands/retrieve-fingerprint';
import { StripeWebhookService } from './services/stripe-webhook';
import { StripeDisputeWebhookService } from './services/stripe-webhook/dispute-webhook.service';
import { StripePaymentIntentWebhookService } from './services/stripe-webhook/payment-intent-webhook.service';
import { StripeRefundWebhookService } from './services/stripe-webhook/refund-webhook.service';
import { StripeTransferWebhookService } from './services/stripe-webhook/transfer-webhook.service';

export const GATEWAY_APPLICATION_SERVICES = [
  StripeWebhookService,
  StripeRefundWebhookService,
  StripePaymentIntentWebhookService,
  StripeTransferWebhookService,
  StripeDisputeWebhookService,
];

export const GATEWAY_COMMAND_HANDLERS = [RetrieveFingerprintCommandHandler];
