import { Command } from '@/core/ddd';
import { GatewayMetadata } from '@/domain/shared';

type RefundSplitData = {
  accountId: string;
  amount: number;
};

type Props = {
  paymentId: string;
  amount: number;
  currency: string;
  refundSplits: RefundSplitData[];
  metadata?: GatewayMetadata;
  marketplaceOrderFee: number;
  platformOrderFee: number;
};

export class CreateRefundCommand extends Command<Props> {}
