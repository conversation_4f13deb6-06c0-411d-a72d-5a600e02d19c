import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { PAYMENT_REPOSITORY_TOKEN, PAYMENT_SPLIT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { Refund } from '@/domain/payment/refund';
import { CreateRefundDomainService } from '@/domain/payment/services';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';
import { Money } from '@/domain/shared';

import { CreateRefundCommand } from './create-refund.command';

@CommandHandler(CreateRefundCommand)
export class CreateRefundCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(PAYMENT_SPLIT_REPOSITORY_TOKEN)
    private readonly paymentSplitRepository: PaymentSplitRepositoryPort,
    private readonly createRefundService: CreateRefundDomainService,
  ) {}

  async execute(command: CreateRefundCommand): Promise<Refund> {
    const {
      paymentId,
      amount,
      currency,
      refundSplits,
      metadata,
      platformOrderFee,
      marketplaceOrderFee,
    } = command.getProps();

    const payment = await this.paymentRepository.findById(paymentId);

    if (!payment) {
      throw new Error('Invalid payment'); // todo: custom error
    }

    const paymentSplits = await this.paymentSplitRepository.findByPaymentId(paymentId);

    return this.db.$transaction(async () => {
      const refund = await this.createRefundService.createRefund({
        payment,
        paymentSplits,
        refundSplits: refundSplits.map((x) => ({
          amount: Money.from(x.amount, currency),
          accountId: x.accountId,
        })),
        refundAmount: Money.from(amount, currency),
        metadata,
        marketplaceOrderFee: Money.from(marketplaceOrderFee, currency),
        platformOrderFee: Money.from(platformOrderFee, currency),
      });

      await this.paymentRepository.update(payment);
      await Promise.all(
        paymentSplits.map(async (paymentSplit) => this.paymentSplitRepository.update(paymentSplit)),
      );

      return refund;
    });
  }
}
