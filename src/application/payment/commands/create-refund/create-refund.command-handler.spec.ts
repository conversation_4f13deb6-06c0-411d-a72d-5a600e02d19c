import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import { PAYMENT_REPOSITORY_TOKEN, PAYMENT_SPLIT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Payment } from '@/domain/payment/payment';
import { Refund } from '@/domain/payment/refund';
import { CreateRefundDomainService } from '@/domain/payment/services';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { Money } from '@/domain/shared';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { PaymentSplitRepositoryMock } from '@/test/mocks/repositories/payment-split-repository.mock';
import { CreateRefundDomainServiceMock } from '@/test/mocks/services/create-refund-domain-service.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

import { CreateRefundCommand, CreateRefundCommandHandler } from '.';

describe('CreateRefundCommandHandler', () => {
  let handler: CreateRefundCommandHandler;
  let db: DatabaseServiceMock;
  let paymentRepository: PaymentRepositoryMock;
  let paymentSplitRepository: PaymentSplitRepositoryMock;
  let createRefundService: CreateRefundDomainServiceMock;
  let commandProps: CreateRefundCommand['props'];
  let payment: Payment;
  let findPaymentSplitsSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateRefundCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: PAYMENT_SPLIT_REPOSITORY_TOKEN, useClass: PaymentSplitRepositoryMock },
        { provide: CreateRefundDomainService, useClass: CreateRefundDomainServiceMock },
      ],
    }).compile();

    handler = module.get<CreateRefundCommandHandler>(CreateRefundCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    paymentSplitRepository = module.get<PaymentSplitRepositoryMock>(PAYMENT_SPLIT_REPOSITORY_TOKEN);
    createRefundService = module.get<CreateRefundDomainServiceMock>(CreateRefundDomainService);
    [payment] = paymentRepository.db;
    commandProps = {
      paymentId: payment.getId(),
      amount: 100,
      currency: 'USD',
      refundSplits: [
        { accountId: 'account-id-0', amount: 5 },
        { accountId: 'account-id-1', amount: 5 },
      ],
      metadata: { key: 'value' },
      platformOrderFee: 10,
      marketplaceOrderFee: 5,
    };
    createRefundService.createRefund = jest.fn();
    paymentRepository.update = jest.fn();
    paymentSplitRepository.update = jest.fn();
    findPaymentSplitsSpy = jest.spyOn(paymentSplitRepository, 'findByPaymentId');

    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payment is not found', async () => {
    const command = new CreateRefundCommand({
      ...commandProps,
      paymentId: 'invalid-payment-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Invalid payment'));
  });

  it('should trigger repository update foreach payment split', async () => {
    const command = new CreateRefundCommand(commandProps);

    await handler.execute(command);

    const paymentSplits = await findPaymentSplitsSpy.mock.results[0].value as PaymentSplit[];
    // Ensure we have at least one payment split to test the update
    expect(paymentSplits.length).toBeGreaterThanOrEqual(1);
    paymentSplits.forEach((paymentSplit) => {
      expect(paymentSplitRepository.update).toHaveBeenCalledWith(paymentSplit);
    });
  });

  it('should trigger payment repository update', async () => {
    const command = new CreateRefundCommand(commandProps);

    await handler.execute(command);

    expect(paymentRepository.update).toHaveBeenCalledWith(payment);
  });

  it('should create and return refund', async () => {
    const command = new CreateRefundCommand(commandProps);
    const mockRefund = { } as Refund;
    createRefundService.createRefund = jest.fn().mockReturnValue(mockRefund);

    const result = await handler.execute(command);

    const paymentSplits = await findPaymentSplitsSpy.mock.results[0].value as PaymentSplit[];
    // Ensure we have at least one refund split to test the mapping
    expect(commandProps.refundSplits.length).toBeGreaterThan(0);
    expect(createRefundService.createRefund).toHaveBeenCalledWith({
      payment,
      paymentSplits,
      refundSplits: commandProps.refundSplits.map((refundSplit) => ({
        amount: Money.from(refundSplit.amount, commandProps.currency),
        accountId: refundSplit.accountId,
      })),
      refundAmount: Money.from(commandProps.amount, commandProps.currency),
      metadata: commandProps.metadata,
      marketplaceOrderFee: Money.from(commandProps.marketplaceOrderFee, commandProps.currency),
      platformOrderFee: Money.from(commandProps.platformOrderFee, commandProps.currency),
    });
    expect(result).toBe(mockRefund);
  });

  it('should operate within a transaction', async () => {
    const command = new CreateRefundCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(createRefundService.createRefund).not.toHaveBeenCalled();
    expect(paymentRepository.update).not.toHaveBeenCalled();
    expect(paymentSplitRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    const paymentSplits = await findPaymentSplitsSpy.mock.results[0].value as PaymentSplit[];
    expect(createRefundService.createRefund).toHaveBeenCalled();
    expect(paymentRepository.update).toHaveBeenCalled();
    expect(paymentSplitRepository.update).toHaveBeenCalledTimes(paymentSplits.length);
  });
});
