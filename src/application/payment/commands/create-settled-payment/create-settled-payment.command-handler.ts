import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';

import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import { AccountRepositoryPort } from '@/domain/account/ports';
import {
  ACCOUNT_REPOSITORY_TOKEN,
  GATEWAY_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { PaymentSettledIntegrationEvent } from '@/domain/payment/integration-events';
import { Payment } from '@/domain/payment/payment';
import { PaymentSplitData } from '@/domain/payment/payment-split-data';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';
import { Gateway, Money } from '@/domain/shared';

import { CreateSettledPaymentCommand } from './create-settled-payment.command';

@CommandHandler(CreateSettledPaymentCommand)
export class CreateSettledPaymentCommandHandler implements ICommandHandler {
  constructor(
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(GATEWAY_REPOSITORY_TOKEN)
    private readonly gatewayRepository: GatewayRepositoryPort,
    @Inject(PAYMENT_SPLIT_REPOSITORY_TOKEN)
    private readonly paymentSplitRepository: PaymentSplitRepositoryPort,
    @Inject(PAYMENT_INTENT_REPOSITORY_TOKEN)
    private readonly paymentIntentRepository: PaymentIntentRepositoryPort,
    @Inject(ACCOUNT_REPOSITORY_TOKEN)
    private readonly accountRepository: AccountRepositoryPort,
    private readonly db: DatabaseService,
    private readonly rabbitClient: RabbitMQClient,
  ) {}

  async execute(command: CreateSettledPaymentCommand): Promise<Payment> {
    const {
      gatewayType,
      amount,
      currency,
      paymentMethodType,
      customerId,
      paymentSplits: paymentSplitsData,
      paymentIntentId,
      marketplaceId,
      paymentMethodId,
    } = command.getProps();

    const gateway = await this.gatewayRepository.findByGatewayType(gatewayType);

    if (!gateway) {
      throw new Error('invalid gateway'); // todo: custom error
    }

    const paymentIntent = await this.paymentIntentRepository.findById(paymentIntentId);

    if (!paymentIntent) {
      throw new Error('Invalid payment intent');
    }

    const paymentIntentProps = paymentIntent.getProps();

    const payment = Payment.create({
      paymentIntentId,
      paymentMethodType,
      marketplaceId,
      providerFee: paymentIntentProps.providerFee, // todo: add actual provider fee as well
      amount: Money.from(amount, currency),
      customerId,
      gateway: new Gateway(gateway.getId(), gateway.getProps().type),
      platformPaymentFee: paymentIntentProps.platformPaymentFee,
      platformPaymentFeeFixed: paymentIntentProps.platformPaymentFeeFixed,
      platformPaymentFeePercentage: paymentIntentProps.platformPaymentFeePercentage,
      marketplacePaymentFee: paymentIntentProps.marketplacePaymentFee,
      marketplacePaymentFeeFixed: paymentIntentProps.marketplacePaymentFeeFixed,
      marketplacePaymentFeePercentage: paymentIntentProps.marketplacePaymentFeePercentage,
      providerFeeFixed: paymentIntentProps.providerFeeFixed,
      providerFeePercentage: paymentIntentProps.providerFeePercentage,
      platformOrderFee: paymentIntentProps.platformOrderFee,
      marketplaceOrderFee: paymentIntentProps.marketplaceOrderFee,
      paymentMethodId,
      feeSettingsId: paymentIntentProps.feeSettingsId,
    });

    payment.markSettled();

    await this.db.$transaction(async () => {
      await this.paymentRepository.create(payment);

      await this.createPaymentSplits(
        payment,
        paymentSplitsData.map((paymentSplit) => ({
          accountId: paymentSplit.accountId,
          amount: Money.from(paymentSplit.amount, currency),
        })),
      );
    });

    await this.rabbitClient.publish(
      RABBIT_MQ_EXCHANGES.payment.name,
      RABBIT_MQ_EXCHANGES.payment.routingKeys.paymentSettled.name,
      new PaymentSettledIntegrationEvent({ aggregateId: payment.getId(), paymentIntentId }),
    );

    return payment;
  }

  async createPaymentSplits(payment: Payment, paymentSplitsData: PaymentSplitData[]) {
    const { gateway, id: paymentId } = payment.getProps();

    const promises = paymentSplitsData.map(async (paymentSplitData) => {
      const { accountId, amount } = paymentSplitData;

      await this.validateGatewayAccount(accountId, gateway);

      const paymentSplit = PaymentSplit.create({
        accountId,
        paymentId,
        amount,
        gateway,
      });

      await this.paymentSplitRepository.create(paymentSplit);

      return paymentSplit;
    });

    return Promise.all(promises);
  }

  private async validateGatewayAccount(accountId: string, gateway: Gateway) {
    const receivingAccount = await this.accountRepository.findById(accountId);

    if (!receivingAccount) {
      throw new Error('Account not found'); // todo: custom error
    }

    const gatewayAccount = receivingAccount
      .getProps()
      .gatewayAccounts.find((x) => x.getProps().gateway.equals(gateway));

    if (!gatewayAccount) {
      throw new Error('Gateway account not found'); // todo: custom error
    }
  }
}
