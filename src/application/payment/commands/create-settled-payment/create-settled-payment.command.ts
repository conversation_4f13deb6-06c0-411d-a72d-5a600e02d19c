import { Command } from '@/core/ddd';
import { GatewayType, PaymentMethodType } from '@/domain/shared';

type PaymentSplitData = {
  accountId: string;
  amount: number;
};

type Props = {
  marketplaceId: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
  providerFee: number;
  customerId: string;
  gatewayType: GatewayType;
  paymentMethodType: PaymentMethodType;
  paymentSplits: PaymentSplitData[];
  paymentMethodId: string;
};

export class CreateSettledPaymentCommand extends Command<Props> {}
