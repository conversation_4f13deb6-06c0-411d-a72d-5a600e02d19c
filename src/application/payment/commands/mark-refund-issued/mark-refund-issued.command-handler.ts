import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';

import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import { PAYMENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { RefundIssuedIntegrationEvent } from '@/domain/payment/integration-events';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';

import { MarkRefundIssuedCommand } from './mark-refund-issued.command';

@CommandHandler(MarkRefundIssuedCommand)
export class MarkRefundIssuedCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    private readonly rabbitClient: RabbitMQClient,
  ) {}

  async execute(command: MarkRefundIssuedCommand): Promise<void> {
    const { refundId } = command.getProps();

    const payment = await this.paymentRepository.findByRefundId(refundId);

    if (!payment) {
      throw new Error('Invalid payment'); // todo: custom error
    }

    payment.markRefundIssued(refundId);

    await this.db.$transaction(async () => this.paymentRepository.update(payment));

    await this.rabbitClient.publish(
      RABBIT_MQ_EXCHANGES.payment.name,
      RABBIT_MQ_EXCHANGES.payment.routingKeys.refundIssued.name,
      new RefundIssuedIntegrationEvent({ aggregateId: payment.getId(), refundId }),
    );
  }
}
