import { Test, TestingModule } from '@nestjs/testing';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';

import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import { PAYMENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { RefundIssuedIntegrationEvent } from '@/domain/payment/integration-events';
import { Payment } from '@/domain/payment/payment';
import { RabbitMQClientMock } from '@/test/mocks/libs/rabbitmq-client.mock';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

import { MarkRefundIssuedCommand, MarkRefundIssuedCommandHandler } from '.';

describe('MarkRefundIssuedCommandHandler', () => {
  let handler: MarkRefundIssuedCommandHandler;
  let db: DatabaseServiceMock;
  let rabbitClient: RabbitMQClient;
  let paymentRepository: PaymentRepositoryMock;
  let commandProps: MarkRefundIssuedCommand['props'];
  let payment: Payment;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MarkRefundIssuedCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: RabbitMQClient, useClass: RabbitMQClientMock },
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
      ],
    }).compile();

    handler = module.get<MarkRefundIssuedCommandHandler>(MarkRefundIssuedCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    rabbitClient = module.get<RabbitMQClient>(RabbitMQClient);
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    [payment] = paymentRepository.db;
    commandProps = {
      refundId: payment.getProps().refunds[0].getId(),
    };
    payment.markRefundIssued = jest.fn();
    paymentRepository.update = jest.fn();
    rabbitClient.publish = jest.fn();

    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if no payment is found by refund id', async () => {
    const command = new MarkRefundIssuedCommand({
      refundId: 'invalid-refund-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Invalid payment'));
  });

  it('should update payment refund status to issued', async () => {
    const command = new MarkRefundIssuedCommand(commandProps);

    await handler.execute(command);

    expect(payment.markRefundIssued).toHaveBeenCalledWith(commandProps.refundId);
    expect(paymentRepository.update).toHaveBeenCalledWith(payment);
    const [markRefundIssuedInvOrder] = (payment.markRefundIssued as jest.Mock).mock
      .invocationCallOrder;
    const [updateInvOrder] = (paymentRepository.update as jest.Mock).mock.invocationCallOrder;
    expect(markRefundIssuedInvOrder).toBeLessThan(updateInvOrder);
  });

  it('should publish refund issued event after updating payment', async () => {
    const command = new MarkRefundIssuedCommand(commandProps);
    // Mock the Date object to avoid time-based issues
    const fixedDate = new Date();
    jest.spyOn(global, 'Date').mockImplementation(() => fixedDate);
    const transactionSpy = jest.spyOn(db, '$transaction');

    await handler.execute(command);

    expect(transactionSpy).toHaveBeenCalled();
    expect(rabbitClient.publish).toHaveBeenCalledWith(
      RABBIT_MQ_EXCHANGES.payment.name,
      RABBIT_MQ_EXCHANGES.payment.routingKeys.refundIssued.name,
      new RefundIssuedIntegrationEvent({
        aggregateId: payment.getId(),
        refundId: commandProps.refundId,
      }),
    );
    const [transactionInvOrder] = transactionSpy.mock.invocationCallOrder;
    const [publishInvOrder] = (rabbitClient.publish as jest.Mock).mock.invocationCallOrder;
    expect(publishInvOrder).toBeGreaterThan(transactionInvOrder);
  });

  it('should operate within a transaction', async () => {
    const command = new MarkRefundIssuedCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(paymentRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    expect(paymentRepository.update).toHaveBeenCalledWith(payment);
  });
});
