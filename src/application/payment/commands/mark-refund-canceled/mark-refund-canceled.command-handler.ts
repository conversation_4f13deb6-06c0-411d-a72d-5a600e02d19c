import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';

import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import { PAYMENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { RefundCanceledIntegrationEvent } from '@/domain/payment/integration-events';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';

import { MarkRefundCanceledCommand } from './mark-refund-canceled.command';

@CommandHandler(MarkRefundCanceledCommand)
export class MarkRefundCanceledCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    private readonly rabbitClient: RabbitMQClient,
  ) {}

  async execute(command: MarkRefundCanceledCommand): Promise<void> {
    const { refundId } = command.getProps();

    const payment = await this.paymentRepository.findByRefundId(refundId);

    if (!payment) {
      throw new Error('Invalid payment'); // todo: custom error
    }

    payment.markRefundCanceled(refundId);

    await this.db.$transaction(async () => this.paymentRepository.update(payment));

    await this.rabbitClient.publish(
      RABBIT_MQ_EXCHANGES.payment.name,
      RABBIT_MQ_EXCHANGES.payment.routingKeys.refundCanceled.name,
      new RefundCanceledIntegrationEvent({ aggregateId: payment.getId(), refundId }),
    );
  }
}
