import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';

import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import { PAYMENT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { RefundFailedIntegrationEvent } from '@/domain/payment/integration-events';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';

import { MarkRefundFailedCommand } from './mark-refund-failed.command';

@CommandHandler(MarkRefundFailedCommand)
export class MarkRefundFailedCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    private readonly rabbitClient: RabbitMQClient,
  ) {}

  async execute(command: MarkRefundFailedCommand): Promise<void> {
    const { refundId } = command.getProps();

    const payment = await this.paymentRepository.findByRefundId(refundId);

    if (!payment) {
      throw new Error('Invalid payment'); // todo: custom error
    }

    payment.markRefundFailed(refundId);

    await this.db.$transaction(async () => this.paymentRepository.update(payment));

    await this.rabbitClient.publish(
      RABBIT_MQ_EXCHANGES.payment.name,
      RABBIT_MQ_EXCHANGES.payment.routingKeys.refundFailed.name,
      new RefundFailedIntegrationEvent({ aggregateId: payment.getId(), refundId }),
    );
  }
}
