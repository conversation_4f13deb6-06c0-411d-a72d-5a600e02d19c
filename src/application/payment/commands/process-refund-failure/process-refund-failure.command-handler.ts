import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DebtRepositoryPort } from '@/domain/debt/ports';
import {
  DEBT_REPOSITORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { RefundProcessingDomainService } from '@/domain/payment/services';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';

import { ProcessRefundFailureCommand } from './process-refund-failure.command';

@CommandHandler(ProcessRefundFailureCommand)
export class ProcessRefundFailureCommandHandler implements ICommandHandler {
  constructor(
    private readonly refundProcessingDomainService: RefundProcessingDomainService,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(PAYMENT_SPLIT_REPOSITORY_TOKEN)
    private readonly paymentSplitRepository: PaymentSplitRepositoryPort,
    @Inject(DEBT_REPOSITORY_TOKEN)
    private readonly debtRepository: DebtRepositoryPort,
  ) {}

  async execute(command: ProcessRefundFailureCommand): Promise<void> {
    const { paymentId, refundId } = command.getProps();

    const paymentSplits = await this.paymentSplitRepository.findByPaymentId(paymentId);

    const { debts, paymentSplits: updatedPaymentSplits } =
      this.refundProcessingDomainService.processRefundFailure(paymentSplits, refundId);

    await this.paymentRepository.transaction(async () => {
      const debtsCreatePromises = debts.map(async (debt) => this.debtRepository.create(debt));

      const paymentSplitsUpdatePromises = updatedPaymentSplits.map(async (paymentSplit) =>
        this.paymentSplitRepository.update(paymentSplit),
      );

      await Promise.all([...debtsCreatePromises, ...paymentSplitsUpdatePromises]);
    });
  }
}
