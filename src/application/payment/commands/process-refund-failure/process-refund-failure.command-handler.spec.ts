import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import {
  DEBT_REPOSITORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN
} from '@/domain/di-tokens';
import { Payment } from '@/domain/payment/payment';
import { RefundProcessingDomainService } from '@/domain/payment/services';
import { DebtRepositoryMock } from '@/test/mocks/repositories/debt-repository.mock';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { PaymentSplitRepositoryMock } from '@/test/mocks/repositories/payment-split-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import { RefundProcessingDomainServiceMock } from '@/test/mocks/services/refund-processing-domain-service.mock';

import { ProcessRefundFailureCommand, ProcessRefundFailureCommandHandler } from '.';

describe('ProcessRefundFailureCommandHandler', () => {
  let handler: ProcessRefundFailureCommandHandler;
  let db: DatabaseServiceMock;
  let debtRepository: DebtRepositoryMock;
  let paymentRepository: PaymentRepositoryMock;
  let paymentSplitRepository: PaymentSplitRepositoryMock;
  let refundProcessingDomainService: RefundProcessingDomainService;
  let commandProps: ProcessRefundFailureCommand['props'];
  let payment: Payment;
  let findPaymentSplitsSpy: jest.SpyInstance;
  let processRefundFailureSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessRefundFailureCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: RefundProcessingDomainService, useClass: RefundProcessingDomainServiceMock },
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: PAYMENT_SPLIT_REPOSITORY_TOKEN, useClass: PaymentSplitRepositoryMock },
        { provide: DEBT_REPOSITORY_TOKEN, useClass: DebtRepositoryMock },
      ],
    }).compile();

    handler = module.get<ProcessRefundFailureCommandHandler>(ProcessRefundFailureCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    refundProcessingDomainService = module.get<RefundProcessingDomainService>(RefundProcessingDomainService);
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    paymentSplitRepository = module.get<PaymentSplitRepositoryMock>(PAYMENT_SPLIT_REPOSITORY_TOKEN);
    debtRepository = module.get<DebtRepositoryMock>(DEBT_REPOSITORY_TOKEN);
    [payment] = paymentRepository.db;
    commandProps = {
      paymentId: payment.getId(),
      refundId: 'refund-id',
    };
    findPaymentSplitsSpy = jest.spyOn(paymentSplitRepository, 'findByPaymentId');
    processRefundFailureSpy = jest.spyOn(refundProcessingDomainService, 'processRefundFailure')
      .mockReturnValue({
        debts: debtRepository.db,
        paymentSplits: paymentSplitRepository.db,
      }
    );
    paymentRepository.transaction = jest.fn().mockImplementation(db.$transaction.bind(db));
    debtRepository.create = jest.fn();
    paymentSplitRepository.update = jest.fn();

    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should seek payment splits by payment id', async () => {
    const command = new ProcessRefundFailureCommand(commandProps);

    await handler.execute(command);

    expect(findPaymentSplitsSpy).toHaveBeenCalledWith(payment.getId());
  });

  it('should create debts from payment splits', async () => {
    const command = new ProcessRefundFailureCommand(commandProps);
    processRefundFailureSpy.mockReturnValue({
      debts: debtRepository.db,
      paymentSplits: [],
    });

    await handler.execute(command);

    debtRepository.db.forEach((debt) => {
      expect(debtRepository.create).toHaveBeenCalledWith(debt);
    });
  });

  it('should update payment splits', async () => {
    const command = new ProcessRefundFailureCommand(commandProps);
    processRefundFailureSpy.mockReturnValue({
      debts: [],
      paymentSplits: paymentSplitRepository.db,
    });

    await handler.execute(command);

    paymentSplitRepository.db.forEach((paymentSplit) => {
      expect(paymentSplitRepository.update).toHaveBeenCalledWith(paymentSplit);
    });
  });

  it('should operate within a transaction', async () => {
    const command = new ProcessRefundFailureCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(debtRepository.create).not.toHaveBeenCalled();
    expect(paymentSplitRepository.update).not.toHaveBeenCalled();
    expect(paymentRepository.transaction).toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    expect(debtRepository.create).toHaveBeenCalledTimes(debtRepository.db.length);
    expect(paymentSplitRepository.update).toHaveBeenCalledTimes(paymentSplitRepository.db.length);
  });
});
