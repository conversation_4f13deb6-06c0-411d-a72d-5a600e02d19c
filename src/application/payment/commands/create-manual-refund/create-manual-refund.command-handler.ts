import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { PAYMENT_REPOSITORY_TOKEN, PAYMENT_SPLIT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { Refund } from '@/domain/payment/refund';
import { CreateRefundDomainService } from '@/domain/payment/services';
import { PaymentSplitRepositoryPort } from '@/domain/payment-split/ports';
import { Money } from '@/domain/shared';

import { CreateManualRefundCommand } from './create-manual-refund.command';

@CommandHandler(CreateManualRefundCommand)
export class CreateManualRefundCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(PAYMENT_SPLIT_REPOSITORY_TOKEN)
    private readonly paymentSplitRepository: PaymentSplitRepositoryPort,
    private readonly createRefundService: CreateRefundDomainService,
  ) {}

  async execute(command: CreateManualRefundCommand): Promise<Refund> {
    const { paymentId, refundSplits, amount, currency, gatewayRefundId, status, metadata } =
      command.getProps();
    const payment = await this.paymentRepository.findById(paymentId);

    if (!payment) {
      throw new Error('Invalid payment'); // todo: custom error
    }

    const paymentSplits = await this.paymentSplitRepository.findByPaymentId(paymentId);

    return this.db.$transaction(async () => {
      const refund = this.createRefundService.createManualRefund({
        payment,
        paymentSplits,
        refundSplits: refundSplits.map((x) => ({
          amount: Money.from(x.amount, currency),
          accountId: x.accountId,
        })),
        refundAmount: Money.from(amount, currency),
        gatewayRefundId,
        status,
        metadata,
        marketplaceOrderFee: Money.from(0, currency),
        platformOrderFee: Money.from(0, currency),
      });

      await this.paymentRepository.update(payment);
      await Promise.all(
        paymentSplits.map(async (paymentSplit) => this.paymentSplitRepository.update(paymentSplit)),
      );

      return refund;
    });
  }
}
