import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import { PAYMENT_REPOSITORY_TOKEN, PAYMENT_SPLIT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Payment } from '@/domain/payment/payment';
import { Refund, RefundStatus } from '@/domain/payment/refund';
import { CreateRefundDomainService } from '@/domain/payment/services';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { AllowedCurrencies, Money } from '@/domain/shared';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import {
  PaymentSplitRepositoryMock,
} from '@/test/mocks/repositories/payment-split-repository.mock';
import {
  CreateRefundDomainServiceMock,
} from '@/test/mocks/services/create-refund-domain-service.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

import { CreateManualRefundCommand, CreateManualRefundCommandHandler } from '.';

describe('CreateManualRefundCommandHandler', () => {
  let handler: CreateManualRefundCommandHandler;
  let db: DatabaseServiceMock;
  let paymentRepository: PaymentRepositoryMock;
  let paymentSplitRepository: PaymentSplitRepositoryMock;
  let createRefundService: CreateRefundDomainServiceMock;
  let commandProps: CreateManualRefundCommand['props'];
  let payment: Payment;
  let findPaymentSplitsSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateManualRefundCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: PAYMENT_SPLIT_REPOSITORY_TOKEN, useClass: PaymentSplitRepositoryMock },
        { provide: CreateRefundDomainService, useClass: CreateRefundDomainServiceMock },
      ],
    }).compile();

    handler = module.get<CreateManualRefundCommandHandler>(CreateManualRefundCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    paymentSplitRepository = module.get<PaymentSplitRepositoryMock>(PAYMENT_SPLIT_REPOSITORY_TOKEN);
    createRefundService = module.get<CreateRefundDomainServiceMock>(CreateRefundDomainService);
    [payment] = paymentRepository.db;
    commandProps = {
      paymentId: payment.getId(),
      gatewayRefundId: `gateway-refund-id`,
      amount: 10,
      currency: AllowedCurrencies.USD,
      refundSplits: [
        { accountId: 'account-id-0', amount: 5 },
        { accountId: 'account-id-1', amount: 5 },
      ],
      metadata: { key: 'value' },
      status: RefundStatus.ISSUED,
    };
    createRefundService.createManualRefund = jest.fn();
    paymentRepository.update = jest.fn();
    paymentSplitRepository.update = jest.fn();
    findPaymentSplitsSpy = jest.spyOn(paymentSplitRepository, 'findByPaymentId');
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payment is not found', async () => {
    const command = new CreateManualRefundCommand({
      ...commandProps,
      paymentId: 'invalid-payment-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Invalid payment'));
  });

  it('should trigger repository update foreach payment split', async () => {
    const command = new CreateManualRefundCommand(commandProps);
    db.catchTransactions = false;

    await handler.execute(command);

    const paymentSplits = await findPaymentSplitsSpy.mock.results[0].value as PaymentSplit[];
    // Ensure we have at least one payment split to test the update
    expect(paymentSplits.length).toBeGreaterThanOrEqual(1);
    paymentSplits.forEach((paymentSplit) => {
      expect(paymentSplitRepository.update).toHaveBeenCalledWith(paymentSplit);
    });
  });

  it('should trigger payment repository update', async () => {
    const command = new CreateManualRefundCommand(commandProps);
    db.catchTransactions = false;

    await handler.execute(command);

    expect(paymentRepository.update).toHaveBeenCalledWith(payment);
  });

  it('should create and return manual refund', async () => {
    const command = new CreateManualRefundCommand(commandProps);
    db.catchTransactions = false;
    const mockRefund = { } as Refund;
    createRefundService.createManualRefund = jest.fn().mockReturnValue(mockRefund);

    const result = await handler.execute(command);

    const paymentSplits = await findPaymentSplitsSpy.mock.results[0].value as PaymentSplit[];
    // Ensure we have at least one refund split to test the mapping
    expect(commandProps.refundSplits.length).toBeGreaterThan(0);
    expect(createRefundService.createManualRefund).toHaveBeenCalledWith({
      payment,
      paymentSplits,
      refundSplits: commandProps.refundSplits.map((el) => ({
        amount: Money.from(el.amount, commandProps.currency),
        accountId: el.accountId,
      })),
      refundAmount: Money.from(commandProps.amount, commandProps.currency),
      gatewayRefundId: commandProps.gatewayRefundId,
      status: commandProps.status,
      metadata: commandProps.metadata,
      marketplaceOrderFee: Money.from(0, commandProps.currency),
      platformOrderFee: Money.from(0, commandProps.currency),
    });
    expect(result).toBe(mockRefund);
  });

  it('should operate within a transaction', async () => {
    const command = new CreateManualRefundCommand(commandProps);

    await handler.execute(command);

    expect(createRefundService.createManualRefund).not.toHaveBeenCalled();
    expect(paymentRepository.update).not.toHaveBeenCalled();
    expect(paymentSplitRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    const paymentSplits = await findPaymentSplitsSpy.mock.results[0].value as PaymentSplit[];
    expect(createRefundService.createManualRefund).toHaveBeenCalled();
    expect(paymentRepository.update).toHaveBeenCalled();
    expect(paymentSplitRepository.update).toHaveBeenCalledTimes(paymentSplits.length);
  });
});
