import { Command } from '@/core/ddd';
import { RefundStatus } from '@/domain/payment/refund';
import { AllowedCurrencies, GatewayMetadata } from '@/domain/shared';

type RefundSplitData = {
  accountId: string;
  amount: number;
};

type Props = {
  paymentId: string;
  refundSplits: RefundSplitData[];
  amount: number;
  currency: AllowedCurrencies;
  gatewayRefundId: string;
  status: RefundStatus;
  metadata?: GatewayMetadata;
};

export class CreateManualRefundCommand extends Command<Props> {}
