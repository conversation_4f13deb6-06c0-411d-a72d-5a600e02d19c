import { CommandBus } from '@nestjs/cqrs';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';

import { PaymentIntentSettledDomainEvent } from '@/domain/payment-intent/events';

import { CreateSettledPaymentCommand } from '../commands/create-settled-payment';

@EventsHandler(PaymentIntentSettledDomainEvent)
export class CreatePaymentWhenPaymentIntentSettledEventHandler implements IEventHandler {
  constructor(private readonly commandBus: CommandBus) {}

  async handle(event: PaymentIntentSettledDomainEvent) {
    const {
      aggregateId,
      amount,
      customerId,
      providerFee,
      gateway,
      paymentMethodType,
      paymentSplits,
      marketplaceId,
      paymentMethodId,
    } = event.getProps();

    const { amount: paymentAmount, currency } = amount.toObject();

    const { amount: providerFeeAmount } = providerFee.toObject();

    await this.commandBus.execute(
      new CreateSettledPaymentCommand({
        paymentIntentId: aggregateId,
        amount: paymentAmount,
        providerFee: providerFeeAmount,
        currency,
        customerId,
        gatewayType: gateway.getType(),
        paymentMethodType,
        marketplaceId,
        paymentSplits: paymentSplits.map((split) => ({
          accountId: split.getAccountId(),
          amount: split.getAmount().toNumber(),
        })),
        paymentMethodId,
      }),
    );
  }
}
