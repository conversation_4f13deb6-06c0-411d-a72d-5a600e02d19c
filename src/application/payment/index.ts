import { Create<PERSON><PERSON>al<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON> } from './commands/create-manual-refund';
import { CreateRefundCommandHandler } from './commands/create-refund';
import { CreateSettledPaymentCommandHandler } from './commands/create-settled-payment';
import { MarkRefundCanceledCommandHandler } from './commands/mark-refund-canceled';
import { MarkRefundFailedCommandHandler } from './commands/mark-refund-failed';
import { MarkRefundIssuedCommandHandler } from './commands/mark-refund-issued';
import { ProcessRefundFailureCommandHandler } from './commands/process-refund-failure';
import { CreatePaymentWhenPaymentIntentSettledEventHandler } from './event-handlers';

export const PAYMENT_COMMAND_HANDLERS = [
  CreateSettledPaymentCommandHandler,
  CreateRefundCommandHandler,
  CreateManualRefund<PERSON>ommandHand<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ued<PERSON><PERSON><PERSON>Hand<PERSON>,
  MarkRefundCance<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Hand<PERSON>,
  ProcessRefundFailureCommandHandler,
];

export const PAYMENT_EVENT_HANDLERS = [CreatePaymentWhenPaymentIntentSettledEventHandler];

export const PAYMENT_APPLICATION_SERVICES = [];
