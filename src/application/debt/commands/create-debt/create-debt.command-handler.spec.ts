import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import { Debt, DebtSource, DebtType } from '@/domain/debt/debt';
import { DEBT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { AllowedCurrencies, Money } from '@/domain/shared';
import { DebtRepositoryMock } from '@/test/mocks/repositories/debt-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

import { CreateDebtCommand, CreateDebtCommandHandler } from '.';

describe('CreateDebtCommandHandler', () => {
  let handler: CreateDebtCommandHandler;
  let db: DatabaseServiceMock;
  let debtRepository: DebtRepositoryMock;
  let commandProps: CreateDebtCommand['props'];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateDebtCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: DEBT_REPOSITORY_TOKEN, useClass: DebtRepositoryMock },
      ],
    }).compile();

    handler = module.get<CreateDebtCommandHandler>(CreateDebtCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    debtRepository = module.get<DebtRepositoryMock>(DEBT_REPOSITORY_TOKEN);
    commandProps = {
      debtAmount: 1,
      debtCurrency: AllowedCurrencies.USD,
      accountId: 'account-id-0',
      sourceId: 'source-id-0',
      type: DebtType.PLATFORM_TO_ACCOUNT,
      source: DebtSource.PAYMENT_SPLIT
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should create a debt within transaction', async () => {
    const command = new CreateDebtCommand(commandProps);
    const [debt] = debtRepository.db;
    const debtCreateSpy = jest.spyOn(Debt, 'create').mockReturnValue(debt);
    debtRepository.create = jest.fn();

    await handler.execute(command);

    expect(debtCreateSpy).not.toHaveBeenCalled();
    expect(debtRepository.create).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    const result = await db.invokeNextTransaction();

    expect(debtCreateSpy).toHaveBeenCalledWith({
      amount: Money.from(commandProps.debtAmount, commandProps.debtCurrency),
      accountId: commandProps.accountId,
      type: commandProps.type,
      source: commandProps.source,
      sourceId: commandProps.sourceId,
    });
    expect(debtRepository.create).toHaveBeenCalledWith(debt);
    expect(result).toBe(debt);
  });
});
