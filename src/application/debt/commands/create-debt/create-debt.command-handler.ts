import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { Debt } from '@/domain/debt/debt';
import { DebtRepositoryPort } from '@/domain/debt/ports';
import { DEBT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Money } from '@/domain/shared';

import { CreateDebtCommand } from './create-debt.command';

@CommandHandler(CreateDebtCommand)
export class CreateDebtCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(DEBT_REPOSITORY_TOKEN)
    private readonly debtRepository: DebtRepositoryPort,
  ) {}

  async execute(command: CreateDebtCommand): Promise<Debt> {
    const { debtAmount, debtCurrency, accountId, type, source, sourceId } = command.getProps();

    return this.db.$transaction(async () => {
      const debt = Debt.create({
        amount: Money.from(debtAmount, debtCurrency),
        accountId,
        type,
        source,
        sourceId,
      });

      await this.debtRepository.create(debt);

      return debt;
    });
  }
}
