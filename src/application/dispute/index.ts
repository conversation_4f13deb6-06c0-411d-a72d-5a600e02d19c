import { CloseDisputeCommandHandler } from './commands/close-dispute';
import { CreateDisputeCommandHandler } from './commands/create-dispute';
import {
  ProcessDisputeFundsRestoredCommandHandler,
} from './commands/dispute-funds-restored';
import { ProcessDisputeFundsWithdrawalCommandHandler } from './commands/dispute-funds-withdrawn';
import { UpdateDisputeCommandHandler } from './commands/update-dispute';

export const DISPUTE_COMMAND_HANDLERS = [
  CreateDisputeCommandHandler,
  UpdateDisputeCommandHandler,
  ProcessDisputeFundsWithdrawalCommandHandler,
  ProcessDisputeFundsRestoredCommandHandler,
  CloseDisputeCommandHandler
];