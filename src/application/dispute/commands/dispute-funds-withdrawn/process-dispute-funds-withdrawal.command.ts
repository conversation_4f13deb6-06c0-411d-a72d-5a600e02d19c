import { CreateDisputeCommandProps } from '@/application/dispute/commands/create-dispute';
import { TransactionData } from '@/application/dispute/commands/dispute-funds-restored';
import { Command } from '@/core/ddd';

export type ProcessDisputeFundsWithdrawalCommandProps = CreateDisputeCommandProps & {
  transactionsData: TransactionData[];
};

export class ProcessDisputeFundsWithdrawalCommand
  extends Command<ProcessDisputeFundsWithdrawalCommandProps> {}