import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import {
  DISPUTE_REPOSITORY_TOKEN,
  TRANSACTION_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_REPOSITORY_TOKEN
} from '@/domain/di-tokens';
import { Dispute } from '@/domain/dispute/dispute';
import { DisputeDomainService } from '@/domain/dispute/services';
import { PaymentIntent } from '@/domain/payment-intent/payment-intent';
import { AllowedCurrencies, Money } from '@/domain/shared';
import { TransactionDomainService } from '@/domain/transaction/services';
import { Transaction } from '@/domain/transaction/transaction';
import { DisputeRepositoryMock } from '@/test/mocks/repositories/dispute-repository.mock';
import {
  MarketplaceGatewayRepositoryMock
} from '@/test/mocks/repositories/marketplace-gateway-repository.mock';
import { PaymentIntentRepositoryMock } from '@/test/mocks/repositories/payment-intent-repository.mock';
import { TransactionRepositoryMock } from '@/test/mocks/repositories/transaction-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import { DisputeDomainServiceMock } from '@/test/mocks/services/dispute-domain-service.mock';

import { ProcessDisputeFundsWithdrawalCommand, ProcessDisputeFundsWithdrawalCommandHandler } from '.';

describe('ProcessDisputeFundsWithdrawalCommandHandler', () => {
  let handler: ProcessDisputeFundsWithdrawalCommandHandler;
  let db: DatabaseServiceMock;
  let transactionRepository: TransactionRepositoryMock;
  let disputeRepository: DisputeRepositoryMock;
  let paymentIntentRepository: PaymentIntentRepositoryMock;
  let marketplaceGatewayRepository: MarketplaceGatewayRepositoryMock;
  let disputeDomainService: DisputeDomainServiceMock;
  let transactionDomainService: TransactionDomainService;
  let commandProps: ProcessDisputeFundsWithdrawalCommand['props'];
  let dispute: Dispute;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessDisputeFundsWithdrawalCommandHandler,
        TransactionDomainService,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: TRANSACTION_REPOSITORY_TOKEN, useClass: TransactionRepositoryMock },
        { provide: DISPUTE_REPOSITORY_TOKEN, useClass: DisputeRepositoryMock },
        { provide: PAYMENT_INTENT_REPOSITORY_TOKEN, useClass: PaymentIntentRepositoryMock },
        { provide: MARKETPLACE_GATEWAY_REPOSITORY_TOKEN, useClass: MarketplaceGatewayRepositoryMock },
        { provide: DisputeDomainService, useClass: DisputeDomainServiceMock },
      ],
    }).compile();

    handler = module.get<ProcessDisputeFundsWithdrawalCommandHandler>(ProcessDisputeFundsWithdrawalCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    transactionRepository = module.get<TransactionRepositoryMock>(TRANSACTION_REPOSITORY_TOKEN);
    disputeRepository = module.get<DisputeRepositoryMock>(DISPUTE_REPOSITORY_TOKEN);
    paymentIntentRepository = module.get<PaymentIntentRepositoryMock>(PAYMENT_INTENT_REPOSITORY_TOKEN);
    marketplaceGatewayRepository = module.get<MarketplaceGatewayRepositoryMock>(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN);
    transactionDomainService = module.get<TransactionDomainService>(TransactionDomainService);
    disputeDomainService = module.get<DisputeDomainServiceMock>(DisputeDomainService);
    [dispute] = disputeRepository.db;
    const { gateway, gatewayDispute, paymentIntentId } = dispute.getProps();
    commandProps = {
      amount: 1,
      currency: AllowedCurrencies.USD,
      evidence: undefined,
      evidenceDetails: undefined,
      gatewayType: gateway.getType(),
      gatewayDisputeId: gatewayDispute.getId(),
      gatewayPaymentIntentId: paymentIntentId,
      reason: '',
      status: 'won',
      transactionsData: [
        { amount: 5, fee: 1 },
        { amount: 10, fee: 2 },
      ],
    };

    transactionRepository.create = jest.fn();
    dispute.fundsWithdrawn = jest.fn();
    disputeDomainService.update = jest.fn().mockReturnValue(dispute);
    disputeRepository.update = jest.fn();
    // Disabling transaction catch by default
    db.catchTransactions = true;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if dispute does not exist', async () => {
    const command = new ProcessDisputeFundsWithdrawalCommand({
      ...commandProps,
      gatewayDisputeId: 'missing-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Dispute does not exist'));
  });

  it('should fail if transactionsData are not provided', async () => {
    const command = new ProcessDisputeFundsWithdrawalCommand({
      ...commandProps,
      transactionsData: [],
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('TransactionsData are required'));
  });

  it('should seek payment intent by id from the dispute', async () => {
    const command = new ProcessDisputeFundsWithdrawalCommand(commandProps);
    const findByGatewayDisputeIdSpy = jest.spyOn(disputeRepository, 'findByGatewayDisputeId');
    const findPaymentIntentByIdSpy = jest.spyOn(paymentIntentRepository, 'findById');

    await handler.execute(command);

    expect(findByGatewayDisputeIdSpy).toHaveBeenCalledWith(commandProps.gatewayDisputeId, commandProps.gatewayType);
    const foundDispute = await findByGatewayDisputeIdSpy.mock.results[0].value as Dispute;
    expect(foundDispute).toBeInstanceOf(Dispute);
    expect(findPaymentIntentByIdSpy).toHaveBeenCalledWith(foundDispute.getProps().paymentIntentId);
  });

  it('should fail if payment intent does not exist', async () => {
    const command = new ProcessDisputeFundsWithdrawalCommand(commandProps);
    paymentIntentRepository.findById = jest.fn().mockResolvedValue(null);

    await expect(handler.execute(command)).rejects.toThrow(
      new Error(`Payment intent for dispute ${dispute.getId()} does not found`)
    );
  });

  it('should seek marketplace gateway by id from the payment intent', async () => {
    const command = new ProcessDisputeFundsWithdrawalCommand(commandProps);
    const findByGatewayDisputeIdSpy = jest.spyOn(disputeRepository, 'findByGatewayDisputeId');
    const findPaymentIntentByIdSpy = jest.spyOn(paymentIntentRepository, 'findById');
    marketplaceGatewayRepository.findByMarketplaceAndGatewayId = jest.fn();

    await handler.execute(command);

    expect(findByGatewayDisputeIdSpy).toHaveBeenCalled();
    expect(findPaymentIntentByIdSpy).toHaveBeenCalled();
    const foundDispute = await findByGatewayDisputeIdSpy.mock.results[0].value as Dispute;
    const foundPaymentIntent = await findPaymentIntentByIdSpy.mock.results[0].value as PaymentIntent;
    expect(foundDispute).toBeInstanceOf(Dispute);
    expect(foundPaymentIntent).toBeInstanceOf(PaymentIntent);
    expect(marketplaceGatewayRepository.findByMarketplaceAndGatewayId).toHaveBeenCalledWith({
      marketplaceId: foundPaymentIntent.getProps().marketplaceId,
      gatewayId: foundDispute.getProps().gatewayId,
    });
  });

  it('should create dispute fee and dispute transactions for each transactions data', async () => {
    const command = new ProcessDisputeFundsWithdrawalCommand(commandProps);
    const createDisputeFeeTransactionSpy = jest.spyOn(transactionDomainService, 'createDisputeFeeTransaction');
    const createDisputeTransactionSpy = jest.spyOn(transactionDomainService, 'createDisputeTransaction');
    db.catchTransactions = false;

    await handler.execute(command);

    commandProps.transactionsData.forEach((data, i) => {
      expect(createDisputeFeeTransactionSpy).toHaveBeenCalledWith({
        amount: Money.from(data.fee, commandProps.currency),
        disputeId: dispute.getId(),
      });
      expect(createDisputeTransactionSpy).toHaveBeenCalledWith({
        amount: Money.from(data.amount, commandProps.currency),
        disputeId: dispute.getId(),
      });
      expect(transactionRepository.create).toHaveBeenCalledWith(createDisputeFeeTransactionSpy.mock.results[i].value);
      expect(transactionRepository.create).toHaveBeenCalledWith(createDisputeTransactionSpy.mock.results[i].value);
    });
  });

  it('should update dispute funds withdrawn for each transactions data', async () => {
    const command = new ProcessDisputeFundsWithdrawalCommand(commandProps);
    const createDisputeFeeTransactionSpy = jest.spyOn(transactionDomainService, 'createDisputeFeeTransaction');
    db.catchTransactions = false;

    await handler.execute(command);

    const marketplaceGatewayId = marketplaceGatewayRepository.db[0].getId();
    commandProps.transactionsData.forEach((data, i) => {
      const feeTransaction = createDisputeFeeTransactionSpy.mock.results[i].value as Transaction;
      expect(dispute.fundsWithdrawn).toHaveBeenCalledWith({
        amount: Money.from(data.amount, commandProps.currency),
        fee: Money.from(data.fee, commandProps.currency),
        relatedTransactionId: feeTransaction.getId(),
        marketplaceGatewayId,
      });
    });
  });

  it('should proceed dispute funds withdrawal in transaction', async () => {
    const command = new ProcessDisputeFundsWithdrawalCommand(commandProps);
    const createDisputeFeeTransactionSpy = jest.spyOn(transactionDomainService, 'createDisputeFeeTransaction');
    const createDisputeTransactionSpy = jest.spyOn(transactionDomainService, 'createDisputeTransaction');

    await handler.execute(command);

    expect(createDisputeFeeTransactionSpy).not.toHaveBeenCalled();
    expect(createDisputeTransactionSpy).not.toHaveBeenCalled();
    expect(transactionRepository.create).not.toHaveBeenCalled();
    expect(dispute.fundsWithdrawn).not.toHaveBeenCalled();
    expect(disputeDomainService.update).not.toHaveBeenCalled();
    expect(disputeRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    const transactionsDataLength = commandProps.transactionsData.length;
    expect(createDisputeFeeTransactionSpy).toHaveBeenCalledTimes(transactionsDataLength);
    expect(createDisputeTransactionSpy).toHaveBeenCalledTimes(transactionsDataLength);
    expect(transactionRepository.create).toHaveBeenCalledTimes(transactionsDataLength * 2);
    expect(dispute.fundsWithdrawn).toHaveBeenCalledTimes(transactionsDataLength);
    expect(disputeDomainService.update).toHaveBeenCalledWith(dispute, commandProps);
    expect(disputeRepository.update).toHaveBeenCalledWith(dispute);
  });
});
