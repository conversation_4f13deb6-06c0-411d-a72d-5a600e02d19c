import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import {
  DISPUTE_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
  TRANSACTION_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { DisputeRepositoryPort } from '@/domain/dispute/ports';
import { DisputeDomainService } from '@/domain/dispute/services';
import { MarketplaceGatewayRepositoryPort } from '@/domain/marketplace-gateway/ports';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';
import { Money } from '@/domain/shared';
import { TransactionRepositoryPort } from '@/domain/transaction/ports';
import { TransactionDomainService } from '@/domain/transaction/services';

import { ProcessDisputeFundsWithdrawalCommand } from './process-dispute-funds-withdrawal.command';

@CommandHandler(ProcessDisputeFundsWithdrawalCommand)
export class ProcessDisputeFundsWithdrawalCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(TRANSACTION_REPOSITORY_TOKEN)
    private readonly transactionRepository: TransactionRepositoryPort,
    @Inject(DISPUTE_REPOSITORY_TOKEN)
    private readonly disputeRepository: DisputeRepositoryPort,
    @Inject(PAYMENT_INTENT_REPOSITORY_TOKEN)
    private readonly paymentIntentRepository: PaymentIntentRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayRepositoryPort: MarketplaceGatewayRepositoryPort,
    private readonly transactionDomainService: TransactionDomainService,
    private readonly disputeDomainService: DisputeDomainService,
  ) {}

  async execute(command: ProcessDisputeFundsWithdrawalCommand): Promise<void> {
    const props = command.getProps();

    const { gatewayDisputeId, gatewayType } = props;

    const dispute = await this.disputeRepository.findByGatewayDisputeId(
      gatewayDisputeId,
      gatewayType,
    );

    if (!dispute) {
      throw new Error('Dispute does not exist');
    }

    if (!props.transactionsData?.length) {
      throw new Error('TransactionsData are required');
    }

    const { paymentIntentId, gatewayId } = dispute.getProps();

    const paymentIntent = await this.paymentIntentRepository.findById(paymentIntentId);

    if (!paymentIntent) {
      throw new Error(`Payment intent for dispute ${dispute.getId()} does not found`);
    }

    const marketplaceGateway =
      await this.marketplaceGatewayRepositoryPort.findByMarketplaceAndGatewayId({
        marketplaceId: paymentIntent.getProps().marketplaceId,
        gatewayId,
      });

    await this.db.$transaction(async () => {
      for (const balanceTransaction of props.transactionsData) {
        const feeTransaction = this.transactionDomainService.createDisputeFeeTransaction({
          amount: Money.from(balanceTransaction.fee, props.currency),
          disputeId: dispute.getId(),
        });

        const disputeTransaction = this.transactionDomainService.createDisputeTransaction({
          amount: Money.from(balanceTransaction.amount, props.currency),
          disputeId: dispute.getId(),
        });

        await Promise.all([
          this.transactionRepository.create(feeTransaction),
          this.transactionRepository.create(disputeTransaction),
        ]);

        dispute.fundsWithdrawn({
          amount: Money.from(balanceTransaction.amount, props.currency),
          fee: Money.from(balanceTransaction.fee, props.currency),
          marketplaceGatewayId: marketplaceGateway.getId(),
          relatedTransactionId: feeTransaction.getId(),
        });
      }

      const updatedDispute = this.disputeDomainService.update(dispute, props);

      await this.disputeRepository.update(updatedDispute);
    });
  }
}
