import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import { DISPUTE_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Dispute } from '@/domain/dispute/dispute';
import { DisputeDomainService } from '@/domain/dispute/services';
import { AllowedCurrencies } from '@/domain/shared';
import { DisputeRepositoryMock } from '@/test/mocks/repositories/dispute-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import { DisputeDomainServiceMock } from '@/test/mocks/services/dispute-domain-service.mock';

import { UpdateDisputeCommand, UpdateDisputeCommandHandler } from '.';
import * as stripe from 'stripe';

describe('UpdateDisputeCommandHandler', () => {
  let handler: UpdateDisputeCommandHandler;
  let db: DatabaseServiceMock;
  let disputeRepository: DisputeRepositoryMock;
  let disputeDomainService: DisputeDomainServiceMock;
  let commandProps: UpdateDisputeCommand['props'];
  let dispute: Dispute;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateDisputeCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: DISPUTE_REPOSITORY_TOKEN, useClass: DisputeRepositoryMock },
        { provide: DisputeDomainService, useClass: DisputeDomainServiceMock },
      ],
    }).compile();

    handler = module.get<UpdateDisputeCommandHandler>(UpdateDisputeCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    disputeRepository = module.get<DisputeRepositoryMock>(DISPUTE_REPOSITORY_TOKEN);
    disputeDomainService = module.get<DisputeDomainServiceMock>(DisputeDomainService);
    [dispute] = disputeRepository.db;
    const { gateway, gatewayDispute, paymentIntentId } = dispute.getProps();
    commandProps = {
      amount: 1,
      currency: AllowedCurrencies.USD,
      evidence: undefined,
      evidenceDetails: undefined,
      gatewayType: gateway.getType(),
      gatewayDisputeId: gatewayDispute.getId(),
      gatewayPaymentIntentId: paymentIntentId,
      reason: '',
      status: 'won',
    };
    disputeDomainService.update = jest.fn().mockReturnValue(dispute);
    disputeRepository.update = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if no dispute is found by gateway dispute id', async () => {
    const command = new UpdateDisputeCommand({
      ...commandProps,
      gatewayDisputeId: 'invalid-gateway-dispute-id',
    });
    db.catchTransactions = false;

    await expect(handler.execute(command)).rejects.toThrow(new Error('Dispute does not exist'));
  });

  it('should update dispute within a transaction', async () => {
    const command = new UpdateDisputeCommand(commandProps);
    const findDisputeSpy = jest.spyOn(disputeRepository, 'findByGatewayDisputeId');

    await handler.execute(command);

    expect(findDisputeSpy).not.toHaveBeenCalled();
    expect(disputeDomainService.update).not.toHaveBeenCalled();
    expect(disputeRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    expect(findDisputeSpy).toHaveBeenCalledWith(commandProps.gatewayDisputeId, commandProps.gatewayType);
    expect(disputeDomainService.update).toHaveBeenCalledWith(dispute, commandProps);
    expect(disputeRepository.update).toHaveBeenCalledWith(dispute);
    const [serviceUpdateInvOrder] = (disputeDomainService.update as jest.Mock).mock.invocationCallOrder;
    const [repositoryUpdateInvOrder] = (disputeRepository.update as jest.Mock).mock.invocationCallOrder;
    expect(serviceUpdateInvOrder).toBeLessThan(repositoryUpdateInvOrder);
  });
});
