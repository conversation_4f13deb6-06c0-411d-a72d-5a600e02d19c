import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { DISPUTE_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { DisputeRepositoryPort } from '@/domain/dispute/ports';
import { DisputeDomainService } from '@/domain/dispute/services';

import { UpdateDisputeCommand } from './update-dispute.command';

@CommandHandler(UpdateDisputeCommand)
export class UpdateDisputeCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(DISPUTE_REPOSITORY_TOKEN)
    private readonly disputeRepository: DisputeRepositoryPort,
    private readonly disputeDomainService: DisputeDomainService,
  ) {}

  async execute(command: UpdateDisputeCommand): Promise<void> {
    const props = command.getProps();

    await this.db.$transaction(async () => {
      const dispute = await this.disputeRepository.findByGatewayDisputeId(
        props.gatewayDisputeId,
        props.gatewayType
      );

      if (!dispute) {
        throw new Error('Dispute does not exist'); // todo: custom error
      }

      const updatedDispute = this.disputeDomainService.update(dispute, props);

      await this.disputeRepository.update(updatedDispute);
    });
  }
}
