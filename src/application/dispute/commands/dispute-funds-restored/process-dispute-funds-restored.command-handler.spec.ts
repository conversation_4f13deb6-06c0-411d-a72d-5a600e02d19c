import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import { DISPUTE_REPOSITORY_TOKEN, TRANSACTION_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Dispute } from '@/domain/dispute/dispute';
import { DisputeDomainService } from '@/domain/dispute/services';
import { AllowedCurrencies, Money } from '@/domain/shared';
import { TransactionDomainService } from '@/domain/transaction/services';
import { DisputeRepositoryMock } from '@/test/mocks/repositories/dispute-repository.mock';
import { TransactionRepositoryMock } from '@/test/mocks/repositories/transaction-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import { DisputeDomainServiceMock } from '@/test/mocks/services/dispute-domain-service.mock';

import { ProcessDisputeFundsRestoredCommand, ProcessDisputeFundsRestoredCommandHandler } from '.';

describe('ProcessDisputeFundsRestoredCommandHandler', () => {
  let handler: ProcessDisputeFundsRestoredCommandHandler;
  let db: DatabaseServiceMock;
  let transactionRepository: TransactionRepositoryMock;
  let disputeRepository: DisputeRepositoryMock;
  let disputeDomainService: DisputeDomainServiceMock;
  let transactionDomainService: TransactionDomainService;
  let commandProps: ProcessDisputeFundsRestoredCommand['props'];
  let dispute: Dispute;
  let createDisputeTransactionSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessDisputeFundsRestoredCommandHandler,
        TransactionDomainService,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: TRANSACTION_REPOSITORY_TOKEN, useClass: TransactionRepositoryMock },
        { provide: DISPUTE_REPOSITORY_TOKEN, useClass: DisputeRepositoryMock },
        { provide: DisputeDomainService, useClass: DisputeDomainServiceMock },
      ],
    }).compile();

    handler = module.get<ProcessDisputeFundsRestoredCommandHandler>(ProcessDisputeFundsRestoredCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    transactionRepository = module.get<TransactionRepositoryMock>(TRANSACTION_REPOSITORY_TOKEN);
    transactionDomainService = module.get<TransactionDomainService>(TransactionDomainService);
    disputeRepository = module.get<DisputeRepositoryMock>(DISPUTE_REPOSITORY_TOKEN);
    disputeDomainService = module.get<DisputeDomainServiceMock>(DisputeDomainService);
    [dispute] = disputeRepository.db;
    const { gateway, gatewayDispute, paymentIntentId } = dispute.getProps();
    commandProps = {
      amount: 1,
      currency: AllowedCurrencies.USD,
      evidence: undefined,
      evidenceDetails: undefined,
      gatewayType: gateway.getType(),
      gatewayDisputeId: gatewayDispute.getId(),
      gatewayPaymentIntentId: paymentIntentId,
      reason: '',
      status: 'won',
      transactionsData: [
        { amount: 5, fee: 1 },
        { amount: 10, fee: 2 },
      ],
    };
    createDisputeTransactionSpy = jest.spyOn(transactionDomainService, 'createDisputeTransaction');
    transactionRepository.create = jest.fn();
    disputeDomainService.update = jest.fn().mockReturnValue(dispute);
    disputeRepository.update = jest.fn();
    // Disabling transaction catch by default
    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if dispute does not exist', async () => {
    const command = new ProcessDisputeFundsRestoredCommand(commandProps);
    disputeRepository.findByGatewayDisputeId = jest.fn().mockResolvedValue(null);

    await expect(handler.execute(command)).rejects.toThrow(new Error('Dispute does not exist'));
  });

  it('should fail if transactionsData are not provided', async () => {
    const command = new ProcessDisputeFundsRestoredCommand({
      ...commandProps,
      transactionsData: [],
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('transactionsData are required'));
  });

  it('should create dispute transactions for each transactions data', async () => {
    const command = new ProcessDisputeFundsRestoredCommand(commandProps);
    await handler.execute(command);

    commandProps.transactionsData.forEach((data, i) => {
      expect(createDisputeTransactionSpy).toHaveBeenCalledWith({
        amount: Money.from(data.amount, commandProps.currency),
        disputeId: dispute.getId(),
      });
      expect(transactionRepository.create).toHaveBeenCalledWith(createDisputeTransactionSpy.mock.results[i].value);
    });
  });

  it('should proceed dispute funds restore in transaction', async () => {
    const command = new ProcessDisputeFundsRestoredCommand(commandProps);
    const findByGatewayDisputeIdSpy = jest.spyOn(disputeRepository, 'findByGatewayDisputeId');
    db.catchTransactions = true;

    await handler.execute(command);

    expect(findByGatewayDisputeIdSpy).not.toHaveBeenCalled();
    expect(createDisputeTransactionSpy).not.toHaveBeenCalled();
    expect(transactionRepository.create).not.toHaveBeenCalled();
    expect(disputeDomainService.update).not.toHaveBeenCalled();
    expect(disputeRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    const transactionDataLength = commandProps.transactionsData.length;
    expect(findByGatewayDisputeIdSpy).toHaveBeenCalledWith(commandProps.gatewayDisputeId, commandProps.gatewayType);
    expect(createDisputeTransactionSpy).toHaveBeenCalledTimes(transactionDataLength);
    expect(transactionRepository.create).toHaveBeenCalledTimes(transactionDataLength);
    expect(disputeDomainService.update).toHaveBeenCalledWith(dispute, commandProps);
    expect(disputeRepository.update).toHaveBeenCalledWith(dispute);
    const [serviceUpdateInvOrder] = (disputeDomainService.update as jest.Mock).mock.invocationCallOrder;
    const [repositoryUpdateInvOrder] = (disputeRepository.update as jest.Mock).mock.invocationCallOrder;
    expect(serviceUpdateInvOrder).toBeLessThan(repositoryUpdateInvOrder);
  });
});
