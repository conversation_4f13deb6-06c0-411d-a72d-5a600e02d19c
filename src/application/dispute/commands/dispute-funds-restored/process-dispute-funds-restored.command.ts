import { CreateDisputeCommandProps } from '@/application/dispute/commands/create-dispute';
import { Command } from '@/core/ddd';

export type TransactionData = {
  amount: number;
  fee: number;
};

export type ProcessDisputeFundsRestoredCommandProps = CreateDisputeCommandProps & {
  transactionsData: TransactionData[];
};

export class ProcessDisputeFundsRestoredCommand
  extends Command<ProcessDisputeFundsRestoredCommandProps> {}