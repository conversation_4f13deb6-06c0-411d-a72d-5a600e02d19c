import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { DISPUTE_REPOSITORY_TOKEN, TRANSACTION_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { DisputeRepositoryPort } from '@/domain/dispute/ports';
import { DisputeDomainService } from '@/domain/dispute/services';
import { Money } from '@/domain/shared';
import { TransactionRepositoryPort } from '@/domain/transaction/ports';
import { TransactionDomainService } from '@/domain/transaction/services';

import { ProcessDisputeFundsRestoredCommand } from './process-dispute-funds-restored.command';

@CommandHandler(ProcessDisputeFundsRestoredCommand)
export class ProcessDisputeFundsRestoredCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(TRANSACTION_REPOSITORY_TOKEN)
    private readonly transactionRepository: TransactionRepositoryPort,
    private readonly transactionDomainService: TransactionDomainService,
    @Inject(DISPUTE_REPOSITORY_TOKEN)
    private readonly disputeRepository: DisputeRepositoryPort,
    private readonly disputeDomainService: DisputeDomainService,
  ) {}

  async execute(command: ProcessDisputeFundsRestoredCommand): Promise<void> {
    const props = command.getProps();

    const { gatewayDisputeId, gatewayType } = props;

    await this.db.$transaction(async () => {
      const dispute = await this.disputeRepository.findByGatewayDisputeId(
        gatewayDisputeId,
        gatewayType,
      );

      if (!dispute) {
        throw new Error('Dispute does not exist');
      }

      if (!props.transactionsData?.length) {
        throw new Error('transactionsData are required');
      }

      for (const transactionData of props.transactionsData) {
        const disputeTransaction = this.transactionDomainService.createDisputeTransaction({
          amount: Money.from(transactionData.amount, props.currency),
          disputeId: dispute.getId(),
        });

        await this.transactionRepository.create(disputeTransaction);
      }

      const updatedDispute = this.disputeDomainService.update(dispute, props);

      await this.disputeRepository.update(updatedDispute);
    });
  }
}
