import Stripe from 'stripe';

import { Command } from '@/core/ddd';
import { AllowedCurrencies, GatewayType } from '@/domain/shared';

export type DisputeStatus = Stripe.Dispute.Status;
export type DisputeEvidence = Stripe.Dispute.Evidence;
export type DisputeEvidenceDetails = Stripe.Dispute.EvidenceDetails;

export type CreateDisputeCommandProps = {
  gatewayType: GatewayType;
  gatewayDisputeId: string;
  gatewayPaymentIntentId: string;
  amount: number;
  currency: AllowedCurrencies;
  status: DisputeStatus;
  reason: string;
  evidence: DisputeEvidence;
  evidenceDetails: DisputeEvidenceDetails;
};
export class CreateDisputeCommand extends Command<CreateDisputeCommandProps> {}
