import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import {
  DISPUTE_REPOSITORY_TOKEN,
  GATEWAY_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { DisputeStatus } from '@/domain/dispute/dispute';
import { DisputeDomainService } from '@/domain/dispute/services';
import { Payment } from '@/domain/payment/payment';
import { PaymentIntent } from '@/domain/payment-intent/payment-intent';
import { AllowedCurrencies, Gateway } from '@/domain/shared';
import { DisputeRepositoryMock } from '@/test/mocks/repositories/dispute-repository.mock';
import { GatewayRepositoryMock } from '@/test/mocks/repositories/gateway-repository.mock';
import { PaymentIntentRepositoryMock } from '@/test/mocks/repositories/payment-intent-repository.mock';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import { DisputeDomainServiceMock } from '@/test/mocks/services/dispute-domain-service.mock';

import { CreateDisputeCommand, CreateDisputeCommandHandler } from '.';

describe('CreateDisputeCommandHandler', () => {
  let handler: CreateDisputeCommandHandler;
  let db: DatabaseServiceMock;
  let disputeRepository: DisputeRepositoryMock;
  let gatewayRepository: GatewayRepositoryMock;
  let paymentIntentRepository: PaymentIntentRepositoryMock;
  let paymentRepository: PaymentRepositoryMock;

  let disputeDomainService: DisputeDomainServiceMock;
  let paymentIntent: PaymentIntent;
  let payment: Payment;
  let commandProps: CreateDisputeCommand['props'];
  let disputeExistsSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateDisputeCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: DISPUTE_REPOSITORY_TOKEN, useClass: DisputeRepositoryMock },
        { provide: PAYMENT_INTENT_REPOSITORY_TOKEN, useClass: PaymentIntentRepositoryMock },
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: GATEWAY_REPOSITORY_TOKEN, useClass: GatewayRepositoryMock },
        { provide: DisputeDomainService, useClass: DisputeDomainServiceMock },
      ],
    }).compile();

    handler = module.get<CreateDisputeCommandHandler>(CreateDisputeCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    disputeRepository = module.get<DisputeRepositoryMock>(DISPUTE_REPOSITORY_TOKEN);
    gatewayRepository = module.get<GatewayRepositoryMock>(GATEWAY_REPOSITORY_TOKEN);
    paymentIntentRepository = module.get<PaymentIntentRepositoryMock>(
      PAYMENT_INTENT_REPOSITORY_TOKEN,
    );
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    disputeDomainService = module.get<DisputeDomainServiceMock>(DisputeDomainService);
    [paymentIntent] = paymentIntentRepository.db;
    [payment] = paymentRepository.db;
    commandProps = {
      gatewayDisputeId: 'gateway-dispute-id',
      gatewayPaymentIntentId: paymentIntent.getId(),
      gatewayType: paymentIntent.getProps().gateway.getType(),
      amount: 100,
      evidence: undefined,
      evidenceDetails: undefined,
      currency: AllowedCurrencies.USD,
      status: DisputeStatus.WON,
      reason: 'Test reason',
    };

    disputeExistsSpy = jest
      .spyOn(disputeRepository, 'isGatewayDisputeExists')
      .mockResolvedValue(false);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should do nothing if dispute already exists', async () => {
    const command = new CreateDisputeCommand(commandProps);
    disputeExistsSpy.mockResolvedValue(true);
    gatewayRepository.findByGatewayType = jest.fn();

    await expect(handler.execute(command)).resolves.toBeUndefined();
    expect(gatewayRepository.findByGatewayType).not.toHaveBeenCalled();
  });

  it('should fail if gateway is not found', async () => {
    const command = new CreateDisputeCommand(commandProps);
    gatewayRepository.findByGatewayType = jest.fn().mockResolvedValue(null);

    await expect(handler.execute(command)).rejects.toThrow(new Error('invalid gateway'));
  });

  it('should create a dispute within transaction', async () => {
    const command = new CreateDisputeCommand(commandProps);
    paymentIntentRepository.findPaymentIntentByGatewayPaymentIntentId = jest
      .fn()
      .mockResolvedValue(paymentIntent);
    paymentRepository.findByPaymentIntentId = jest.fn().mockResolvedValue(payment);
    const [dispute] = disputeRepository.db;
    disputeDomainService.create = jest.fn().mockReturnValue(dispute);
    disputeRepository.create = jest.fn();

    await handler.execute(command);

    expect(paymentIntentRepository.findPaymentIntentByGatewayPaymentIntentId).toHaveBeenCalled();
    expect(disputeDomainService.create).not.toHaveBeenCalled();
    expect(disputeRepository.create).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    const gateway = new Gateway(
      gatewayRepository.db[0].getId(),
      gatewayRepository.db[0].getProps().type,
    );
    const { gatewayType, ...createParams } = commandProps;
    expect(paymentIntentRepository.findPaymentIntentByGatewayPaymentIntentId).toHaveBeenCalledWith(
      commandProps.gatewayPaymentIntentId,
      gateway,
    );
    expect(paymentRepository.findByPaymentIntentId).toHaveBeenCalledWith(paymentIntent.getId());

    expect(disputeDomainService.create).toHaveBeenCalledWith({
      ...createParams,
      gateway,
      paymentIntentId: paymentIntent.getId(),
      paymentId: payment.getId(),
    });
    expect(disputeRepository.create).toHaveBeenCalledWith(dispute);
  });
});
