import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import {
  DISPUTE_REPOSITORY_TOKEN,
  GATEWAY_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { DisputeRepositoryPort } from '@/domain/dispute/ports';
import { DisputeDomainService } from '@/domain/dispute/services';
import { GatewayRepositoryPort } from '@/domain/gateway/ports';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { PaymentIntentRepositoryPort } from '@/domain/payment-intent/ports';
import { Gateway } from '@/domain/shared/value-objects';

import { CreateDisputeCommand } from './create-dispute.command';

@CommandHandler(CreateDisputeCommand)
export class CreateDisputeCommandHand<PERSON> implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(DISPUTE_REPOSITORY_TOKEN)
    private readonly disputeRepository: DisputeRepositoryPort,
    @Inject(PAYMENT_INTENT_REPOSITORY_TOKEN)
    private readonly paymentIntentRepository: PaymentIntentRepositoryPort,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(GATEWAY_REPOSITORY_TOKEN)
    private readonly gatewayRepository: GatewayRepositoryPort,
    private readonly disputeDomainService: DisputeDomainService,
  ) {}

  async execute(command: CreateDisputeCommand): Promise<void> {
    const {
      gatewayDisputeId,
      gatewayPaymentIntentId,
      gatewayType,
      amount,
      currency,
      status,
      reason,
      evidence,
      evidenceDetails,
    } = command.getProps();

    const disputeExists = await this.disputeRepository.isGatewayDisputeExists(
      gatewayDisputeId,
      gatewayType,
    );
    if (disputeExists) {
      return;
    }

    const gateway = await this.gatewayRepository.findByGatewayType(gatewayType);
    if (!gateway) {
      throw new Error('invalid gateway');
    }

    const paymentIntent = await this.getPaymentIntent(
      gatewayPaymentIntentId,
      new Gateway(gateway.getId(), gateway.getProps().type),
    );

    const payment = await this.getPaymentByIntentId(paymentIntent.getId());

    await this.db.$transaction(async () => {
      const dispute = this.disputeDomainService.create({
        amount,
        currency,
        status,
        reason,
        evidence,
        evidenceDetails,
        gateway: new Gateway(gateway.getId(), gateway.getProps().type),
        gatewayDisputeId,
        gatewayPaymentIntentId,
        paymentIntentId: paymentIntent.getId(),
        paymentId: payment.getId(),
      });

      await this.disputeRepository.create(dispute);
    });
  }

  private async getPaymentIntent(paymentIntentIdAtProvider: string, gateway: Gateway) {
    const paymentIntent =
      await this.paymentIntentRepository.findPaymentIntentByGatewayPaymentIntentId(
        paymentIntentIdAtProvider,
        gateway,
      );

    if (!paymentIntent) {
      throw new Error('Payment intent not found');
    }

    return paymentIntent;
  }

  private async getPaymentByIntentId(paymentIntentId: string) {
    const payment = await this.paymentRepository.findByPaymentIntentId(paymentIntentId);

    if (!payment) {
      throw new Error('Payment not found');
    }

    return payment;
  }
}
