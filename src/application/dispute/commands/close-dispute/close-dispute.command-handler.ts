import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { DISPUTE_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { DisputeStatus } from '@/domain/dispute/dispute';
import { DisputeRepositoryPort } from '@/domain/dispute/ports';
import { DisputeDomainService } from '@/domain/dispute/services';

import { CloseDisputeCommand } from './close-dispute.command';

@CommandHandler(CloseDisputeCommand)
export class CloseDisputeCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(DISPUTE_REPOSITORY_TOKEN)
    private readonly disputeRepository: DisputeRepositoryPort,
    private readonly disputeDomainService: DisputeDomainService,
  ) {}

  async execute(command: CloseDisputeCommand): Promise<void> {
    const props = command.getProps();

    await this.db.$transaction(async () => {
      const dispute = await this.disputeRepository.findByGatewayDisputeId(
        props.gatewayDisputeId,
        props.gatewayType
      );

      if (!dispute) {
        throw new Error('Dispute does not exist'); // todo: custom error
      }

      if (props.status === DisputeStatus.LOST) {
        dispute.markAsLost();
      } else if (props.status === DisputeStatus.WON) {
        dispute.markAsWon();
      } else {
        throw new Error('Invalid dispute status');
      }

      const updatedDispute = this.disputeDomainService.update(dispute, props);

      await this.disputeRepository.update(updatedDispute);
    });
  }
}
