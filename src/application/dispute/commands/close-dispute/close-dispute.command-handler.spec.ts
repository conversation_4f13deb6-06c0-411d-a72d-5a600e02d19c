import { Test, TestingModule } from '@nestjs/testing';

import { DatabaseService } from '@/database';
import { DISPUTE_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Dispute, DisputeStatus } from '@/domain/dispute/dispute';
import { DisputeDomainService } from '@/domain/dispute/services';
import { DisputeRepositoryMock } from '@/test/mocks/repositories/dispute-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import { DisputeDomainServiceMock } from '@/test/mocks/services/dispute-domain-service.mock';

import { CloseDisputeCommand, CloseDisputeCommandHandler } from '.';

describe('CloseDisputeCommandHandler', () => {
  let handler: CloseDisputeCommandHandler;
  let db: DatabaseServiceMock;
  let disputeRepository: DisputeRepositoryMock;
  let disputeDomainService: DisputeDomainService;
  let dispute: Dispute;
  let commandProps: CloseDisputeCommand['props'];
  let findDisputeSpy: jest.SpyInstance;
  let disputDomainUpdateSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CloseDisputeCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: DISPUTE_REPOSITORY_TOKEN, useClass: DisputeRepositoryMock },
        { provide: DisputeDomainService, useClass: DisputeDomainServiceMock },
      ],
    }).compile();

    handler = module.get<CloseDisputeCommandHandler>(CloseDisputeCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    disputeRepository = module.get<DisputeRepositoryMock>(DISPUTE_REPOSITORY_TOKEN);
    disputeDomainService = module.get<DisputeDomainService>(DisputeDomainService);
    [dispute] = disputeRepository.db;
    commandProps = {
      gatewayType: dispute.getProps().gateway.getType(),
      gatewayDisputeId: dispute.getProps().gatewayDispute.getId(),
      currency: dispute.getProps().currency,
      amount: 1,
      evidence: undefined,
      evidenceDetails: undefined,
      gatewayPaymentIntentId: '',
      reason: '',
      status: DisputeStatus.WON,
    };

    findDisputeSpy = jest.spyOn(disputeRepository, 'findByGatewayDisputeId');
    disputeRepository.update = jest.fn();
    dispute.markAsWon = jest.fn();
    dispute.markAsLost = jest.fn();
    disputDomainUpdateSpy = jest.spyOn(disputeDomainService, 'update');
    // Disabling transaction catch by default
    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if dispute is not found', async () => {
    const gatewayDisputeId = 'missing-id';
    const command = new CloseDisputeCommand({ ...commandProps, gatewayDisputeId });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Dispute does not exist'));
    expect(findDisputeSpy).toHaveBeenCalledWith(gatewayDisputeId, commandProps.gatewayType);
  });

  it('should mark dispute as won', async () => {
    const command = new CloseDisputeCommand({ ...commandProps, status: DisputeStatus.WON });

    await handler.execute(command);

    expect(dispute.markAsWon).toHaveBeenCalled();
    expect(dispute.markAsLost).not.toHaveBeenCalled();
  });

  it('should mark dispute as lost', async () => {
    const command = new CloseDisputeCommand({ ...commandProps, status: DisputeStatus.LOST });

    await handler.execute(command);

    expect(dispute.markAsLost).toHaveBeenCalled();
    expect(dispute.markAsWon).not.toHaveBeenCalled();
  });

  it('should throw an error for invalid dispute status', async () => {
    const command = new CloseDisputeCommand({ ...commandProps, status: 'needs_response' });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Invalid dispute status'));
  });

  it('should successfully close the dispute in transaction', async () => {
    const command = new CloseDisputeCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(findDisputeSpy).not.toHaveBeenCalled();
    expect(disputDomainUpdateSpy).not.toHaveBeenCalled();
    expect(disputeRepository.update).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    expect(findDisputeSpy).toHaveBeenCalledWith(commandProps.gatewayDisputeId, commandProps.gatewayType);
    expect(disputDomainUpdateSpy).toHaveBeenCalledWith(dispute, command.getProps());
    expect(disputeRepository.update).toHaveBeenCalledWith(dispute);
  });
});
