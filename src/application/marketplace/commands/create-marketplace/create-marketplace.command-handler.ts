import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import { MARKETPLACE_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Marketplace } from '@/domain/marketplace/marketplace';
import { MarketplaceRepositoryPort } from '@/domain/marketplace/ports';
import { CreateMarketplaceService } from '@/domain/marketplace/services';

import { CreateMarketplaceCommand } from './create-marketplace.command';

@CommandHandler(CreateMarketplaceCommand)
export class CreateMarketplaceCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    private readonly createMarketplace: CreateMarketplaceService,
    @Inject(MARKETPLACE_REPOSITORY_TOKEN)
    private readonly marketplaceRepository: MarketplaceRepositoryPort,
  ) {}

  async execute(command: CreateMarketplaceCommand): Promise<Marketplace> {
    const { webhookUrl, name } = command.getProps();

    return this.db.$transaction(async () => {
      const marketplace = this.createMarketplace.createMarketplace({
        name,
        webhookUrl,
      });

      await this.marketplaceRepository.create(marketplace);

      return marketplace;
    });
  }
}
