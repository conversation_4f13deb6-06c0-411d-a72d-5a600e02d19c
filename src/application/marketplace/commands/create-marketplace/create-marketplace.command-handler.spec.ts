import { Test, TestingModule } from '@nestjs/testing';

import {
  CreateMarketplaceCommand,
  CreateMarketplaceCommandHandler
} from '@/application/marketplace/commands/create-marketplace';
import { DatabaseService } from '@/database';
import { MARKETPLACE_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { CreateMarketplaceService } from '@/domain/marketplace/services';
import { MarketplaceRepositoryMock } from '@/test/mocks/repositories/marketplace-repository.mock';
import { CreateMarketplaceServiceMock } from '@/test/mocks/services/create-marketplace-service.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

describe('CreateMarketplaceCommandHandler', () => {
  let handler: CreateMarketplaceCommandHandler;
  let db: DatabaseServiceMock;
  let marketplaceRepository: MarketplaceRepositoryMock;
  let createMarketplaceService: CreateMarketplaceServiceMock;
  let commandProps: CreateMarketplaceCommand['props'];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateMarketplaceCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: MARKETPLACE_REPOSITORY_TOKEN, useClass: MarketplaceRepositoryMock },
        { provide: CreateMarketplaceService, useClass: CreateMarketplaceServiceMock },
      ],
    }).compile();

    handler = module.get<CreateMarketplaceCommandHandler>(CreateMarketplaceCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    marketplaceRepository = module.get<MarketplaceRepositoryMock>(MARKETPLACE_REPOSITORY_TOKEN);
    createMarketplaceService = module.get<CreateMarketplaceServiceMock>(CreateMarketplaceService);
    commandProps = {
      name: 'marketplace-name',
      webhookUrl: 'http://webhook-url',
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should create a new marketplace in transaction', async () => {
    const command = new CreateMarketplaceCommand(commandProps);
    const [marketplace] = marketplaceRepository.db;
    createMarketplaceService.createMarketplace = jest.fn().mockReturnValue(marketplace);
    marketplaceRepository.create = jest.fn();

    await handler.execute(command);

    expect(createMarketplaceService.createMarketplace).not.toHaveBeenCalled();
    expect(marketplaceRepository.create).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    expect(createMarketplaceService.createMarketplace).toHaveBeenCalledWith({
      name: commandProps.name,
      webhookUrl: commandProps.webhookUrl,
    });
    expect(marketplaceRepository.create).toHaveBeenCalledWith(marketplace);
  });
});
