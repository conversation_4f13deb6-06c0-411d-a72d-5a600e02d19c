import { Inject } from '@nestjs/common';
import { <PERSON><PERSON>us, CommandHandler, ICommandHandler } from '@nestjs/cqrs';

import { PAYMENT_REPOSITORY_TOKEN, PAYOUT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { Payment } from '@/domain/payment/payment';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { Refund } from '@/domain/payment/refund';
import { Payout, PayoutStatus, PayoutType } from '@/domain/payout/payout';
import { PayoutRepositoryPort } from '@/domain/payout/ports';

import { CreatePayoutRefundReversalsCommand } from './create-payout-refund-reversals.command';
import { ReversePayoutCommand } from '../reverse-payout';

@CommandHandler(CreatePayoutRefundReversalsCommand)
export class CreatePayoutRefundReversalsCommandHandler implements ICommandHandler {
  constructor(
    private readonly commandBus: CommandBus,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(PAYOUT_REPOSITORY_TOKEN)
    private readonly payoutRepository: PayoutRepositoryPort,
  ) {}

  async execute(command: CreatePayoutRefundReversalsCommand) {
    const payment = await this.paymentRepository.findById(command.getProps().paymentId);

    if (!payment) {
      throw new Error('Payment not found'); // todo: custom error
    }

    const refund = payment.getRefund(command.getProps().refundId);

    const payouts = await this.payoutRepository.findByPaymentId(payment.getId());

    const paymentFeePayout = payouts.find((x) => x.getProps().type === PayoutType.PAYMENT_FEE);

    await this.payoutRepository.transaction(async () => {
      await this.reversePaymentFeePayout({ payment, paymentFeePayout, refund });

      const orderFeePayout = payouts.find((x) => x.getProps().type === PayoutType.ORDER_FEE);

      await this.reverseOrderFeePayout({ payment, orderFeePayout, refund });
    });
  }

  async reverseOrderFeePayout({
    payment,
    orderFeePayout,
    refund,
  }: {
    orderFeePayout: Payout;
    payment: Payment;
    refund: Refund;
  }) {
    const { marketplaceOrderFeeRefunded } = refund.getProps();

    const payoutReversalAmount = marketplaceOrderFeeRefunded.floor();

    if (payoutReversalAmount.isPositive() && this.shouldReversePayout(orderFeePayout)) {
      await this.commandBus.execute(
        new ReversePayoutCommand({
          amount: payoutReversalAmount.toNumber(),
          currency: marketplaceOrderFeeRefunded.getCurrency().getValue(),
          marketplaceId: payment.getProps().marketplaceId,
          payoutId: orderFeePayout.getId(),
          refundId: refund.getId(),
        }),
      );
    }
  }

  async reversePaymentFeePayout({
    payment,
    paymentFeePayout,
    refund,
  }: {
    paymentFeePayout: Payout;
    payment: Payment;
    refund: Refund;
  }) {
    const paymentFeeProfit = this.calculatePaymentFeeProfit(refund);

    if (paymentFeeProfit.isPositive() && this.shouldReversePayout(paymentFeePayout)) {
      await this.commandBus.execute(
        new ReversePayoutCommand({
          amount: paymentFeeProfit.getProps().amount.toNumber(),
          currency: paymentFeeProfit.getCurrency().getValue(),
          marketplaceId: payment.getProps().marketplaceId,
          payoutId: paymentFeePayout.getId(),
          refundId: refund.getId(),
        }),
      );
    }
  }

  private shouldReversePayout(payout: Payout) {
    const { status } = payout.getProps();

    return status === PayoutStatus.SUCCEEDED;
  }

  private calculatePaymentFeeProfit(refund: Refund) {
    const { marketplacePaymentFeeRefunded, platformPaymentFeeRefunded } = refund.getProps();

    return marketplacePaymentFeeRefunded.sub(platformPaymentFeeRefunded);
  }
}
