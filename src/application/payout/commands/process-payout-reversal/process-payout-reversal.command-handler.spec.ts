import { Test, TestingModule } from '@nestjs/testing';

import {
  GATEWAY_SERVICE_FACTORY_TOKEN,
  MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN,
  PAYOUT_REPOSITORY_TOKEN
} from '@/domain/di-tokens';
import {
  MarketplaceGatewayAccount
} from '@/domain/marketplace-gateway-account/marketplace-gateway-account';
import { Payout } from '@/domain/payout/payout';
import { PayoutReversal } from '@/domain/payout/payout-reversal';
import {
  GatewayTransfer,
  GatewayTransferSource,
  GatewayTransferStatus,
} from '@/domain/shared/interfaces';
import {
  MarketplaceGatewayAccountRepositoryMock
} from '@/test/mocks/repositories/marketplace-gateway-account-repository.mock';
import { PayoutRepositoryMock } from '@/test/mocks/repositories/payout-repository.mock';
import {
  GatewayServiceFactoryMock,
  MockGatewayServiceAdapter,
} from '@/test/mocks/services/gateway-service-factory.mock';

import { ProcessPayoutReversalCommand, ProcessPayoutReversalCommandHandler } from '.';

describe('ProcessPayoutReversalCommandHandler', () => {
  let handler: ProcessPayoutReversalCommandHandler;
  let payoutRepository: PayoutRepositoryMock;
  let marketplaceGatewayAccountRepository: MarketplaceGatewayAccountRepositoryMock;
  let commandProps: ProcessPayoutReversalCommand['props'];
  let payout: Payout;
  let reversal: PayoutReversal;
  let marketplaceGatewayAccount: MarketplaceGatewayAccount;
  let createInboundTransferSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessPayoutReversalCommandHandler,
        { provide: PAYOUT_REPOSITORY_TOKEN, useClass: PayoutRepositoryMock },
        { provide: MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN, useClass: MarketplaceGatewayAccountRepositoryMock },
        { provide: GATEWAY_SERVICE_FACTORY_TOKEN, useClass: GatewayServiceFactoryMock },
      ],
    }).compile();

    handler = module.get<ProcessPayoutReversalCommandHandler>(ProcessPayoutReversalCommandHandler);
    payoutRepository = module.get<PayoutRepositoryMock>(PAYOUT_REPOSITORY_TOKEN);
    marketplaceGatewayAccountRepository = module.get<MarketplaceGatewayAccountRepositoryMock>(MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN);
    [payout] = payoutRepository.db;
    [reversal] = payout.getProps().reversals;
    [marketplaceGatewayAccount] = marketplaceGatewayAccountRepository.db;
    commandProps = {
      payoutId: payout.getId(),
      reversalId: reversal.getId(),
    };
    createInboundTransferSpy = jest
      .spyOn(MockGatewayServiceAdapter.prototype, 'createInboundTransfer')
      .mockResolvedValue({
        status: GatewayTransferStatus.SETTLED,
      } as GatewayTransfer);
    payout.markReversalProcessing = jest.fn();
    payout.markReversalSucceeded = jest.fn();
    payout.markReversalFailed = jest.fn();
    payoutRepository.update = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payout is not found', async () => {
    const command = new ProcessPayoutReversalCommand({
      ...commandProps,
      payoutId: 'invalid-payout-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Payout not found'));
  });

  it('should fail if marketplace gateway account is not found', async () => {
    const command = new ProcessPayoutReversalCommand(commandProps);
    jest.spyOn(payoutRepository, 'findById').mockResolvedValue(new Payout({
      id: payout.getId(),
      props: {
        ...payout.getProps(),
        marketplaceGatewayAccountId: 'invalid-marketplace-gateway-account-id',
      }
    }));

    await expect(handler.execute(command)).rejects.toThrow(new Error('Marketplace gateway account not found'));
  });

  it('should create inbound transfer', async () => {
    const command = new ProcessPayoutReversalCommand(commandProps);

    await handler.execute(command);

    expect(createInboundTransferSpy).toHaveBeenCalledWith({
      source: GatewayTransferSource.PAYOUT_REVERSAL,
      sourceId: reversal.getId(),
      amount: reversal.getProps().amount,
      sendingGatewayAccountId: marketplaceGatewayAccount.getProps().idAtGateway,
      originalSourceId: payout.getId(),
      idempotencyKey: reversal.getId(),
    });
  });

  it('should mark reversal as succeeded if transfer is settled', async () => {
    const command = new ProcessPayoutReversalCommand(commandProps);

    await handler.execute(command);

    expect(payout.markReversalSucceeded).toHaveBeenCalledWith(reversal.getId());
    expect(payout.markReversalProcessing).not.toHaveBeenCalled();
    expect(payout.markReversalFailed).not.toHaveBeenCalled();
  });

  it('should mark reversal as processing if transfer is pending', async () => {
    const command = new ProcessPayoutReversalCommand(commandProps);
    createInboundTransferSpy.mockResolvedValue({
      status: GatewayTransferStatus.PENDING,
    } as GatewayTransfer);

    await handler.execute(command);

    expect(payout.markReversalProcessing).toHaveBeenCalledWith(reversal.getId());
    expect(payout.markReversalSucceeded).not.toHaveBeenCalled();
    expect(payout.markReversalFailed).not.toHaveBeenCalled();
  });

  it('should mark reversal as failed if transfer is canceled', async () => {
    const command = new ProcessPayoutReversalCommand(commandProps);
    createInboundTransferSpy.mockResolvedValue({
      status: GatewayTransferStatus.CANCELED,
    } as GatewayTransfer);

    await handler.execute(command);

    expect(payout.markReversalFailed).toHaveBeenCalledWith(reversal.getId());
    expect(payout.markReversalProcessing).not.toHaveBeenCalled();
    expect(payout.markReversalSucceeded).not.toHaveBeenCalled();
  });

  it('should mark reversal as failed if transfer is failed', async () => {
    const command = new ProcessPayoutReversalCommand(commandProps);
    createInboundTransferSpy.mockResolvedValue({
      status: GatewayTransferStatus.FAILED,
    } as GatewayTransfer);

    await handler.execute(command);

    expect(payout.markReversalFailed).toHaveBeenCalledWith(reversal.getId());
    expect(payout.markReversalProcessing).not.toHaveBeenCalled();
    expect(payout.markReversalSucceeded).not.toHaveBeenCalled();
  });

  it('should update payout', async () => {
    const command = new ProcessPayoutReversalCommand(commandProps);

    await handler.execute(command);

    expect(payoutRepository.update).toHaveBeenCalledWith(payout);
    const [markReversalSucceededInvOrder] = (payout.markReversalSucceeded as jest.Mock).mock.invocationCallOrder;
    const [updateInvOrder] = (payoutRepository.update as jest.Mock).mock.invocationCallOrder;
    expect(markReversalSucceededInvOrder).toBeLessThan(updateInvOrder);
  });
});
