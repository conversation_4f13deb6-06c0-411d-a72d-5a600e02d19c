import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import {
  GATEWAY_SERVICE_FACTORY_TOKEN,
  MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN,
  PAYOUT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { MarketplaceGatewayAccountRepositoryPort } from '@/domain/marketplace-gateway-account/ports';
import { Payout } from '@/domain/payout/payout';
import { PayoutRepositoryPort } from '@/domain/payout/ports';
import { GatewayServiceFactoryPort } from '@/domain/shared';
import { GatewayTransferSource, GatewayTransferStatus } from '@/domain/shared/interfaces';

import { ProcessPayoutReversalCommand } from './process-payout-reversal.command';

@CommandHandler(ProcessPayoutReversalCommand)
export class ProcessPayoutReversalCommandHandler implements ICommandHandler {
  constructor(
    @Inject(PAYOUT_REPOSITORY_TOKEN)
    private readonly payoutRepository: PayoutRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayAccountRepository: MarketplaceGatewayAccountRepositoryPort,
    @Inject(GATEWAY_SERVICE_FACTORY_TOKEN)
    private readonly gatewayServiceFactory: GatewayServiceFactoryPort,
  ) {}

  async execute(command: ProcessPayoutReversalCommand): Promise<void> {
    const { payoutId, reversalId } = command.getProps();

    const payout = await this.getPayout(payoutId);

    const reversal = payout.getReversal(reversalId);

    const marketplaceGatewayAccount = await this.getMarketplaceGatewayAccount(
      payout.getProps().marketplaceGatewayAccountId,
    );

    const gatewayService = this.gatewayServiceFactory.getGateway(payout.getProps().gateway);

    const gatewayTransfer = await gatewayService.createInboundTransfer({
      source: GatewayTransferSource.PAYOUT_REVERSAL,
      sourceId: reversal.getId(),
      amount: reversal.getProps().amount,
      sendingGatewayAccountId: marketplaceGatewayAccount.getProps().idAtGateway,
      originalSourceId: payout.getId(),
      idempotencyKey: reversal.getId(),
    });

    this.updateReversalStatus(payout, reversalId, gatewayTransfer.status);

    await this.payoutRepository.update(payout);
  }

  updateReversalStatus(
    payout: Payout,
    reversalId: string,
    gatewayTransferStatus: GatewayTransferStatus,
  ) {
    if (gatewayTransferStatus === GatewayTransferStatus.PENDING) {
      payout.markReversalProcessing(reversalId);
    }

    if (gatewayTransferStatus === GatewayTransferStatus.SETTLED) {
      payout.markReversalSucceeded(reversalId);
    }

    if (
      [GatewayTransferStatus.CANCELED, GatewayTransferStatus.FAILED].includes(gatewayTransferStatus)
    ) {
      payout.markReversalFailed(reversalId);
    }
  }

  async getMarketplaceGatewayAccount(marketplaceGatewayAccountId: string) {
    const marketplaceGatewayAccount = await this.marketplaceGatewayAccountRepository.findById(
      marketplaceGatewayAccountId,
    );

    if (!marketplaceGatewayAccount) {
      throw new Error('Marketplace gateway account not found'); // todo: custom error
    }

    return marketplaceGatewayAccount;
  }

  async getPayout(payoutId: string) {
    const payout = await this.payoutRepository.findById(payoutId);

    if (!payout) {
      throw new Error('Payout not found'); // todo: custom error
    }

    return payout;
  }
}
