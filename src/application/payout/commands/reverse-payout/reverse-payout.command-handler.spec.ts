import { Test, TestingModule } from '@nestjs/testing';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';

import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import { PAYOUT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PayoutReversalCreatedIntegrationEvent } from '@/domain/payout/integration-events';
import { Payout } from '@/domain/payout/payout';
import { PayoutReversal } from '@/domain/payout/payout-reversal';
import { AllowedCurrencies, Money } from '@/domain/shared';
import { RabbitMQClientMock } from '@/test/mocks/libs/rabbitmq-client.mock';
import { PayoutRepositoryMock } from '@/test/mocks/repositories/payout-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

import { ReversePayoutCommand, ReversePayoutCommandHandler } from '.';

describe('ReversePayoutCommandHandler', () => {
  let handler: ReversePayoutCommandHandler;
  let db: DatabaseServiceMock;
  let payoutRepository: PayoutRepositoryMock;
  let rabbitClient: RabbitMQClient;
  let commandProps: ReversePayoutCommand['props'];
  let payout: Payout;
  let reversal: PayoutReversal;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReversePayoutCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: PAYOUT_REPOSITORY_TOKEN, useClass: PayoutRepositoryMock },
        { provide: RabbitMQClient, useClass: RabbitMQClientMock },
      ],
    }).compile();

    handler = module.get<ReversePayoutCommandHandler>(ReversePayoutCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    payoutRepository = module.get<PayoutRepositoryMock>(PAYOUT_REPOSITORY_TOKEN);
    rabbitClient = module.get<RabbitMQClient>(RabbitMQClient);
    [payout] = payoutRepository.db;
    [reversal] = payout.getProps().reversals;
    commandProps = {
      amount: 10,
      currency: AllowedCurrencies.USD,
      payoutId: payout.getId(),
      marketplaceId: payout.getProps().marketplaceId,
      refundId: 'refund-id',
    };
    db.catchTransactions = false;
    payout.reverse = jest.fn().mockReturnValue({ reversalId: reversal.getId() });
    payoutRepository.update = jest.fn();
    rabbitClient.publish = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payout is not found', async () => {
    const command = new ReversePayoutCommand({
      ...commandProps,
      payoutId: 'invalid-payout-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Payout not found'));
  });

  it('should create a reversal', async () => {
    const command = new ReversePayoutCommand(commandProps);

    const result = await handler.execute(command);

    expect(payout.reverse).toHaveBeenCalledWith(
      Money.from(commandProps.amount, commandProps.currency),
      commandProps.refundId,
    );
    expect(payoutRepository.update).toHaveBeenCalledWith(payout);
    const [payoutReverseInvOrder] = (payout.reverse as jest.Mock).mock.invocationCallOrder;
    const [updateInvOrder] = (payoutRepository.update as jest.Mock).mock.invocationCallOrder;
    expect(payoutReverseInvOrder).toBeLessThan(updateInvOrder);
    expect(result).toBe(reversal);
  });

  it('should publish a payout reversal created event', async () => {
    const command = new ReversePayoutCommand(commandProps);
    // Mock the Date object to avoid time-based issues
    const fixedDate = new Date();
    jest.spyOn(global, 'Date').mockImplementation(() => fixedDate);

    await handler.execute(command);

    expect(payoutRepository.update).toHaveBeenCalledWith(payout);
    expect(rabbitClient.publish).toHaveBeenCalledWith(
      RABBIT_MQ_EXCHANGES.payout.name,
      RABBIT_MQ_EXCHANGES.payout.routingKeys.payoutReversalCreated.name,
      new PayoutReversalCreatedIntegrationEvent({
        aggregateId: payout.getId(),
        reversalId: reversal.getId(),
      }),
    );
    const [updateInvOrder] = (payoutRepository.update as jest.Mock).mock.invocationCallOrder;
    const [publishInvOrder] = (rabbitClient.publish as jest.Mock).mock.invocationCallOrder;
    expect(updateInvOrder).toBeLessThan(publishInvOrder);
  });

  it('should operate within a transaction', async () => {
    const command = new ReversePayoutCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(payout.reverse).not.toHaveBeenCalled();
    expect(payoutRepository.update).not.toHaveBeenCalled();
    expect(rabbitClient.publish).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    const result = await db.invokeNextTransaction();

    expect(payout.reverse).toHaveBeenCalled();
    expect(payoutRepository.update).toHaveBeenCalled();
    expect(rabbitClient.publish).toHaveBeenCalled();
    expect(result).toBe(reversal);
  });
});
