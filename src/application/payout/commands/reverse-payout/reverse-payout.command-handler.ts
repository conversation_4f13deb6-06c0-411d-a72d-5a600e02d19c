import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';

import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import { PAYOUT_REPOSITORY_TOKEN } from '@/domain/di-tokens';
import { PayoutReversalCreatedIntegrationEvent } from '@/domain/payout/integration-events';
import { PayoutReversal } from '@/domain/payout/payout-reversal';
import { PayoutRepositoryPort } from '@/domain/payout/ports';
import { Money } from '@/domain/shared';

import { ReversePayoutCommand } from './reverse-payout.command';

@CommandHandler(ReversePayoutCommand)
export class ReversePayoutCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(PAYOUT_REPOSITORY_TOKEN)
    private readonly payoutRepository: PayoutRepositoryPort,
    private readonly rabbitClient: RabbitMQClient,
  ) {}

  async execute(command: ReversePayoutCommand): Promise<PayoutReversal> {
    const { amount, currency, payoutId, marketplaceId, refundId } = command.getProps();

    const payout = await this.getPayout(payoutId, marketplaceId);

    return this.db.$transaction(async () => {
      const { reversalId } = payout.reverse(Money.from(amount, currency), refundId);

      await this.payoutRepository.update(payout);

      await this.rabbitClient.publish(
        RABBIT_MQ_EXCHANGES.payout.name,
        RABBIT_MQ_EXCHANGES.payout.routingKeys.payoutReversalCreated.name,
        new PayoutReversalCreatedIntegrationEvent({ aggregateId: payout.getId(), reversalId }),
      );

      return payout.getReversal(reversalId);
    });
  }

  async getPayout(payoutId: string, marketplaceId: string) {
    const payout = await this.payoutRepository.findByIdAndMarketplaceId(payoutId, marketplaceId);

    if (!payout) {
      throw new Error('Payout not found'); // todo: add custom error
    }

    return payout;
  }
}
