import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';

import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import {
  MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN,
  PAYOUT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { MarketplaceGatewayAccountRepositoryPort } from '@/domain/marketplace-gateway-account/ports';
import { PayoutCreatedIntegrationEvent } from '@/domain/payout/integration-events';
import { Payout, PayoutStatus } from '@/domain/payout/payout';
import { PayoutRepositoryPort } from '@/domain/payout/ports';
import { Money } from '@/domain/shared';

import { CreatePayoutCommand } from './create-payout.command';

@CommandHandler(CreatePayoutCommand)
export class CreatePayoutCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(PAYOUT_REPOSITORY_TOKEN)
    private readonly payoutRepository: PayoutRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayAccountRepository: MarketplaceGatewayAccountRepositoryPort,
    private readonly rabbitClient: RabbitMQClient,
  ) {}

  async execute(command: CreatePayoutCommand): Promise<void> {
    const { amount, currency, marketplaceGatewayAccountId, type, marketplaceId, paymentId } =
      command.getProps();

    const marketplaceGatewayAccount = await this.marketplaceGatewayAccountRepository.findById(
      marketplaceGatewayAccountId,
    );

    if (!marketplaceGatewayAccount) {
      throw new Error('Marketplace gateway account not found'); // todo: custom error
    }

    await this.db.$transaction(async () => {
      const payout = Payout.create({
        amount: Money.from(amount, currency),
        marketplaceGatewayAccountId,
        gateway: marketplaceGatewayAccount.getProps().gateway,
        status: PayoutStatus.PENDING,
        marketplaceId,
        type,
        paymentId,
      });

      await this.payoutRepository.create(payout);

      await this.rabbitClient.publish(
        RABBIT_MQ_EXCHANGES.payout.name,
        RABBIT_MQ_EXCHANGES.payout.routingKeys.payoutCreated.name,
        new PayoutCreatedIntegrationEvent({ aggregateId: payout.getId() }),
      );
    });
  }
}
