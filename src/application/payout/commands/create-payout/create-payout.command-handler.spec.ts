import { Test, TestingModule } from '@nestjs/testing';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';

import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import {
  MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN,
  PAYOUT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { MarketplaceGatewayAccount } from '@/domain/marketplace-gateway-account/marketplace-gateway-account';
import { PayoutCreatedIntegrationEvent } from '@/domain/payout/integration-events';
import { Payout, PayoutStatus, PayoutType } from '@/domain/payout/payout';
import { AllowedCurrencies, Money } from '@/domain/shared';
import { RabbitMQClientMock } from '@/test/mocks/libs/rabbitmq-client.mock';
import { MarketplaceGatewayAccountRepositoryMock } from '@/test/mocks/repositories/marketplace-gateway-account-repository.mock';
import { PayoutRepositoryMock } from '@/test/mocks/repositories/payout-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

import { CreatePayoutCommand, CreatePayoutCommandHandler } from '.';

describe('CreatePayoutCommandHandler', () => {
  let handler: CreatePayoutCommandHandler;
  let db: DatabaseServiceMock;
  let payoutRepository: PayoutRepositoryMock;
  let marketplaceGatewayAccountRepository: MarketplaceGatewayAccountRepositoryMock;
  let rabbitClient: RabbitMQClient;
  let commandProps: CreatePayoutCommand['props'];
  let marketplaceGatewayAccount: MarketplaceGatewayAccount;
  let payout: Payout;
  let payoutCreateSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreatePayoutCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: PAYOUT_REPOSITORY_TOKEN, useClass: PayoutRepositoryMock },
        {
          provide: MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN,
          useClass: MarketplaceGatewayAccountRepositoryMock,
        },
        { provide: RabbitMQClient, useClass: RabbitMQClientMock },
      ],
    }).compile();

    handler = module.get<CreatePayoutCommandHandler>(CreatePayoutCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    payoutRepository = module.get<PayoutRepositoryMock>(PAYOUT_REPOSITORY_TOKEN);
    marketplaceGatewayAccountRepository = module.get<MarketplaceGatewayAccountRepositoryMock>(
      MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN,
    );
    rabbitClient = module.get<RabbitMQClient>(RabbitMQClient);
    [marketplaceGatewayAccount] = marketplaceGatewayAccountRepository.db;
    [payout] = payoutRepository.db;
    commandProps = {
      amount: 10,
      currency: AllowedCurrencies.USD,
      marketplaceGatewayAccountId: marketplaceGatewayAccount.getId(),
      marketplaceId: 'marketplace-id',
      paymentId: 'payment-id',
      type: PayoutType.PAYMENT_FEE,
    };
    payoutCreateSpy = jest.spyOn(Payout, 'create').mockReturnValue(payout);
    payoutRepository.create = jest.fn();
    rabbitClient.publish = jest.fn();

    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if marketplace gateway account is not found', async () => {
    const command = new CreatePayoutCommand({
      ...commandProps,
      marketplaceGatewayAccountId: 'invalid-marketplace-gateway-account-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(
      new Error('Marketplace gateway account not found'),
    );
  });

  it('should create a payout', async () => {
    const command = new CreatePayoutCommand(commandProps);

    await handler.execute(command);

    expect(payoutCreateSpy).toHaveBeenCalledWith({
      amount: Money.from(commandProps.amount, commandProps.currency),
      marketplaceGatewayAccountId: commandProps.marketplaceGatewayAccountId,
      gateway: marketplaceGatewayAccount.getProps().gateway,
      status: PayoutStatus.PENDING,
      marketplaceId: commandProps.marketplaceId,
      type: commandProps.type,
      paymentId: commandProps.paymentId,
    });
    expect(payoutRepository.create).toHaveBeenCalledWith(payout);
  });

  it('should publish an event after creating a payout', async () => {
    const command = new CreatePayoutCommand(commandProps);
    // Mock the Date object to avoid time-based issues
    const fixedDate = new Date();
    jest.spyOn(global, 'Date').mockImplementation(() => fixedDate);

    await handler.execute(command);

    expect(payoutRepository.create).toHaveBeenCalledWith(payout);
    expect(rabbitClient.publish).toHaveBeenCalledWith(
      RABBIT_MQ_EXCHANGES.payout.name,
      RABBIT_MQ_EXCHANGES.payout.routingKeys.payoutCreated.name,
      new PayoutCreatedIntegrationEvent({ aggregateId: payout.getId() }),
    );
    const [payoutCreateInvOrder] = (payoutRepository.create as jest.Mock).mock.invocationCallOrder;
    const [rabbitPublishInvOrder] = (rabbitClient.publish as jest.Mock).mock.invocationCallOrder;
    expect(payoutCreateInvOrder).toBeLessThan(rabbitPublishInvOrder);
  });

  it('should operate within a transaction', async () => {
    const command = new CreatePayoutCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(payoutCreateSpy).not.toHaveBeenCalled();
    expect(payoutRepository.create).not.toHaveBeenCalled();
    expect(rabbitClient.publish).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    expect(payoutRepository.create).toHaveBeenCalled();
    expect(rabbitClient.publish).toHaveBeenCalled();
    expect(rabbitClient.publish).toHaveBeenCalled();
  });
});
