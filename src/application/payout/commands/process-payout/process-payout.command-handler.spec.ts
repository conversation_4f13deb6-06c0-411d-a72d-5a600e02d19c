import { Test, TestingModule } from '@nestjs/testing';

import {
  GATEWAY_SERVICE_FACTORY_TOKEN,
  MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN,
  PAYOUT_REPOSITORY_TOKEN
} from '@/domain/di-tokens';
import {
  MarketplaceGatewayAccount
} from '@/domain/marketplace-gateway-account/marketplace-gateway-account';
import { Payout } from '@/domain/payout/payout';
import {
  GatewayTransfer,
  GatewayTransferSource,
  GatewayTransferStatus,
} from '@/domain/shared/interfaces';
import { MarketplaceGatewayAccountRepositoryMock } from '@/test/mocks/repositories/marketplace-gateway-account-repository.mock';
import { PayoutRepositoryMock } from '@/test/mocks/repositories/payout-repository.mock';
import {
  GatewayServiceFactoryMock,
  MockGatewayServiceAdapter,
} from '@/test/mocks/services/gateway-service-factory.mock';

import { ProcessPayoutCommand, ProcessPayoutCommandHandler } from '.';

describe('ProcessPayoutCommandHandler', () => {
  let handler: ProcessPayoutCommandHandler;
  let payoutRepository: PayoutRepositoryMock;
  let marketplaceGatewayAccountRepository: MarketplaceGatewayAccountRepositoryMock;
  let commandProps: ProcessPayoutCommand['props'];
  let payout: Payout;
  let marketplaceGatewayAccount: MarketplaceGatewayAccount;
  let createOutboundTransferSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessPayoutCommandHandler,
        { provide: PAYOUT_REPOSITORY_TOKEN, useClass: PayoutRepositoryMock },
        { provide: MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN, useClass: MarketplaceGatewayAccountRepositoryMock },
        { provide: GATEWAY_SERVICE_FACTORY_TOKEN, useClass: GatewayServiceFactoryMock },
      ],
    }).compile();

    handler = module.get<ProcessPayoutCommandHandler>(ProcessPayoutCommandHandler);
    payoutRepository = module.get<PayoutRepositoryMock>(PAYOUT_REPOSITORY_TOKEN);
    marketplaceGatewayAccountRepository = module.get<MarketplaceGatewayAccountRepositoryMock>(MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN);
    [payout] = payoutRepository.db;
    [marketplaceGatewayAccount] = marketplaceGatewayAccountRepository.db;
    commandProps = {
      payoutId: payout.getId(),
    };
    createOutboundTransferSpy = jest
      .spyOn(MockGatewayServiceAdapter.prototype, 'createOutboundTransfer')
      .mockResolvedValue({
        status: GatewayTransferStatus.SETTLED,
      } as GatewayTransfer);
    payout.markProcessing = jest.fn();
    payout.markSucceeded = jest.fn();
    payout.markFailed = jest.fn();
    payoutRepository.update = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if payout is not found', async () => {
    const command = new ProcessPayoutCommand({
      payoutId: 'invalid-payout-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Payout not found'));
  });

  it('should fail if marketplace gateway account is not found', async () => {
    const command = new ProcessPayoutCommand(commandProps);
    jest.spyOn(payoutRepository, 'findById').mockResolvedValue(new Payout({
      id: payout.getId(),
      props: {
        ...payout.getProps(),
        marketplaceGatewayAccountId: 'invalid-marketplace-gateway-account-id',
      }
    }));

    await expect(handler.execute(command)).rejects.toThrow(new Error('Marketplace gateway account not found'));
  });

  it('should create outbound transfer', async () => {
    const command = new ProcessPayoutCommand(commandProps);

    await handler.execute(command);

    expect(createOutboundTransferSpy).toHaveBeenCalledWith({
      source: GatewayTransferSource.PAYOUT,
      sourceId: payout.getId(),
      amount: payout.getProps().amount,
      receivingGatewayAccountId: marketplaceGatewayAccount.getProps().idAtGateway,
      idempotencyKey: payout.getId(),
    });
  });

  it('should mark payout as succeeded if transfer is settled', async () => {
    const command = new ProcessPayoutCommand(commandProps);

    await handler.execute(command);

    expect(payout.markSucceeded).toHaveBeenCalled();
    expect(payout.markProcessing).not.toHaveBeenCalled();
    expect(payout.markFailed).not.toHaveBeenCalled();
  });

  it('should mark payout as processing if transfer is pending', async () => {
    const command = new ProcessPayoutCommand(commandProps);
    createOutboundTransferSpy.mockResolvedValue({
      status: GatewayTransferStatus.PENDING,
    } as GatewayTransfer);

    await handler.execute(command);

    expect(payout.markProcessing).toHaveBeenCalled();
    expect(payout.markSucceeded).not.toHaveBeenCalled();
    expect(payout.markFailed).not.toHaveBeenCalled();
  });

  it('should mark payout as failed if transfer is canceled', async () => {
    const command = new ProcessPayoutCommand(commandProps);
    createOutboundTransferSpy.mockResolvedValue({
      status: GatewayTransferStatus.CANCELED,
    } as GatewayTransfer);

    await handler.execute(command);

    expect(payout.markFailed).toHaveBeenCalled();
    expect(payout.markSucceeded).not.toHaveBeenCalled();
    expect(payout.markProcessing).not.toHaveBeenCalled();
  });

  it('should mark payout as failed if transfer is failed', async () => {
    const command = new ProcessPayoutCommand(commandProps);
    createOutboundTransferSpy.mockResolvedValue({
      status: GatewayTransferStatus.FAILED,
    } as GatewayTransfer);

    await handler.execute(command);

    expect(payout.markFailed).toHaveBeenCalled();
    expect(payout.markSucceeded).not.toHaveBeenCalled();
    expect(payout.markProcessing).not.toHaveBeenCalled();
  });

  it('should update payout after processing', async () => {
    const command = new ProcessPayoutCommand(commandProps);

    await handler.execute(command);

    expect(payoutRepository.update).toHaveBeenCalledWith(payout);
    const [markSuccessInvOrder] = (payout.markSucceeded as jest.Mock).mock.invocationCallOrder;
    const [updateInvOrder] = (payoutRepository.update as jest.Mock).mock.invocationCallOrder;
    expect(markSuccessInvOrder).toBeLessThan(updateInvOrder);
  });
});
