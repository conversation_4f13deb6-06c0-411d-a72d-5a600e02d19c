import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import {
  GATEWAY_SERVICE_FACTORY_TOKEN,
  MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN,
  PAYOUT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { MarketplaceGatewayAccountRepositoryPort } from '@/domain/marketplace-gateway-account/ports';
import { PayoutRepositoryPort } from '@/domain/payout/ports';
import { GatewayServiceFactoryPort } from '@/domain/shared';
import { GatewayTransferSource, GatewayTransferStatus } from '@/domain/shared/interfaces';

import { ProcessPayoutCommand } from './process-payout.command';

@CommandHandler(ProcessPayoutCommand)
export class ProcessPayoutCommandHandler implements ICommandHandler {
  constructor(
    @Inject(PAYOUT_REPOSITORY_TOKEN)
    private readonly payoutRepository: PayoutRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayAccountRepository: MarketplaceGatewayAccountRepositoryPort,
    @Inject(GATEWAY_SERVICE_FACTORY_TOKEN)
    private readonly gatewayServiceFactory: GatewayServiceFactoryPort,
  ) {}

  async execute(command: ProcessPayoutCommand): Promise<void> {
    const { payoutId } = command.getProps();

    const payout = await this.getPayout(payoutId);

    const marketplaceGatewayAccount = await this.getMarketplaceGatewayAccount(
      payout.getProps().marketplaceGatewayAccountId,
    );

    const gatewayService = this.gatewayServiceFactory.getGateway(payout.getProps().gateway);

    const gatewayTransfer = await gatewayService.createOutboundTransfer({
      source: GatewayTransferSource.PAYOUT,
      sourceId: payout.getId(),
      amount: payout.getProps().amount,
      receivingGatewayAccountId: marketplaceGatewayAccount.getProps().idAtGateway,
      idempotencyKey: payout.getId(),
    });

    if (gatewayTransfer.status === GatewayTransferStatus.PENDING) {
      payout.markProcessing();
    }

    if (gatewayTransfer.status === GatewayTransferStatus.SETTLED) {
      payout.markSucceeded();
    }

    if (
      [GatewayTransferStatus.CANCELED, GatewayTransferStatus.FAILED].includes(
        gatewayTransfer.status,
      )
    ) {
      payout.markFailed();
    }

    await this.payoutRepository.update(payout);
  }

  async getMarketplaceGatewayAccount(marketplaceGatewayAccountId: string) {
    const marketplaceGatewayAccount = await this.marketplaceGatewayAccountRepository.findById(
      marketplaceGatewayAccountId,
    );

    if (!marketplaceGatewayAccount) {
      throw new Error('Marketplace gateway account not found'); // todo: custom error
    }

    return marketplaceGatewayAccount;
  }

  async getPayout(payoutId: string) {
    const payout = await this.payoutRepository.findById(payoutId);

    if (!payout) {
      throw new Error('Payout not found'); // todo: custom error
    }

    return payout;
  }
}
