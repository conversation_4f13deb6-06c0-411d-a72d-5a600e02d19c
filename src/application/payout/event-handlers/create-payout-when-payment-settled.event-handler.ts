import { Inject } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';

import {
  MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { MarketplaceGatewayAccount } from '@/domain/marketplace-gateway-account/marketplace-gateway-account';
import { MarketplaceGatewayAccountRepositoryPort } from '@/domain/marketplace-gateway-account/ports';
import { PaymentSettledEvent } from '@/domain/payment/events';
import { Payment } from '@/domain/payment/payment';
import { PaymentRepositoryPort } from '@/domain/payment/ports/payment.repository';
import { PayoutType } from '@/domain/payout/payout';
import { Money } from '@/domain/shared';

import { CreatePayoutCommand } from '../commands/create-payout';

@EventsHandler(PaymentSettledEvent)
export class CreatePayoutWhenPaymentSettledEventHandler implements IEventHandler {
  constructor(
    private readonly commandBus: CommandBus,
    @Inject(PAYMENT_REPOSITORY_TOKEN)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayAccountRepository: MarketplaceGatewayAccountRepositoryPort,
  ) {}

  async handle(event: PaymentSettledEvent) {
    const payment = await this.paymentRepository.findById(event.getProps().aggregateId);

    if (!payment) {
      throw new Error('Payment not found'); // todo: custom error
    }

    const marketplaceGatewayAccount =
      await this.marketplaceGatewayAccountRepository.findByMarketplaceAndGatewayId(
        payment.getProps().marketplaceId,
        payment.getProps().gateway.getGatewayId(),
      );

    if (!marketplaceGatewayAccount) {
      throw new Error('Marketplace Gateway Account not found'); // todo: custom error
    }

    const paymentFeeProfit = this.calculatePaymentFeeProfit(payment);

    if (paymentFeeProfit.isPositive()) {
      await this.createPayout({
        payment,
        marketplaceGatewayAccount,
        amount: paymentFeeProfit,
        type: PayoutType.PAYMENT_FEE,
      });
    }

    const { marketplaceOrderFee } = payment.getProps();

    if (marketplaceOrderFee.isPositive()) {
      await this.createPayout({
        payment,
        marketplaceGatewayAccount,
        amount: marketplaceOrderFee,
        type: PayoutType.ORDER_FEE,
      });
    }
  }

  private async createPayout({
    payment,
    marketplaceGatewayAccount,
    amount,
    type,
  }: {
    marketplaceGatewayAccount: MarketplaceGatewayAccount;
    payment: Payment;
    amount: Money;
    type: PayoutType;
  }) {
    await this.commandBus.execute(
      new CreatePayoutCommand({
        amount: amount.getProps().amount.toNumber(),
        currency: amount.getCurrency().getValue(),
        marketplaceGatewayAccountId: marketplaceGatewayAccount.getId(),
        type,
        marketplaceId: payment.getProps().marketplaceId,
        paymentId: payment.getId(),
      }),
    );
  }

  private calculatePaymentFeeProfit(payment: Payment) {
    const { marketplacePaymentFee, platformPaymentFee } = payment.getProps();

    return marketplacePaymentFee.sub(platformPaymentFee);
  }
}
