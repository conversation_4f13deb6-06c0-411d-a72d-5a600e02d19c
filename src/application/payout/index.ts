import { CreatePayout<PERSON>ommandHandler } from './commands/create-payout';
import { CreatePayoutRefundReversalsCommandHandler } from './commands/create-payout-refund-reversals/create-payout-refund-reversals.command-handler';
import { ProcessPayoutCommandHandler } from './commands/process-payout';
import { ProcessPayoutReversalCommandHandler } from './commands/process-payout-reversal';
import { ReversePayoutCommandHandler } from './commands/reverse-payout';
import { CreatePayoutWhenPaymentSettledEventHandler } from './event-handlers';

export const PAYOUT_COMMAND_HANDLERS = [
  CreatePayoutCommandHandler,
  ProcessPayoutCommandHandler,
  ProcessPayoutReversalCommandHandler,
  ReversePayoutCommandHandler,
  CreatePayoutRefundReversalsCommandHandler,
];

export const PAYOUT_EVENT_HANDLERS = [CreatePayoutWhenPaymentSettledEventHandler];
