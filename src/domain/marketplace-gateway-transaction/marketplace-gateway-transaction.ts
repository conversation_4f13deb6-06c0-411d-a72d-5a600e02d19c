import { AggregateRoot, idFactory } from '@/core/ddd';
import { MarketplaceGatewayTransactionCreatedDomainEvent } from '@/domain/marketplace-gateway-transaction/events';

import { Money } from '../shared';

export type EntityProps = {
  amount: Money;
  marketplaceGatewayId: string;
  transactionId?: string;
};

export class MarketplaceGatewayTransaction extends AggregateRoot<EntityProps> {
  static create(props: EntityProps) {
    const transaction = new MarketplaceGatewayTransaction({
      id: idFactory(),
      props,
    });

    transaction.addEvent(
      new MarketplaceGatewayTransactionCreatedDomainEvent({
        aggregateId: transaction.getId(),
      }),
    );

    return transaction;
  }
}
