import { AggregateRoot, idFactory } from '@/core/ddd';

import { MarketplaceGatewayAccountCreatedDomainEvent } from './events';
import { Gateway } from '../shared';

type EntityProps = {
  name: string | null;
  gateway: Gateway;
  idAtGateway: string;
  marketplaceGatewayId: string;
};

export class MarketplaceGatewayAccount extends AggregateRoot<EntityProps> {
  static create(props: EntityProps) {
    const marketplaceGatewayAccount = new MarketplaceGatewayAccount({
      id: idFactory(),
      props,
    });

    marketplaceGatewayAccount.addEvent(
      new MarketplaceGatewayAccountCreatedDomainEvent({
        aggregateId: marketplaceGatewayAccount.getId(),
      }),
    );

    return marketplaceGatewayAccount;
  }
}
