import dayjs from 'dayjs';

import { WebhookData } from '@/application/webhook/interfaces/webhook-event.interface';
import { AggregateRoot, checkRules, idFactory } from '@/core/ddd';

import { MaximumRetriesExceededError } from './errors';
import {
  WebhookCreatedDomainEvent,
  WebhookDeliveredDomainEvent,
  WebhookFailedDomainEvent,
} from './events';
import { WebhookMustNotBeDeliveredRule } from './rules/webhook-must-not-be-delivered.rule';
import { Gateway } from '../shared';

export enum WebhookType {
  PAYMENT_INTENT_SUCCEEDED = 'payment-intent.succeeded',
  PAYMENT_INTENT_FAILED = 'payment-intent.failed',
  PAYMENT_INTENT_CREATED = 'payment-intent.created',
  PAYMENT_SETTLED = 'payment.settled',
  PAYMENT_FAILED = 'payment.failed',
  PAYMENT_SPLIT_CREATED = 'payment-split.created',
  PAYMENT_SPLIT_SETTLED = 'payment-split.settled',
  PAYMENT_SPLIT_FAILED = 'payment-split.failed',
  PAYMENT_SPLIT_CANCELED = 'payment-split.canceled',
  REFUND_SPLIT_CREATED = 'refund-split.created',
  REFUND_SPLIT_SETTLED = 'refund-split.settled',
  REFUND_SPLIT_FAILED = 'refund-split.failed',
  REFUND_SPLIT_CANCELED = 'refund-split.canceled',
  REFUND_SPLIT_PROCESSING = 'refund-split.processing',
  REFUND_CREATED = 'refund.created',
  REFUND_CANCELED = 'refund.canceled',
  REFUND_ISSUED = 'refund.issued',
  REFUND_FAILED = 'refund.failed',
  DISPUTE_CREATED = 'dispute.created',
  DISPUTE_UPDATED = 'dispute.update',
}

export enum WebhookObject {
  PAYMENT_INTENT = 'payment-intent',
  PAYMENT = 'payment',
  PAYMENT_SPLIT = 'payment-split',
  REFUND_SPLIT = 'refund-split',
  REFUND = 'refund',
  DISPUTE = 'dispute',
}

export enum WebhookStatus {
  PENDING = 'pending',
  DELIVERED = 'delivered',
  SCHEDULING = 'scheduling',
  SCHEDULED = 'scheduled',
  FAILED = 'failed',
}

type EntityProps = {
  marketplaceId: string;
  type: WebhookType;
  gateway: Gateway;
  object: WebhookObject;
  status: WebhookStatus;
  failReason: string | null;
  fails: number;
  lastAttemptAt: Date | null;
  nextRetryAt: Date | null;
  data: WebhookData;
  url: string;
};

type CreateEntityProps = Pick<
  EntityProps,
  'data' | 'gateway' | 'object' | 'type' | 'url' | 'marketplaceId'
>;

export class Webhook extends AggregateRoot<EntityProps> {
  private readonly BACKOFF_TIMES = [
    { amount: 2, unit: 'minutes' },
    { amount: 6, unit: 'minutes' },
    { amount: 30, unit: 'minutes' },
    { amount: 1, unit: 'hours' },
    { amount: 5, unit: 'hours' },
    { amount: 1, unit: 'days' },
    { amount: 2, unit: 'days' },
  ] satisfies { amount: number; unit: dayjs.ManipulateType }[];

  private readonly MAX_RETRIES = this.BACKOFF_TIMES.length;

  static create(data: CreateEntityProps) {
    const webhook = new Webhook({
      id: idFactory(),
      props: {
        ...data,
        nextRetryAt: null,
        lastAttemptAt: null,
        failReason: null,
        fails: 0,
        status: WebhookStatus.PENDING,
      },
    });

    webhook.addEvent(new WebhookCreatedDomainEvent({ aggregateId: webhook.getId() }));

    return webhook;
  }

  scheduleRetry() {
    checkRules(new WebhookMustNotBeDeliveredRule(this.props.status));

    if (!this.canRetry()) {
      throw new MaximumRetriesExceededError();
    }

    this.props.nextRetryAt = this.getNextRetryTime();
    this.props.status = WebhookStatus.PENDING;

    return this.props.nextRetryAt;
  }

  markAsDelivered() {
    checkRules(new WebhookMustNotBeDeliveredRule(this.props.status));

    this.props.status = WebhookStatus.DELIVERED;
    this.updateLastAttempt();

    this.addEvent(new WebhookDeliveredDomainEvent({ aggregateId: this.getId() }));
  }

  recordFailure(reason: string) {
    checkRules(new WebhookMustNotBeDeliveredRule(this.props.status));

    this.props.fails += 1;
    this.props.failReason = reason;
    this.updateLastAttempt();

    if (!this.canRetry()) {
      this.props.status = WebhookStatus.FAILED;
    }

    this.addEvent(new WebhookFailedDomainEvent({ aggregateId: this.getId() }));
  }

  canRetry() {
    return this.props.fails < this.MAX_RETRIES;
  }

  private updateLastAttempt() {
    this.props.lastAttemptAt = new Date();
  }

  private getNextRetryTime() {
    const now = dayjs();

    if (this.props.fails > this.MAX_RETRIES) {
      throw new MaximumRetriesExceededError();
    }

    const backoff = this.BACKOFF_TIMES[this.props.fails - 1];

    return now.add(backoff.amount, backoff.unit).toDate();
  }
}
