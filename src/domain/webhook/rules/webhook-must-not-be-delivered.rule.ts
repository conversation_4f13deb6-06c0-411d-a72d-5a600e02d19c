import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { WebhookAlreadyDeliveredError } from '../errors';
import { WebhookStatus } from '../webhook';

export class WebhookMustNotBeDeliveredRule implements IBusinessRule {
  constructor(private readonly status: WebhookStatus) {}

  getError(): DomainError {
    return new WebhookAlreadyDeliveredError();
  }

  isBroken(): boolean {
    return this.status === WebhookStatus.DELIVERED;
  }
}
