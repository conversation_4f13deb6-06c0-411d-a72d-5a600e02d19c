import { WebhookAlreadyDeliveredError } from '@/domain/webhook/errors';
import { WebhookStatus } from '@/domain/webhook/webhook';

import { WebhookMustNotBeDeliveredRule } from '.';

describe('WebhookMustNotBeDeliveredRule', () => {
  it('should success if webhook status is not delivered and fail otherwise', () => {
    const rule = new WebhookMustNotBeDeliveredRule('other' as WebhookStatus);
    expect(rule.isBroken()).toBe(false);

    const rule2 = new WebhookMustNotBeDeliveredRule(WebhookStatus.DELIVERED);
    expect(rule2.isBroken()).toBe(true);
  });

  it('should return an WebhookAlreadyDeliveredError to indicate the webhook is already delivered', () => {
    const rule = new WebhookMustNotBeDeliveredRule(WebhookStatus.PENDING);
    expect(rule.getError()).toBeInstanceOf(WebhookAlreadyDeliveredError);
  });
});
