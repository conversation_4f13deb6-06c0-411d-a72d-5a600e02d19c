import { Injectable } from '@nestjs/common';
import { checkRules } from '@sw-web/nestjs-core/domain';

import { Money } from '@/domain/shared';

import { FeeSettings } from '../fee-settings';
import { FeeSettingsCannotHaveMultipleDefaultsRule } from '../rules';

export type CreateFeeSettingsProps = {
  platformFeeFixed: Money;
  platformFeePercentage: number;
  marketplaceFeeFixed: Money;
  marketplaceFeePercentage: number;
  marketplaceGatewayId: string;
  isDefault: boolean;
  marketplaceGatewayFeeSettings: FeeSettings[];
};

@Injectable()
export class FeeSettingsDomainService {
  createFeeSettings({
    marketplaceGatewayFeeSettings,
    ...props
  }: CreateFeeSettingsProps): FeeSettings {
    checkRules(
      new FeeSettingsCannotHaveMultipleDefaultsRule(marketplaceGatewayFeeSettings, props.isDefault),
    );

    return FeeSettings.create({
      ...props,
    });
  }
}
