import { IBusinessRule } from '@sw-web/nestjs-core/domain';

import { DomainError } from '@/core/ddd';

import { DuplicateDefaultsFeeSettingsError } from '../errors';
import { FeeSettings } from '../fee-settings';

export class FeeSettingsCannotHaveMultipleDefaultsRule implements IBusinessRule {
  constructor(
    private readonly existingMarketplaceGatewayFeeSettings: FeeSettings[],
    private readonly isNewFeeSettingsDefault: boolean,
  ) {}

  getError(): DomainError {
    return new DuplicateDefaultsFeeSettingsError();
  }

  isBroken(): boolean {
    return (
      this.isNewFeeSettingsDefault &&
      this.existingMarketplaceGatewayFeeSettings.some(
        (feeSettings) => feeSettings.getProps().isDefault,
      )
    );
  }
}
