import Stripe from 'stripe';

import { AggregateRoot, checkRules, idFactory } from '@/core/ddd';
import {
  DisputeCreatedDomainEvent,
  DisputeUpdatedDomainEvent,
  DisputeFundsWithdrawnDomainEvent,
} from '@/domain/dispute/events';
import { DisputeLostDomainEvent } from '@/domain/dispute/events/dispute-lost.domain-event';
import { DisputeWonDomainEvent } from '@/domain/dispute/events/dispute-won.domain-event';
import { MustNotBeWonOrLostRule } from '@/domain/dispute/rules';
import { StripeDispute } from '@/domain/dispute/stripe-dispute';

import { AllowedCurrencies, Gateway, Money, MoneyCurrencyMustMatchRule } from '../shared';

export enum DisputeStatus {
  WARNING_NEEDS_RESPONSE = 'warning-needs-response',
  WARNING_UNDER_REVIEW = 'warning-under-review',
  WARNING_CLOSED = 'warning-closed',
  NEEDS_RESPONSE = 'needs-response',
  RESPONSE_DISABLED = 'response-disabled',
  UNDER_REVIEW = 'under-review',
  CHARGE_REFUNDED = 'charge-refunded',
  WON = 'won',
  LOST = 'lost',
}

export type DisputeEvidence = Stripe.Dispute.Evidence;
export type DisputeEvidenceDetails = Stripe.Dispute.EvidenceDetails;

export type GatewayDispute = StripeDispute;

export type EntityProps = {
  amount: Money;
  currency: AllowedCurrencies;
  status: DisputeStatus;
  reason: string;
  evidence: DisputeEvidence;
  evidenceDetails: DisputeEvidenceDetails;
  gatewayId: string;
  gateway: Gateway;
  paymentIntentId: string;
  paymentId: string;
  gatewayDispute: GatewayDispute;
};

export type DisputeWithdrawnProps = {
  amount: Money;
  fee: Money;
  marketplaceGatewayId: string;
  relatedTransactionId?: string;
};

export class Dispute extends AggregateRoot<EntityProps> {
  static create(props: EntityProps) {
    const dispute = new Dispute({
      id: idFactory(),
      props,
    });

    dispute.addEvent(new DisputeCreatedDomainEvent({ aggregateId: dispute.getId() }));

    return dispute;
  }

  update(data: Partial<EntityProps>) {
    this.props = {
      ...this.props,
      evidence: data.evidence,
      evidenceDetails: data.evidenceDetails,
      reason: data.reason,
      status: data.status,
    };

    this.addEvent(new DisputeUpdatedDomainEvent({ aggregateId: this.getId() }));

    return this;
  }

  markAsWon() {
    checkRules(new MustNotBeWonOrLostRule(this.props.status));

    this.props.status = DisputeStatus.WON;

    this.addEvent(new DisputeWonDomainEvent({ aggregateId: this.getId() }));
  }

  markAsLost() {
    checkRules(new MustNotBeWonOrLostRule(this.props.status));

    this.props.status = DisputeStatus.LOST;

    this.addEvent(new DisputeLostDomainEvent({ aggregateId: this.getId() }));
  }

  fundsWithdrawn(disputeWithdrawnProps: DisputeWithdrawnProps) {
    const { amount, relatedTransactionId, marketplaceGatewayId } = disputeWithdrawnProps;

    checkRules(new MoneyCurrencyMustMatchRule(this.props.amount, amount));

    this.addEvent(
      new DisputeFundsWithdrawnDomainEvent({
        aggregateId: this.getId(),
        amount,
        marketplaceGatewayId,
        relatedTransactionId,
      }),
    );
  }
}
