import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { DisputeStatus } from '../dispute';
import { InvalidDisputeStatusError } from '../errors';


export class MustNotBeWonOrLostRule implements IBusinessRule {
  constructor(private readonly status: DisputeStatus) {}

  getError(): DomainError {
    return new InvalidDisputeStatusError();
  }

  isBroken(): boolean {
    return [DisputeStatus.WON, DisputeStatus.LOST].includes(this.status);
  }
}
