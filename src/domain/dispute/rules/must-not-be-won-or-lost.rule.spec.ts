import { DisputeStatus } from '@/domain/dispute/dispute';
import { InvalidDisputeStatusError } from '@/domain/dispute/errors';

import { MustNotBeWonOrLostRule } from './must-not-be-won-or-lost.rule';

describe('MustNotBeWonOrLostRule', () => {
  it('should succeed if dispute status is not won or lost and fail otherwise', () => {
    expect(
      new MustNotBeWonOrLostRule('other' as DisputeStatus).isBroken()
    ).toBe(false);
    expect(
      new MustNotBeWonOrLostRule(DisputeStatus.WON).isBroken()
    ).toBe(true);
    expect(
      new MustNotBeWonOrLostRule(DisputeStatus.LOST).isBroken()
    ).toBe(true);
  });

  it('should return an InvalidDisputeStatusError to indicate the dispute status is won or lost', () => {
    const rule = new MustNotBeWonOrLostRule(DisputeStatus.WON);
    expect(rule.getError()).toBeInstanceOf(InvalidDisputeStatusError);
  });
});
