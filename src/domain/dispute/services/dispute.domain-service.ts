import { Injectable } from '@nestjs/common';
import Stripe from 'stripe';

import {
  Dispute,
  DisputeEvidence,
  DisputeEvidenceDetails,
  DisputeStatus,
  GatewayDispute,
} from '@/domain/dispute/dispute';
import { GatewayDisputeEntityProps, StripeDispute } from '@/domain/dispute/stripe-dispute';
import { AllowedCurrencies, Gateway, GatewayType, Money } from '@/domain/shared';

export type CreateProps = {
  amount: number;
  currency: AllowedCurrencies;
  status: DisputeStatus | Stripe.Dispute.Status;
  reason: string;
  evidence: DisputeEvidence;
  evidenceDetails: DisputeEvidenceDetails;
  gateway: Gateway;
  gatewayDisputeId: string;
  gatewayPaymentIntentId: string;
  paymentIntentId: string;
  paymentId: string;
};

@Injectable()
export class DisputeDomainService {
  mapToDisputeStatus(status: string) {
    switch (status) {
      case 'lost':
        return DisputeStatus.LOST;
      case 'needs_response':
        return DisputeStatus.NEEDS_RESPONSE;
      case 'under_review':
        return DisputeStatus.UNDER_REVIEW;
      case 'warning_closed':
        return DisputeStatus.WARNING_CLOSED;
      case 'warning_needs_response':
        return DisputeStatus.WARNING_NEEDS_RESPONSE;
      case 'warning_under_review':
        return DisputeStatus.WARNING_UNDER_REVIEW;
      case 'won':
        return DisputeStatus.WON;
      default:
        throw new Error('Invalid dispute status');
    }
  }

  create(props: CreateProps): Dispute {
    const {
      amount,
      currency,
      status,
      reason,
      evidence,
      evidenceDetails,
      gateway,
      gatewayDisputeId,
      gatewayPaymentIntentId,
      paymentId,
      paymentIntentId,
    } = props;

    const gatewayDispute = this.createGatewayDispute({
      stripeDisputeId: gatewayDisputeId,
      stripePaymentIntentId: gatewayPaymentIntentId,
      gateway,
    });

    return Dispute.create({
      amount: Money.from(amount, currency),
      currency,
      status: this.mapToDisputeStatus(status),
      reason,
      evidence,
      evidenceDetails,
      gatewayId: gateway.getGatewayId(),
      gateway,
      paymentIntentId,
      paymentId,
      gatewayDispute,
    });
  }

  createGatewayDispute(props: GatewayDisputeEntityProps): GatewayDispute {
    switch (props.gateway.getType()) {
      case GatewayType.STRIPE:
        return StripeDispute.create(props);
      default:
        throw new Error('invalid gateway type');
    }
  }

  update(dispute: Dispute, data: Partial<CreateProps>): Dispute {
    return dispute.update({
      evidence: data.evidence,
      evidenceDetails: data.evidenceDetails,
      reason: data.reason,
      status: this.mapToDisputeStatus(data.status),
    });
  }
}
