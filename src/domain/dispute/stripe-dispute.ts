import { AggregateRoot, idFactory } from '@/core/ddd';
import { Gateway } from '@/domain/shared';

export type StripeEntityProps = {
  stripeDisputeId: string;
  stripePaymentIntentId: string;
  gateway: Gateway;
};

export type GatewayDisputeEntityProps = StripeEntityProps;

export class StripeDispute extends AggregateRoot<StripeEntityProps> {
  static create(props: StripeEntityProps) {
    return new StripeDispute({
      id: idFactory(),
      props,
    });
  }
}
