import { AllowedCurrencies } from '../shared';

export class PaymentSplitRefundIssuer {
  accountId: string;
  amount: number;
  providerFee: number;
  currency: AllowedCurrencies;

  constructor({
    accountId,
    amount,
    currency,
    providerFee,
  }: {
    accountId: string;
    currency: AllowedCurrencies;
    amount: number;
    providerFee: number;
  }) {
    this.accountId = accountId;
    this.amount = amount;
    this.currency = currency;
    this.providerFee = providerFee;
  }
}
