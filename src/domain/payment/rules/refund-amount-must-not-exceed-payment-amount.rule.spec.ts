import { RefundAmountExceededRemainingAmountError } from '@/domain/payment/errors';
import * as Entities from '@/test/helpers/creators';

import { RefundAmountMustNotExceedPaymentAmountRule } from '.';

describe('RefundAmountMustNotExceedPaymentAmountRule', () => {
  it('should succeed if refund amount is less than or equal to payment amount and fail otherwise', () => {
    const payment = Entities.payment(100)
      .refundedAmount(30)
      .refunds([
        Entities.refund(10).create(),
        Entities.refund(20).create(),
      ])
      .create();

    // 100 - 30 - 10 - 20 = 40
    expect(
      new RefundAmountMustNotExceedPaymentAmountRule(payment, Entities.money(40)).isBroken()
    ).toBe(false);
    expect(
      new RefundAmountMustNotExceedPaymentAmountRule(payment, Entities.money(40.1)).isBroken()
    ).toBe(true);
  });

  it('should ignore refunds with status other than pending', () => {
    const payment = Entities.payment(100)
      .refundedAmount(0)
      .refunds([
        Entities.refund(60).create(),
        Entities.refund(10).failed().create(),
        Entities.refund(10).canceled().create(),
        Entities.refund(10).issued().create(),
      ])
      .create();

    // 100 - 60 = 40 (ignoring non-pending refunds)
    expect(
      new RefundAmountMustNotExceedPaymentAmountRule(payment, Entities.money(40)).isBroken()
    ).toBe(false);
    expect(
      new RefundAmountMustNotExceedPaymentAmountRule(payment, Entities.money(40.1)).isBroken()
    ).toBe(true);
  });

  it('should return an RefundAmountExceededRemainingAmountError to indicate the refund amount exceeded the payment amount', () => {
    const rule = new RefundAmountMustNotExceedPaymentAmountRule(
      Entities.payment(0).create(), Entities.money(0)
    );
    expect(rule.getError()).toBeInstanceOf(RefundAmountExceededRemainingAmountError);
  });
});
