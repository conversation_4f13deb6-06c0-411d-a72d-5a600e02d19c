import { InvalidRefundStatusError } from '@/domain/payment/errors';
import { PaymentStatus } from '@/domain/payment/payment';

import { CanRefundIfSettledOrPartiallyRefundedRule } from '.';

describe('CanRefundIfSettledOrPartiallyRefundedRule', () => {
  it('should succeed if payment status is settled or partially refunded and fail otherwise', () => {
    expect(
      new CanRefundIfSettledOrPartiallyRefundedRule(PaymentStatus.SETTLED).isBroken()
    ).toBe(false);
    expect(
      new CanRefundIfSettledOrPartiallyRefundedRule(PaymentStatus.PARTIALLY_REFUNDED).isBroken()
    ).toBe(false);
    expect(
      new CanRefundIfSettledOrPartiallyRefundedRule('other' as PaymentStatus).isBroken()
    ).toBe(true);
  });

  it('should return an InvalidRefundStatusError to indicate the payment status is not settled or partially refunded', () => {
    const rule = new CanRefundIfSettledOrPartiallyRefundedRule(PaymentStatus.PENDING);
    expect(rule.getError()).toBeInstanceOf(InvalidRefundStatusError);
  });
});
