import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { InvalidSettlementStatusError } from '../errors';
import { Payment, PaymentStatus } from '../payment';

export class MustBePendingToSettleRule implements IBusinessRule {
  constructor(private payment: Payment) {}

  getError() {
    return new InvalidSettlementStatusError();
  }

  isBroken(): boolean {
    return this.payment.getProps().status !== PaymentStatus.PENDING;
  }
}
