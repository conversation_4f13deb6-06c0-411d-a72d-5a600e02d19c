import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { TotalRefundSplitsExceededRefundAmountError } from '../errors';
import { RefundSplitData } from '../refund-split-data';

export class TotalRefundSplitsMustNotExceedRefundAmountRule implements IBusinessRule {
  constructor(
    private refundAmount: Money,
    private refundSplitsData: RefundSplitData[],
  ) {}

  getError() {
    return new TotalRefundSplitsExceededRefundAmountError();
  }

  isBroken(): boolean {
    const total = this.refundSplitsData.reduce(
      (accumulate: Money, split: RefundSplitData) => accumulate.add(split.amount),
      Money.zeroFrom(this.refundAmount),
    );

    return total.greaterThan(this.refundAmount);
  }
}
