import { TotalRefundSplitsExceededRefundAmountError } from '@/domain/payment/errors';
import { RefundSplitData } from '@/domain/payment/refund-split-data';
import * as Entities from '@/test/helpers/creators';

import { TotalRefundSplitsMustNotExceedRefundAmountRule } from '.';

describe('TotalRefundSplitsMustNotExceedRefundAmountRule', () => {
  it('should succeed if refund splits total does not exceed refund amount and fail otherwise', () => {
    const splitsData: RefundSplitData[] = [
      { accountId: 'account-id-1', amount: Entities.money(10) },
      { accountId: 'account-id-2', amount: Entities.money(20) },
    ];

    expect(
      new TotalRefundSplitsMustNotExceedRefundAmountRule(Entities.money(30), splitsData).isBroken()
    ).toBe(false);
    expect(
      new TotalRefundSplitsMustNotExceedRefundAmountRule(Entities.money(29.9), splitsData).isBroken()
    ).toBe(true);
  });

  it('should return an TotalRefundSplitsExceededRefundAmountError to indicate the refund splits total exceeded the refund amount', () => {
    const rule = new TotalRefundSplitsMustNotExceedRefundAmountRule(Entities.money(0), []);
    expect(rule.getError()).toBeInstanceOf(TotalRefundSplitsExceededRefundAmountError);
  });
});
