import { RefundNotPendingError } from '@/domain/payment/errors';
import { RefundStatus } from '@/domain/payment/refund';
import * as Entities from '@/test/helpers/creators';

import { RefundMustBePendingRule } from '.';

describe('RefundMustBePendingRule', () => {
  it('should succeed if refund status is pending and fail otherwise', () => {
    expect(
      new RefundMustBePendingRule(Entities.refund(1).create()).isBroken()
    ).toBe(false);
    expect(
      new RefundMustBePendingRule(Entities.refund(1).withProps({
        status: 'other' as RefundStatus,
      }).create()).isBroken()
    ).toBe(true);
  });

  it('should return an RefundNotPendingError to indicate the refund status is not pending', () => {
    const rule = new RefundMustBePendingRule(Entities.refund(1).create());
    expect(rule.getError()).toBeInstanceOf(RefundNotPendingError);
  });
});
