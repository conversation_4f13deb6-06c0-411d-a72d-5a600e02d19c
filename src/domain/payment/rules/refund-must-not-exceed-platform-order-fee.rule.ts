import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { RefundPlatformOrderFeeExceededError } from '../errors';
import { Payment } from '../payment';
import { RefundStatus } from '../refund';

export class RefundMustNotExceedPlatformOrderFeeRule implements IBusinessRule {
  constructor(
    private payment: Payment,
    private refundPlatformOrderFee: Money,
  ) {}

  getError() {
    return new RefundPlatformOrderFeeExceededError();
  }

  isBroken(): boolean {
    const { platformOrderFeeRefunded, platformOrderFee } = this.payment.getProps();

    const pendingRefundsPlatformOrderFeeTotal = this.calculatePendingRefundsPlatformOrderFeeTotal();

    const remainingAmount = platformOrderFee
      .sub(platformOrderFeeRefunded)
      .sub(pendingRefundsPlatformOrderFeeTotal);

    return this.refundPlatformOrderFee.greaterThan(remainingAmount);
  }

  private calculatePendingRefundsPlatformOrderFeeTotal() {
    const { refunds, amount: paymentAmount } = this.payment.getProps();

    const pendingRefunds = refunds.filter(
      (refund) => refund.getProps().status === RefundStatus.PENDING,
    );

    return pendingRefunds.reduce(
      (total, refund) => total.add(refund.getProps().platformOrderFeeRefunded),
      Money.zeroFrom(paymentAmount),
    );
  }
}
