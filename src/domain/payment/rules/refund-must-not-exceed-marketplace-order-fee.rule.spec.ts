import { RefundMarketplaceOrderFeeExceededError } from '@/domain/payment/errors';
import * as Entities from '@/test/helpers/creators';

import { RefundMustNotExceedMarketplaceOrderFeeRule } from '.';

describe('RefundMustNotExceedMarketplaceOrderFeeRule', () => {
  it('should succeed if refund amount is less than or equal to remaining marketplace order fee and fail otherwise', () => {
    const payment = Entities.payment(0)
      .marketplaceOrderFee(100)
      .marketplaceOrderFeeRefunded(30)
      .refunds([
        Entities.refund(0).marketplaceOrderFeeRefunded(20).create(),
        Entities.refund(0).marketplaceOrderFeeRefunded(30).create(),
      ])
      .create();

    // 100 - 30 - 20 - 30 = 20
    expect(
      new RefundMustNotExceedMarketplaceOrderFeeRule(payment, Entities.money(20)).isBroken()
    ).toBe(false);
    expect(
      new RefundMustNotExceedMarketplaceOrderFeeRule(payment, Entities.money(20.1)).isBroken()
    ).toBe(true);
  });

  it('should ignore refunds with status other than pending', () => {
    const payment = Entities.payment(0)
      .marketplaceOrderFee(100)
      .marketplaceOrderFeeRefunded(0)
      .refunds([
        Entities.refund(0).marketplaceOrderFeeRefunded(60).create(),
        Entities.refund(0).marketplaceOrderFeeRefunded(10).failed().create(),
        Entities.refund(0).marketplaceOrderFeeRefunded(10).canceled().create(),
        Entities.refund(0).marketplaceOrderFeeRefunded(10).issued().create(),
      ])
      .create();

    // 100 - 60 = 40 (ignoring non-pending refunds)
    expect(
      new RefundMustNotExceedMarketplaceOrderFeeRule(payment, Entities.money(40)).isBroken()
    ).toBe(false);
    expect(
      new RefundMustNotExceedMarketplaceOrderFeeRule(payment, Entities.money(40.1)).isBroken()
    ).toBe(true);
  });

  it('should return an RefundMarketplaceOrderFeeExceededError to indicate the refund amount exceeded the remaining marketplace order fee', () => {
    const rule = new RefundMustNotExceedMarketplaceOrderFeeRule(
      Entities.payment(0).create(), Entities.money(0)
    );
    expect(rule.getError()).toBeInstanceOf(RefundMarketplaceOrderFeeExceededError);
  });
});
