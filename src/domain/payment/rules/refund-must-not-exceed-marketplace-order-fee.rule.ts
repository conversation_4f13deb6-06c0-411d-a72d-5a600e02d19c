import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { RefundMarketplaceOrderFeeExceededError } from '../errors';
import { Payment } from '../payment';
import { RefundStatus } from '../refund';

export class RefundMustNotExceedMarketplaceOrderFeeRule implements IBusinessRule {
  constructor(
    private payment: Payment,
    private refundMarketplaceOrderFee: Money,
  ) {}

  getError() {
    return new RefundMarketplaceOrderFeeExceededError();
  }

  isBroken(): boolean {
    const { marketplaceOrderFeeRefunded, marketplaceOrderFee } = this.payment.getProps();

    const pendingRefundsMarketplaceOrderFeeTotal =
      this.calculatePendingRefundMarketplaceOrderFeeTotal();

    const remainingAmount = marketplaceOrderFee
      .sub(marketplaceOrderFeeRefunded)
      .sub(pendingRefundsMarketplaceOrderFeeTotal);

    return this.refundMarketplaceOrderFee.greaterThan(remainingAmount);
  }

  private calculatePendingRefundMarketplaceOrderFeeTotal() {
    const { refunds, amount: paymentAmount } = this.payment.getProps();

    const pendingRefunds = refunds.filter(
      (refund) => refund.getProps().status === RefundStatus.PENDING,
    );

    return pendingRefunds.reduce(
      (total, refund) => total.add(refund.getProps().marketplaceOrderFeeRefunded),
      Money.zeroFrom(paymentAmount),
    );
  }
}
