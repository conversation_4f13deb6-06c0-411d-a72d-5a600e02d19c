import { TotalRefundSplitsMismatchError } from '@/domain/payment/errors';
import { RefundSplitData } from '@/domain/payment/refund-split-data';
import * as Entities from '@/test/helpers/creators';

import { TotalRefundSplitsEqualsRefundNetAmountRule } from '.';

describe('TotalRefundSplitsEqualsRefundNetAmountRule', () => {
  it('should succeed if refund splits total equals refund net amount and fail otherwise', () => {
    const splitsData: RefundSplitData[] = [
      { accountId: 'account-id-1', amount: Entities.money(10) },
      { accountId: 'account-id-2', amount: Entities.money(20) },
    ];

    expect(
      new TotalRefundSplitsEqualsRefundNetAmountRule(Entities.money(30), splitsData).isBroken()
    ).toBe(false);
    expect(
      new TotalRefundSplitsEqualsRefundNetAmountRule(Entities.money(30.1), splitsData).isBroken()
    ).toBe(true);
  });

  it('should return an TotalRefundSplitsMismatchError to indicate the refund splits total does not equal the refund net amount', () => {
    const rule = new TotalRefundSplitsEqualsRefundNetAmountRule(Entities.money(0), []);
    expect(rule.getError()).toBeInstanceOf(TotalRefundSplitsMismatchError);
  });
});
