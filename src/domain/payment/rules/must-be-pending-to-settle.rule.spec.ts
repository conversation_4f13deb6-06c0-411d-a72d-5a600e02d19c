import { InvalidSettlementStatusError } from '@/domain/payment/errors';
import { PaymentStatus } from '@/domain/payment/payment';
import * as Entities from '@/test/helpers/creators';

import { MustBePendingToSettleRule } from '.';

describe('MustBePendingToSettleRule', () => {
  it('should succeed if payment status is pending and fail otherwise', () => {
    expect(
      new MustBePendingToSettleRule(Entities.payment(1).create()).isBroken()
    ).toBe(false);
    expect(
      new MustBePendingToSettleRule(Entities.payment(1).withProps({
        status: 'other' as PaymentStatus,
      }).create()).isBroken()
    ).toBe(true);
  });

  it('should return an InvalidSettlementStatusError to indicate the payment status is not pending', () => {
    const rule = new MustBePendingToSettleRule(Entities.payment(1).create());
    expect(rule.getError()).toBeInstanceOf(InvalidSettlementStatusError);
  });
});
