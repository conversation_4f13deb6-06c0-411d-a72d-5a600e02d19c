import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { TotalRefundSplitsMismatchError } from '../errors';
import { RefundSplitData } from '../refund-split-data';

export class TotalRefundSplitsEqualsRefundNetAmountRule implements IBusinessRule {
  constructor(
    private refundNetAmount: Money,
    private refundSplitsData: RefundSplitData[],
  ) {}

  getError() {
    return new TotalRefundSplitsMismatchError();
  }

  isBroken(): boolean {
    const total = this.refundSplitsData.reduce(
      (accumulate: Money, split: RefundSplitData) => accumulate.add(split.amount),
      Money.zeroFrom(this.refundNetAmount),
    );

    return !total.equals(this.refundNetAmount);
  }
}
