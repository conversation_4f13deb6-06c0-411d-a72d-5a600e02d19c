import { RefundSplitsEmptyError } from '@/domain/payment/errors';

import { RefundSplitsNotEmptyRule } from '.';

describe('RefundSplitsNotEmptyRule', () => {
  it('should succeed if refund splits count is greater than 0 and fail otherwise', () => {
    expect(new RefundSplitsNotEmptyRule(1).isBroken()).toBe(false);
    expect(new RefundSplitsNotEmptyRule(0).isBroken()).toBe(true);
  });

  it('should return an RefundSplitsEmptyError to indicate the refund splits count is 0', () => {
    const rule = new RefundSplitsNotEmptyRule(0);
    expect(rule.getError()).toBeInstanceOf(RefundSplitsEmptyError);
  });
});
