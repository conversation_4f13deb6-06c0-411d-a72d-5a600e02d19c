import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { RefundNotPendingError } from '../errors';
import { Refund, RefundStatus } from '../refund';

export class RefundMustBePendingRule implements IBusinessRule {
  constructor(private readonly refund: Refund) {}

  getError(): DomainError {
    return new RefundNotPendingError();
  }

  isBroken(): boolean {
    return this.refund.getProps().status !== RefundStatus.PENDING;
  }
}
