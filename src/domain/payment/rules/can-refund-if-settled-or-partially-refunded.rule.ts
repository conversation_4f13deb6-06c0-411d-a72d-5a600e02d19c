import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { InvalidRefundStatusError } from '../errors';
import { PaymentStatus } from '../payment';

export class CanRefundIfSettledOrPartiallyRefundedRule implements IBusinessRule {
  constructor(private readonly status: PaymentStatus) {}

  getError(): DomainError {
    return new InvalidRefundStatusError();
  }

  isBroken(): boolean {
    return (
      this.status !== PaymentStatus.SETTLED && this.status !== PaymentStatus.PARTIALLY_REFUNDED
    );
  }
}
