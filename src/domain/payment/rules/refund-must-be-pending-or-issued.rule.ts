import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { RefundNotPendingOrIssuedError } from '../errors';
import { RefundStatus } from '../refund';

export class RefundMustBePendingOrIssuedRule implements IBusinessRule {
  constructor(private readonly refundStatus: RefundStatus) {}

  getError(): DomainError {
    return new RefundNotPendingOrIssuedError();
  }

  isBroken(): boolean {
    return this.refundStatus !== RefundStatus.PENDING && this.refundStatus !== RefundStatus.ISSUED;
  }
}
