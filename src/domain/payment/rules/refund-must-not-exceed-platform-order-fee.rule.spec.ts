import { RefundPlatformOrderFeeExceededError } from '@/domain/payment/errors';
import * as Entities from '@/test/helpers/creators';

import { RefundMustNotExceedPlatformOrderFeeRule } from '.';

describe('RefundMustNotExceedPlatformOrderFeeRule', () => {
  it('should succeed if refund amount is less than or equal to remaining platform order fee and fail otherwise', () => {
    const payment = Entities.payment(0)
      .platformOrderFee(100)
      .platformOrderFeeRefunded(30)
      .refunds([
        Entities.refund(0).platformOrderFeeRefunded(20).create(),
        Entities.refund(0).platformOrderFeeRefunded(30).create(),
      ])
      .create();

    // 100 - 30 - 20 - 30 = 20
    expect(
      new RefundMustNotExceedPlatformOrderFeeRule(payment, Entities.money(20)).isBroken()
    ).toBe(false);
    expect(
      new RefundMustNotExceedPlatformOrderFeeRule(payment, Entities.money(20.1)).isBroken()
    ).toBe(true);
  });

  it('should ignore refunds with status other than pending', () => {
    const payment = Entities.payment(0)
      .platformOrderFee(100)
      .platformOrderFeeRefunded(0)
      .refunds([
        Entities.refund(0).platformOrderFeeRefunded(60).create(),
        Entities.refund(0).platformOrderFeeRefunded(10).failed().create(),
        Entities.refund(0).platformOrderFeeRefunded(10).canceled().create(),
        Entities.refund(0).platformOrderFeeRefunded(10).issued().create(),
      ])
      .create();

    // 100 - 60 = 40 (ignoring non-pending refunds)
    expect(
      new RefundMustNotExceedPlatformOrderFeeRule(payment, Entities.money(40)).isBroken()
    ).toBe(false);
    expect(
      new RefundMustNotExceedPlatformOrderFeeRule(payment, Entities.money(40.1)).isBroken()
    ).toBe(true);
  });

  it('should return an RefundPlatformOrderFeeExceededError to indicate the refund amount exceeded the remaining platform order fee', () => {
    const rule = new RefundMustNotExceedPlatformOrderFeeRule(
      Entities.payment(0).create(), Entities.money(0)
    );
    expect(rule.getError()).toBeInstanceOf(RefundPlatformOrderFeeExceededError);
  });
});
