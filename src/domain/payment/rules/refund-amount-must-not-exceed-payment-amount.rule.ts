import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { RefundAmountExceededRemainingAmountError } from '../errors';
import { Payment } from '../payment';
import { RefundStatus } from '../refund';

export class RefundAmountMustNotExceedPaymentAmountRule implements IBusinessRule {
  constructor(
    private payment: Payment,
    private refundAmount: Money,
  ) {}

  getError() {
    return new RefundAmountExceededRemainingAmountError();
  }

  isBroken(): boolean {
    const { amount: paymentAmount, refundedAmount } = this.payment.getProps();

    const pendingRefundsTotal = this.calculatePendingRefundTotal();

    const remainingAmount = paymentAmount.sub(refundedAmount).sub(pendingRefundsTotal);

    return this.refundAmount.greaterThan(remainingAmount);
  }

  private calculatePendingRefundTotal() {
    const { refunds, amount: paymentAmount } = this.payment.getProps();

    const pendingRefunds = refunds.filter(
      (refund) => refund.getProps().status === RefundStatus.PENDING,
    );

    return pendingRefunds.reduce(
      (total, refund) => total.add(refund.getProps().amount),
      Money.zeroFrom(paymentAmount),
    );
  }
}
