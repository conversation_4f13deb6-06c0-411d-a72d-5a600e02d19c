import { RefundNotPendingError } from '@/domain/payment/errors';
import { RefundStatus } from '@/domain/payment/refund';
import { RefundMustBePendingRule } from '@/domain/payment/rules/refund-must-be-pending.rule';
import * as Entities from '@/test/helpers/creators';

import { RefundMustBePendingOrIssuedRule } from '.';

describe('RefundMustBePendingOrIssuedRule', () => {
  it('should succeed if refund status is pending or issued and fail otherwise', () => {
    expect(new RefundMustBePendingOrIssuedRule(RefundStatus.PENDING).isBroken()).toBe(false);
    expect(new RefundMustBePendingOrIssuedRule(RefundStatus.ISSUED).isBroken()).toBe(false);
    expect(new RefundMustBePendingOrIssuedRule('other' as RefundStatus).isBroken()).toBe(true);
  });

  it('should return an RefundNotPendingError to indicate the refund status is not pending or issued', () => {
    const rule = new RefundMustBePendingRule(Entities.refund(1).create());
    expect(rule.getError()).toBeInstanceOf(RefundNotPendingError);
  });
});
