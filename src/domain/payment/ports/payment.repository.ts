import { IRepository } from '@/core/ddd';

import { Payment } from '../payment';

export interface PaymentRepositoryPort extends IRepository<Payment> {
  findByPaymentIntentId(paymentIntentId: string): Promise<Payment | null>;

  findByGatewayRefundId(gatewayRefundId: string): Promise<Payment | null>;

  findByRefundId(refundId: string): Promise<Payment | null>;

  findByPaymentSplitId(paymentSplitId: string): Promise<Payment | null>;
}
