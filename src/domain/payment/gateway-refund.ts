import { Entity, idFactory } from '@/core/ddd';

import { Gateway, Money } from '../shared';

type EntityProps = {
  idAtGateway: string;
  gateway: Gateway;
  amount: Money;
  idempotencyKey: string;
};

export class GatewayRefund extends Entity<EntityProps> {
  static create(gatewayRefund: EntityProps) {
    return new GatewayRefund({
      id: idFactory(),
      props: gatewayRefund,
    });
  }
}
