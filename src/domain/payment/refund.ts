import { Entity, idFactory } from '@/core/ddd';

import { GatewayRefund } from './gateway-refund';
import { GatewayMetadata, Money } from '../shared';

export enum RefundStatus {
  ISSUED = 'issued',
  CANCELED = 'canceled',
  FAILED = 'failed',
  PENDING = 'pending',
}

export enum RefundType {
  PARTIAL = 'partial',
  FULL = 'full',
}

type EntityProps = {
  amount: Money;
  status: RefundStatus;
  type: RefundType;
  gatewayRefund: GatewayRefund;
  isManual: boolean;
  metadata: GatewayMetadata;
  platformPaymentFeeRefunded: Money;
  providerPaymentFeeRefunded: Money;
  marketplacePaymentFeeRefunded: Money;
  marketplaceOrderFeeRefunded: Money;
  platformOrderFeeRefunded: Money;
};

export class Refund extends Entity<EntityProps> {
  static create(refund: Omit<EntityProps, 'status'>) {
    return new Refund({
      id: idFactory(),
      props: {
        ...refund,
        status: RefundStatus.PENDING,
      },
    });
  }

  markIssued() {
    this.props.status = RefundStatus.ISSUED;
  }

  markFailed() {
    this.props.status = RefundStatus.FAILED;
  }

  markCanceled() {
    this.props.status = RefundStatus.CANCELED;
  }

  isIssued() {
    return this.props.status === RefundStatus.ISSUED;
  }
}
