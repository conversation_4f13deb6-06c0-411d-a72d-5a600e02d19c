import { AggregateRoot, checkRules, idFactory } from '@/core/ddd';

import { RefundNotFoundError } from './errors/refund-not-found.error';
import {
  PaymentCreatedEvent,
  PaymentSettledEvent,
  RefundCanceledEvent,
  RefundCreatedEvent,
  RefundFailedEvent,
  RefundIssuedEvent,
} from './events';
import { GatewayRefund } from './gateway-refund';
import { Refund, RefundStatus, RefundType } from './refund';
import {
  MustBePendingToSettleRule,
  RefundAmountMustNotExceedPaymentAmountRule,
  RefundMustBePendingOrIssuedRule,
  RefundMustBePendingRule,
  CanRefundIfSettledOrPartiallyRefundedRule,
  RefundMustNotExceedPlatformOrderFeeRule,
  RefundMustNotExceedMarketplaceOrderFeeRule,
} from './rules';
import {
  Gateway,
  GatewayMetadata,
  Money,
  MoneyCurrencyMustMatchRule,
  PaymentMethodType,
} from '../shared';

export enum PaymentStatus {
  SETTLED = 'settled',
  FAILED = 'failed',
  PENDING = 'pending',
  CANCELED = 'canceled',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially-refunded',
}

type EntityProps = {
  marketplaceId: string;
  customerId: string;
  gateway: Gateway;
  amount: Money;
  refundedAmount: Money;
  paymentIntentId: string;
  status: PaymentStatus;
  paymentMethodType: PaymentMethodType;
  refunds: Refund[];
  marketplacePaymentFeeFixed: Money;
  marketplacePaymentFeePercentage: number;
  marketplacePaymentFee: Money;
  platformPaymentFeeFixed: Money;
  platformPaymentFeePercentage: number;
  platformPaymentFee: Money;
  providerFeeFixed: Money;
  providerFeePercentage: number;
  providerFee: Money;
  marketplaceOrderFee: Money;
  platformOrderFee: Money;
  platformPaymentFeeRefunded: Money;
  providerPaymentFeeRefunded: Money;
  marketplacePaymentFeeRefunded: Money;
  marketplaceOrderFeeRefunded: Money;
  platformOrderFeeRefunded: Money;
  paymentMethodId: string;
  feeSettingsId: string | null;
};

type CreatePaymentProps = Omit<
  EntityProps,
  | 'status'
  | 'refundedAmount'
  | 'refunds'
  | 'platformPaymentFeeRefunded'
  | 'providerPaymentFeeRefunded'
  | 'marketplacePaymentFeeRefunded'
  | 'marketplaceOrderFeeRefunded'
  | 'platformOrderFeeRefunded'
>;
export class Payment extends AggregateRoot<EntityProps> {
  static create(data: CreatePaymentProps) {
    const paymentId = idFactory();

    const zeroAmount = new Money(0, data.amount.getCurrency());

    const payment = new Payment({
      id: paymentId,
      props: {
        ...data,
        refunds: [],
        status: PaymentStatus.PENDING,
        refundedAmount: new Money(0, data.amount.getCurrency()),
        platformOrderFeeRefunded: zeroAmount,
        platformPaymentFeeRefunded: zeroAmount,
        providerPaymentFeeRefunded: zeroAmount,
        marketplaceOrderFeeRefunded: zeroAmount,
        marketplacePaymentFeeRefunded: zeroAmount,
      },
    });

    payment.addEvent(new PaymentCreatedEvent({ aggregateId: payment.getId() }));

    return payment;
  }

  markSettled() {
    checkRules(new MustBePendingToSettleRule(this));

    this.props.status = PaymentStatus.SETTLED;

    this.addEvent(
      new PaymentSettledEvent({
        aggregateId: this.getId(),
        amount: this.props.amount,
      }),
    );
  }

  createRefund(params: {
    refundAmount: Money;
    gatewayRefund: GatewayRefund;
    isManual: boolean;
    metadata?: GatewayMetadata;
    marketplaceOrderFee: Money;
    platformOrderFee: Money;
  }) {
    this.checkCanMakeRefund(params);

    const { amount: paymentAmount } = this.props;

    const refund = Refund.create({
      ...this.calculateFeesToRefund(params.refundAmount),
      amount: params.refundAmount,
      type: paymentAmount.equals(params.refundAmount) ? RefundType.FULL : RefundType.PARTIAL,
      gatewayRefund: params.gatewayRefund,
      isManual: params.isManual,
      metadata: params.metadata,
      marketplaceOrderFeeRefunded: params.marketplaceOrderFee,
      platformOrderFeeRefunded: params.platformOrderFee,
    });

    this.props.refunds.push(refund);

    this.addEvent(
      new RefundCreatedEvent({
        aggregateId: this.getId(),
        refundId: refund.getId(),
        amount: refund.getProps().amount,
      }),
    );

    return refund.getId();
  }

  calculateFeesToRefund(refundAmount: Money) {
    const remainingFees = {
      platformPaymentFee: this.props.platformPaymentFee.sub(this.props.platformPaymentFeeRefunded),
      marketplacePaymentFee: this.props.marketplacePaymentFee.sub(
        this.props.marketplacePaymentFeeRefunded,
      ),
      providerFee: this.props.providerFee.sub(this.props.providerPaymentFeeRefunded),
    };

    const amountAfterRefund = this.props.amount.sub(this.props.refundedAmount).sub(refundAmount);
    const feesAfterRefund = this.recalculateFees(amountAfterRefund);

    return {
      marketplacePaymentFeeRefunded: remainingFees.marketplacePaymentFee.sub(
        feesAfterRefund.marketplacePaymentFee,
      ),
      platformPaymentFeeRefunded: remainingFees.platformPaymentFee.sub(
        feesAfterRefund.platformPaymentFee,
      ),
      providerPaymentFeeRefunded: remainingFees.providerFee.sub(feesAfterRefund.providerFee),
    };
  }

  recalculateFees(amount: Money) {
    return {
      platformPaymentFee: this.calculatePaymentFee(
        amount,
        this.props.platformPaymentFeeFixed,
        this.props.platformPaymentFeePercentage,
      ),
      marketplacePaymentFee: this.calculatePaymentFee(
        amount,
        this.props.marketplacePaymentFeeFixed,
        this.props.marketplacePaymentFeePercentage,
      ),
      providerFee: this.calculatePaymentFee(
        amount,
        this.props.providerFeeFixed,
        this.props.providerFeePercentage,
      ),
    };
  }

  private calculatePaymentFee(amount: Money, feeFixed: Money, feePercentage: number) {
    if (amount.isZero()) {
      return Money.zeroFrom(amount);
    }

    return amount.mul(feePercentage).add(feeFixed).round();
  }

  checkCanMakeRefund(params: {
    refundAmount: Money;
    marketplaceOrderFee: Money;
    platformOrderFee: Money;
  }) {
    checkRules(
      new CanRefundIfSettledOrPartiallyRefundedRule(this.props.status),
      new MoneyCurrencyMustMatchRule(this.props.amount, params.refundAmount),
      new RefundAmountMustNotExceedPaymentAmountRule(this, params.refundAmount),
      new RefundMustNotExceedMarketplaceOrderFeeRule(this, params.marketplaceOrderFee),
      new RefundMustNotExceedPlatformOrderFeeRule(this, params.platformOrderFee),
    );
  }

  markRefundIssued(refundId: string) {
    const refund = this.getRefund(refundId);

    checkRules(new RefundMustBePendingRule(refund));

    refund.markIssued();

    this.updateRefundedAmountAfterRefund(refund);
    this.updateRefundedFeesAfterRefund(refund);

    this.props.status = this.props.amount.equals(this.props.refundedAmount)
      ? PaymentStatus.REFUNDED
      : PaymentStatus.PARTIALLY_REFUNDED;

    this.addEvent(
      new RefundIssuedEvent({
        aggregateId: this.getId(),
        refundId: refund.getId(),
        refundAmount: refund.getProps().amount,
      }),
    );
  }

  markRefundFailed(refundId: string) {
    const refund = this.getRefund(refundId);

    checkRules(new RefundMustBePendingOrIssuedRule(refund.getProps().status));

    if (refund.getProps().status === RefundStatus.ISSUED) {
      this.props.refundedAmount = this.props.refundedAmount.sub(refund.getProps().amount);

      this.props.status =
        this.props.refundedAmount.toNumber() === 0
          ? PaymentStatus.SETTLED
          : PaymentStatus.PARTIALLY_REFUNDED;
    }

    refund.markFailed();

    this.addEvent(
      new RefundFailedEvent({
        aggregateId: this.getId(),
        refundId: refund.getId(),
      }),
    );
  }

  markRefundCanceled(refundId: string) {
    const refund = this.getRefund(refundId);

    checkRules(new RefundMustBePendingRule(refund)); // TODO: STRIPE ALLOWS CANCELING AFTER ISSUING REFUND

    refund.markCanceled();

    this.addEvent(
      new RefundCanceledEvent({
        aggregateId: this.getId(),
        refundId: refund.getId(),
      }),
    );
  }

  private updateRefundedAmountAfterRefund(refund: Refund) {
    const { refundedAmount } = this.getProps();

    this.props.refundedAmount = refundedAmount.add(refund.getProps().amount);
  }

  private updateRefundedFeesAfterRefund(refund: Refund) {
    const {
      marketplaceOrderFeeRefunded,
      platformOrderFeeRefunded,
      marketplacePaymentFeeRefunded,
      providerPaymentFeeRefunded,
      platformPaymentFeeRefunded,
    } = this.getProps();

    this.props.marketplaceOrderFeeRefunded = marketplaceOrderFeeRefunded.add(
      refund.getProps().marketplaceOrderFeeRefunded,
    );

    this.props.platformOrderFeeRefunded = platformOrderFeeRefunded.add(
      refund.getProps().platformOrderFeeRefunded,
    );

    this.props.marketplacePaymentFeeRefunded = marketplacePaymentFeeRefunded.add(
      refund.getProps().marketplacePaymentFeeRefunded,
    );

    this.props.providerPaymentFeeRefunded = providerPaymentFeeRefunded.add(
      refund.getProps().providerPaymentFeeRefunded,
    );

    this.props.platformPaymentFeeRefunded = platformPaymentFeeRefunded.add(
      refund.getProps().platformPaymentFeeRefunded,
    );
  }

  public getRefund(refundId: string) {
    const refund = this.props.refunds.find((x) => x.getProps().id === refundId);

    if (!refund) {
      throw new RefundNotFoundError();
    }

    return refund;
  }
}
