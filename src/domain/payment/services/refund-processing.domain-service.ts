import { Injectable } from '@nestjs/common';

import { Debt, DebtSource, DebtType } from '@/domain/debt/debt';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import {
  PaymentSplitRefund,
  PaymentSplitRefundStatus,
} from '@/domain/payment-split/payment-split-refund';

@Injectable()
export class RefundProcessingDomainService {
  processRefundFailure(paymentSplits: PaymentSplit[], refundId: string) {
    const debts: Debt[] = [];

    paymentSplits.forEach((paymentSplit) => {
      this.cancelPendingPaymentSplitRefunds(paymentSplit, refundId);
      this.createDebtsFromIssuedPaymentSplitRefunds(paymentSplit, refundId, debts);
    });

    return {
      debts,
      paymentSplits,
    };
  }

  private cancelPendingPaymentSplitRefunds(paymentSplit: PaymentSplit, refundId: string) {
    const pendingRefunds = this.getPaymentSplitRefundsByStatus(
      paymentSplit,
      refundId,
      PaymentSplitRefundStatus.PENDING,
    );
    pendingRefunds.forEach((refund) => paymentSplit.markRefundCanceled(refund.getId()));
  }

  private createDebtsFromIssuedPaymentSplitRefunds(
    paymentSplit: PaymentSplit,
    refundId: string,
    debts: Debt[],
  ) {
    const issuedRefunds = this.getPaymentSplitRefundsByStatus(
      paymentSplit,
      refundId,
      PaymentSplitRefundStatus.ISSUED,
    );
    issuedRefunds.forEach((refundSplit) => {
      const debt = this.createDebtFromPaymentSplitRefund(paymentSplit, refundSplit);
      debts.push(debt);
    });
  }

  private getPaymentSplitRefundsByStatus(
    paymentSplit: PaymentSplit,
    refundId: string,
    status: PaymentSplitRefundStatus,
  ): PaymentSplitRefund[] {
    const { refunds } = paymentSplit.getProps();
    return refunds.filter(
      (refund) => refund.getProps().refundId === refundId && refund.getProps().status === status,
    );
  }

  private createDebtFromPaymentSplitRefund(
    paymentSplit: PaymentSplit,
    refundSplit: PaymentSplitRefund,
  ): Debt {
    const { amount, accountId } = paymentSplit.getProps();
    const { id } = refundSplit.getProps();

    return Debt.create({
      amount,
      accountId,
      type: DebtType.PLATFORM_TO_ACCOUNT,
      source: DebtSource.REFUND_SPLIT,
      sourceId: id,
    });
  }
}
