import { Inject, Injectable } from '@nestjs/common';

import { checkRules, idFactory } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { GATEWAY_SERVICE_FACTORY_TOKEN } from '@/domain/di-tokens';
import { PaymentSplitNotFoundError } from '@/domain/payment-split/errors';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { GatewayMetadata, GatewayServiceFactoryPort, Money } from '@/domain/shared';

import { GatewayRefund } from '../gateway-refund';
import { Payment } from '../payment';
import { RefundStatus } from '../refund';
import { RefundSplitData } from '../refund-split-data';
import {
  TotalRefundSplitsEqualsRefundNetAmountRule,
  TotalRefundSplitsMustNotExceedRefundAmountRule,
} from '../rules';

export type CreateRefundParams = {
  refundAmount: Money;
  paymentSplits: PaymentSplit[];
  payment: Payment;
  refundSplits: RefundSplitData[];
  metadata?: GatewayMetadata;
  marketplaceOrderFee: Money;
  platformOrderFee: Money;
};

export type CreateManualRefundParams = CreateRefundParams & {
  gatewayRefundId: string;
  status: RefundStatus;
};

@Injectable()
export class CreateRefundDomainService {
  constructor(
    @Inject(GATEWAY_SERVICE_FACTORY_TOKEN)
    private readonly gatewayServiceFactory: GatewayServiceFactoryPort,
  ) {}

  async createRefund(params: CreateRefundParams) {
    const { payment } = params;

    this.checkRefundCreationRules({ ...params, isManual: false });

    const gatewayService = this.gatewayServiceFactory.getGateway(payment.getProps().gateway);

    const gatewayRefund = await gatewayService.createRefund({
      payment,
      amount: params.refundAmount,
      idempotencyKey: this.generateIdempotencyKey(), // todo: make consistent idempotency key (this generates random each time)
      metadata: params.metadata,
    });

    const refundId = payment.createRefund({
      refundAmount: params.refundAmount,
      gatewayRefund,
      isManual: false,
      metadata: params.metadata,
      platformOrderFee: params.platformOrderFee,
      marketplaceOrderFee: params.marketplaceOrderFee,
    });

    params.refundSplits.forEach((refundSplit) => {
      const paymentSplit = this.getPaymentSplit(params.paymentSplits, refundSplit.accountId);
      paymentSplit.createRefund({
        refundId,
        refundAmount: refundSplit.amount,
        isManualRefund: false,
      });
    });

    return payment.getProps().refunds.find((r) => r.getId() === refundId);
  }

  createManualRefund(params: CreateManualRefundParams) {
    const { payment } = params;

    this.checkRefundCreationRules({ ...params, isManual: true });

    const gatewayRefund = GatewayRefund.create({
      idAtGateway: params.gatewayRefundId,
      amount: params.refundAmount,
      gateway: params.payment.getProps().gateway,
      idempotencyKey: '', // TODO: ALLOW NULL
    });

    const refundId = payment.createRefund({
      refundAmount: params.refundAmount,
      gatewayRefund,
      isManual: true,
      metadata: params.metadata,
      platformOrderFee: params.platformOrderFee,
      marketplaceOrderFee: params.marketplaceOrderFee,
    });

    params.refundSplits.forEach((refundSplit) => {
      const paymentSplit = this.getPaymentSplit(params.paymentSplits, refundSplit.accountId);
      paymentSplit.createRefund({
        refundAmount: refundSplit.amount,
        isManualRefund: true,
      });
    });

    return payment.getProps().refunds.find((r) => r.getId() === refundId);
  }

  private checkRefundCreationRules(params: CreateRefundParams & { isManual: boolean }) {
    const { payment } = params;

    payment.checkCanMakeRefund(params);

    this.checkRefundSplitsRules({
      ...params,
      refundNetAmount: this.calculateRefundNetAmount(payment, params),
    });

    this.checkPaymentSplitRules(params);
  }

  private calculateRefundNetAmount(payment: Payment, params: CreateRefundParams) {
    const feesToRefund = payment.calculateFeesToRefund(params.refundAmount);

    return params.refundAmount
      .sub(feesToRefund.marketplacePaymentFeeRefunded)
      .sub(params.marketplaceOrderFee)
      .sub(params.platformOrderFee)
      .round();
  }

  private checkRefundSplitsRules({
    refundNetAmount,
    isManual,
    refundSplits,
  }: Pick<CreateRefundParams, 'refundSplits'> & {
    isManual: boolean;
    refundNetAmount: Money;
  }) {
    const rules: IBusinessRule[] = [];

    if (!isManual) {
      rules.push(
        new TotalRefundSplitsMustNotExceedRefundAmountRule(refundNetAmount, refundSplits),
        new TotalRefundSplitsEqualsRefundNetAmountRule(refundNetAmount, refundSplits),
      );
    }

    checkRules(...rules);
  }

  private checkPaymentSplitRules({
    refundSplits,
    paymentSplits,
  }: Pick<CreateRefundParams, 'refundSplits' | 'paymentSplits'>) {
    refundSplits.forEach((refundSplit) => {
      const paymentSplit = this.getPaymentSplit(paymentSplits, refundSplit.accountId);
      paymentSplit.checkCanMakeRefund(refundSplit.amount);
    });
  }

  getPaymentSplit(paymentSplits: PaymentSplit[], accountId: string) {
    const paymentSplit = paymentSplits.find((x) => x.getProps().accountId === accountId);

    if (!paymentSplit) {
      throw new PaymentSplitNotFoundError(`No payment split by account id ${accountId}`);
    }

    return paymentSplit;
  }

  private generateIdempotencyKey() {
    return idFactory();
  }
}
