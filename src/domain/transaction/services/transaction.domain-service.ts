import { Injectable } from '@nestjs/common';

import { Money } from '@/domain/shared';
import { Transaction, TransactionStatus, TransactionType } from '@/domain/transaction/transaction';

export type CreateTransactionProps = {
  amount: Money;
  type?: TransactionType;
  paymentId?: string;
  accountId?: string;
  refundId?: string;
  paymentSplitId?: string;
  paymentSplitRefundId?: string;
  disputeId?: string;
}

@Injectable()
export class TransactionDomainService {
  createTransaction(props: CreateTransactionProps): Transaction {
    return Transaction.create({
      ...props,
      amount: props.amount,
      type: props.type,
      status: TransactionStatus.PENDING
    });
  }

  createDisputeTransaction(props: CreateTransactionProps): Transaction {
    return this.createTransaction({
      ...props,
      amount: this.negateAmount(props.amount),
      type: TransactionType.DISPUTE_WITHDRAWN,
      disputeId: props.disputeId,
    });
  }

  createDisputeFeeTransaction(props: CreateTransactionProps): Transaction {
    return this.createTransaction({
      ...props,
      amount: this.negateAmount(props.amount),
      type: TransactionType.DISPUTE_FEE,
      disputeId: props.disputeId,
    });
  }

  private negateAmount(amount: Money): Money {
    return amount.isNegative() ? amount : amount.negated();
  }
}
