import { AggregateRoot, idFactory } from '@/core/ddd';
import {
  TransactionCreatedDomainEvent,
  TransactionUpdatedDomainEvent,
} from '@/domain/transaction/events';

import { Money } from '../shared';

export enum TransactionType {
  PAYMENT = 'payment',
  REFUND = 'refund',
  PAYMENT_SPLIT = 'payment-split',
  PAYMENT_SPLIT_REFUND = 'payment-split-refund',
  DISPUTE_WITHDRAWN = 'dispute-withdrawn',
  DISPUTE_FEE = 'dispute-fee',
}

export enum TransactionStatus {
  PENDING = 'pending',
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
}

export type EntityProps = {
  amount: Money;
  type: TransactionType;
  status: TransactionStatus;
  paymentId?: string;
  accountId?: string;
  refundId?: string;
  paymentSplitId?: string;
  paymentSplitRefundId?: string;
  disputeId?: string;
};

export class Transaction extends AggregateRoot<EntityProps> {
  static create(props: EntityProps) {
    const transaction = new Transaction({
      id: idFactory(),
      props,
    });

    transaction.addEvent(new TransactionCreatedDomainEvent({ aggregateId: transaction.getId() }));

    return transaction;
  }

  update(data: Partial<EntityProps>) {
    this.props.status = data.status;

    this.addEvent(new TransactionUpdatedDomainEvent({ aggregateId: this.getId() }));

    return this;
  }
}
