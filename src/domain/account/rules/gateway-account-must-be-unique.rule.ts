import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { Account } from '../account';
import { DuplicateGatewayAccountError } from '../errors';
import { GatewayAccount } from '../gateway-account';

export class GatewayAccountMustBeUniqueRule implements IBusinessRule {
  constructor(
    private readonly account: Account,
    private readonly gatewayAccount: GatewayAccount,
  ) {}

  getError(): DomainError {
    return new DuplicateGatewayAccountError();
  }

  isBroken(): boolean {
    return this.account
      .getProps()
      .gatewayAccounts.some((x) =>
        x.getProps().gateway.equals(this.gatewayAccount.getProps().gateway),
      );
  }
}
