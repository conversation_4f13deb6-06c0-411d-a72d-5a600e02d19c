import { AggregateRoot, checkRules, idFactory } from '@/core/ddd';

import { AccountCreatedDomainEvent, GatewayAccountLinkedDomainEvent } from './events';
import { GatewayAccount } from './gateway-account';
import { GatewayAccountMustBeUniqueRule } from './rules';
import { Currency, Gateway, Money } from '../shared';

export type EntityProps = {
  marketplaceId: string;
  name: string | null;
  email: string | null;
  balance: Money;
  debt: Money;
  gatewayAccounts: GatewayAccount[];
};

type CreateEntityProps = Pick<EntityProps, 'marketplaceId' | 'name' | 'email'> & {
  currency: Currency;
};

export class Account extends AggregateRoot<EntityProps> {
  static create(props: CreateEntityProps) {
    const account = new Account({
      id: idFactory(),
      props: {
        marketplaceId: props.marketplaceId,
        name: props.name,
        email: props.email,
        gatewayAccounts: [],
        debt: new Money(0, props.currency),
        balance: new Money(0, props.currency),
      },
    });

    account.addEvent(new AccountCreatedDomainEvent({ aggregateId: account.getId() }));

    return account;
  }

  linkGatewayAccount(idAtGateway: string, gateway: Gateway) {
    const gatewayAccount = GatewayAccount.create({
      idAtGateway,
      gateway,
    });

    checkRules(new GatewayAccountMustBeUniqueRule(this, gatewayAccount));

    this.props.gatewayAccounts.push(gatewayAccount);

    this.addEvent(
      new GatewayAccountLinkedDomainEvent({
        aggregateId: this.getId(),
        gatewayAccountId: gatewayAccount.getId(),
      }),
    );

    return gatewayAccount.getId();
  }
}
