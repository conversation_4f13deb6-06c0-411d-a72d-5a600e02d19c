import { Entity, idFactory } from '@/core/ddd';

import { Money } from '../shared';

export enum PaymentSplitRefundStatus {
  ISSUED = 'issued',
  PROCESSING = 'processing',
  CANCELED = 'canceled',
  FAILED = 'failed',
  PENDING = 'pending',
}

type EntityProps = {
  refundId: string;
  amount: Money;
  status: PaymentSplitRefundStatus;
  isManual: boolean;
  disputeId?: string;
};

export class PaymentSplitRefund extends Entity<EntityProps> {
  static create(props: Omit<EntityProps, 'status'>) {
    return new PaymentSplitRefund({
      id: idFactory(),
      props: {
        ...props,
        status: PaymentSplitRefundStatus.PENDING,
      },
    });
  }

  markIssued() {
    this.props.status = PaymentSplitRefundStatus.ISSUED;
  }

  markProcessing() {
    this.props.status = PaymentSplitRefundStatus.PROCESSING;
  }

  markCanceled() {
    this.props.status = PaymentSplitRefundStatus.CANCELED;
  }

  markFailed() {
    this.props.status = PaymentSplitRefundStatus.FAILED;
  }

  isIssued() {
    return this.props.status === PaymentSplitRefundStatus.ISSUED;
  }
}
