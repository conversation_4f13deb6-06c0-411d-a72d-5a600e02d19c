import { AggregateRoot, checkRules, idFactory } from '@/core/ddd';

import { PaymentSplitRefundNotFoundError } from './errors';
import {
  PaymentSplitCreatedEvent,
  PaymentSplitProcessingEvent,
  PaymentSplitRefundCanceledEvent,
  PaymentSplitRefundCreatedEvent,
  PaymentSplitRefundFailedEvent,
  PaymentSplitRefundIssuedEvent,
  PaymentSplitRefundProcessingEvent,
  PaymentSplitSettledEvent,
} from './events';
import { PaymentSplitRefund } from './payment-split-refund';
import {
  CanRefundPaymentSplitIfSettledOrPartiallyRefundedRule,
  PaymentSplitMustBePendingOrProcessingRule,
  PaymentSplitMustBePendingRule,
  PaymentSplitRefundAmountMustNotExceedRemainingAmountRule,
  PaymentSplitRefundMustBePendingOrProcessingRule,
  PaymentSplitRefundMustBePendingRule,
} from './rules';
import { Gateway, Money } from '../shared';

export enum PaymentSplitStatus {
  SETTLED = 'settled',
  CANCELED = 'canceled',
  FAILED = 'failed',
  PENDING = 'pending',
  PROCESSING = 'processing',
  PARTIALLY_REFUNDED = 'partially-refunded',
  REFUNDED = 'refunded',
}

type EntityProps = {
  accountId: string;
  paymentId: string;
  amount: Money;
  refundedAmount: Money;
  gateway: Gateway;
  status: PaymentSplitStatus;
  refunds: PaymentSplitRefund[];
};

interface BaseRefundParams {
  refundAmount: Money;
  isManualRefund: boolean;
}

interface ManualPaymentSplitRefundParams extends BaseRefundParams {
  isManualRefund: true;
}

interface DisputeRelatedAutomaticPaymentSplitRefundParams extends BaseRefundParams {
  disputeId?: string;
}

interface RefundRelatedAutomaticPaymentSplitRefundParams extends BaseRefundParams {
  refundId: string;
}

type AutomaticPaymentSplitRefundParams =
  RefundRelatedAutomaticPaymentSplitRefundParams
| DisputeRelatedAutomaticPaymentSplitRefundParams;

type CreateRefundParams =
  | ManualPaymentSplitRefundParams
  | AutomaticPaymentSplitRefundParams;

export class PaymentSplit extends AggregateRoot<EntityProps> {
  static create(props: Omit<EntityProps, 'status' | 'refundedAmount' | 'refunds'>) {
    const paymentSplit = new PaymentSplit({
      id: idFactory(),
      props: {
        ...props,
        status: PaymentSplitStatus.PENDING,
        refundedAmount: Money.zeroFrom(props.amount),
        refunds: [],
      },
    });

    paymentSplit.addEvent(
      new PaymentSplitCreatedEvent({
        aggregateId: paymentSplit.getId(),
      }),
    );

    return paymentSplit;
  }

  createRefund(params: CreateRefundParams) {
    this.checkCanMakeRefund(params.refundAmount);

    let refundId: string | null = null;
    let disputeId: string | null = null;

    if (this.isRefundRelatedAutomaticPaymentSplitRefundParams(params)) {
      refundId = params.refundId;
    }

    if (this.isDisputeRelatedAutomaticPaymentSplitRefundParams(params)) {
      disputeId = params.disputeId;
    }

    const paymentSplitRefund = PaymentSplitRefund.create({
      refundId,
      disputeId,
      amount: params.refundAmount,
      isManual: params.isManualRefund,
    });

    this.props.refunds.push(paymentSplitRefund);

    this.addEvent(
      new PaymentSplitRefundCreatedEvent({
        aggregateId: this.getId(),
        paymentSplitRefundId: paymentSplitRefund.getId(),
        amount: paymentSplitRefund.getProps().amount,
      }),
    );

    return paymentSplitRefund.getId();
  }

  checkCanMakeRefund(amountToRefund: Money) {
    checkRules(
      new CanRefundPaymentSplitIfSettledOrPartiallyRefundedRule(this.props.status),
      new PaymentSplitRefundAmountMustNotExceedRemainingAmountRule(this, amountToRefund),
    );
  }

  markSettled() {
    checkRules(new PaymentSplitMustBePendingOrProcessingRule(this.props.status));

    this.props.status = PaymentSplitStatus.SETTLED;

    this.addEvent(
      new PaymentSplitSettledEvent({
        aggregateId: this.getId(),
      }),
    );
  }

  markProcessing() {
    checkRules(new PaymentSplitMustBePendingRule(this.props.status));

    this.props.status = PaymentSplitStatus.PROCESSING;

    this.addEvent(
      new PaymentSplitProcessingEvent({
        aggregateId: this.getId(),
      }),
    );
  }

  isSettled() {
    return this.props.status === PaymentSplitStatus.SETTLED;
  }

  markRefundIssued(paymentSplitRefundId: string) {
    const refund = this.getPaymentSplitRefund(paymentSplitRefundId);

    checkRules(new PaymentSplitRefundMustBePendingOrProcessingRule(refund.getProps().status));

    refund.markIssued();

    this.props.refundedAmount = this.props.refundedAmount.add(refund.getProps().amount);

    const remainingAmount = this.calculateRemainingAmount();

    const status = remainingAmount.isZero()
      ? PaymentSplitStatus.REFUNDED
      : PaymentSplitStatus.PARTIALLY_REFUNDED;

    this.props.status = status;

    this.addEvent(
      new PaymentSplitRefundIssuedEvent({
        refundId: refund.getProps().refundId,
        paymentSplitRefundId: refund.getId(),
        aggregateId: this.getId(),
      }),
    );
  }

  calculateRemainingAmount() {
    const { amount, refundedAmount } = this.props;

    return amount.sub(refundedAmount);
  }

  markRefundFailed(paymentSplitRefundId: string) {
    const refund = this.getPaymentSplitRefund(paymentSplitRefundId);

    checkRules(new PaymentSplitRefundMustBePendingOrProcessingRule(refund.getProps().status));

    refund.markFailed();

    this.addEvent(
      new PaymentSplitRefundFailedEvent({
        refundId: refund.getProps().refundId,
        paymentSplitRefundId: refund.getId(),
        aggregateId: this.getId(),
      }),
    );
  }

  markRefundProcessing(paymentSplitRefundId: string) {
    const refund = this.getPaymentSplitRefund(paymentSplitRefundId);

    checkRules(new PaymentSplitRefundMustBePendingRule(refund.getProps().status));

    refund.markProcessing();

    this.addEvent(
      new PaymentSplitRefundProcessingEvent({
        refundId: refund.getProps().refundId,
        paymentSplitRefundId: refund.getId(),
        aggregateId: this.getId(),
      }),
    );
  }

  markRefundCanceled(paymentSplitRefundId: string) {
    const refund = this.getPaymentSplitRefund(paymentSplitRefundId);

    checkRules(new PaymentSplitRefundMustBePendingRule(refund.getProps().status));

    refund.markCanceled();

    this.addEvent(
      new PaymentSplitRefundCanceledEvent({
        refundId: refund.getProps().refundId,
        paymentSplitRefundId: refund.getId(),
        aggregateId: this.getId(),
      }),
    );
  }

  getPaymentSplitRefund(paymentSplitRefundId: string) {
    const paymentSplitRefund = this.props.refunds.find((x) => x.getId() === paymentSplitRefundId);

    if (!paymentSplitRefund) {
      throw new PaymentSplitRefundNotFoundError();
    }

    return paymentSplitRefund;
  }

  private isRefundRelatedAutomaticPaymentSplitRefundParams(
    params: CreateRefundParams,
  ): params is RefundRelatedAutomaticPaymentSplitRefundParams {
    return (params as RefundRelatedAutomaticPaymentSplitRefundParams).refundId !== undefined;
  }

  private isDisputeRelatedAutomaticPaymentSplitRefundParams(
    params: CreateRefundParams,
  ): params is DisputeRelatedAutomaticPaymentSplitRefundParams {
    return (params as DisputeRelatedAutomaticPaymentSplitRefundParams).disputeId !== undefined;
  }
}
