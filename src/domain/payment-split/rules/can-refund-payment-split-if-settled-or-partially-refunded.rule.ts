import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { InvalidPaymentSplitRefundStatusError } from '../errors';
import { PaymentSplitStatus } from '../payment-split';

export class CanRefundPaymentSplitIfSettledOrPartiallyRefundedRule implements IBusinessRule {
  constructor(private readonly status: PaymentSplitStatus) {}

  getError(): DomainError {
    return new InvalidPaymentSplitRefundStatusError();
  }

  isBroken(): boolean {
    return (
      this.status !== PaymentSplitStatus.SETTLED &&
      this.status !== PaymentSplitStatus.PARTIALLY_REFUNDED
    );
  }
}
