import { PaymentSplitNotPendingError } from '@/domain/payment-split/errors';
import { PaymentSplitStatus } from '@/domain/payment-split/payment-split';

import { PaymentSplitMustBePendingRule } from '.';

describe('PaymentSplitMustBePendingRule', () => {
  it('should succeed if payment split status is pending and fail otherwise', () => {
    expect(
      new PaymentSplitMustBePendingRule(PaymentSplitStatus.PENDING).isBroken()
    ).toBe(false);
    expect(
      new PaymentSplitMustBePendingRule('other' as PaymentSplitStatus).isBroken()
    ).toBe(true);
  });

  it('should return an PaymentSplitNotPendingError to indicate the payment split status is not pending', () => {
    const rule = new PaymentSplitMustBePendingRule(PaymentSplitStatus.PENDING);
    expect(rule.getError()).toBeInstanceOf(PaymentSplitNotPendingError);
  });
});
