import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { PaymentSplitRefundNotFoundError } from '../errors';
import { PaymentSplitRefundStatus } from '../payment-split-refund';

export class PaymentSplitRefundMustBePendingOrProcessingRule implements IBusinessRule {
  constructor(private readonly status: PaymentSplitRefundStatus) {}
  getError() {
    return new PaymentSplitRefundNotFoundError();
  }

  isBroken(): boolean {
    return (
      this.status !== PaymentSplitRefundStatus.PENDING &&
      this.status !== PaymentSplitRefundStatus.PROCESSING
    );
  }
}
