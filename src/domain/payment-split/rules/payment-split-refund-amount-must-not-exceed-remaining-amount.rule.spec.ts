import { PaymentSplitRefundAmountExceededRemainingAmountError } from '@/domain/payment-split/errors';
import * as Entities from '@/test/helpers/creators';

import { PaymentSplitRefundAmountMustNotExceedRemainingAmountRule } from '.';

describe('PaymentSplitRefundAmountMustNotExceedRemainingAmountRule', () => {
  it('should succeed if refund amount is within the remaining amount and fail otherwise', () => {
    const paymentSplit = Entities.paymentSplit(100).refundedAmount(30).refunds([
      Entities.paymentSplitRefund(10).create(),
      Entities.paymentSplitRefund(20).create(),
    ]).create();

    // 100 - 30 - 10 - 20 = 40
    const rule = new PaymentSplitRefundAmountMustNotExceedRemainingAmountRule(
      paymentSplit, Entities.money(40)
    );
    expect(rule.isBroken()).toBe(false);

    const rule2 = new PaymentSplitRefundAmountMustNotExceedRemainingAmountRule(
      paymentSplit, Entities.money(40.1)
    );
    expect(rule2.isBroken()).toBe(true);
  });

  it('should ignore payment split refunds with status other than pending', () => {
    const paymentSplit = Entities.paymentSplit(100).refundedAmount(0).refunds([
      Entities.paymentSplitRefund(10).create(),
      Entities.paymentSplitRefund(20).create(),
      Entities.paymentSplitRefund(30).issued().create(),
      Entities.paymentSplitRefund(30).processing().create(),
      Entities.paymentSplitRefund(30).canceled().create(),
      Entities.paymentSplitRefund(30).failed().create(),
    ]).create();

    // 100 - 10 - 20 = 70 (ignoring non-pending refunds)
    const rule = new PaymentSplitRefundAmountMustNotExceedRemainingAmountRule(
      paymentSplit, Entities.money(70)
    );
    expect(rule.isBroken()).toBe(false);

    const rule2 = new PaymentSplitRefundAmountMustNotExceedRemainingAmountRule(
      paymentSplit, Entities.money(70.1)
    );
    expect(rule2.isBroken()).toBe(true);
  });

  it('should return an PaymentSplitRefundAmountExceededRemainingAmountError to indicate the refund amount exceeded the remaining amount', () => {
    const paymentSplit = Entities.paymentSplit(0).create();
    const refundAmount = Entities.money(0);
    const rule = new PaymentSplitRefundAmountMustNotExceedRemainingAmountRule(paymentSplit, refundAmount);

    expect(rule.getError()).toBeInstanceOf(PaymentSplitRefundAmountExceededRemainingAmountError);
  });
});
