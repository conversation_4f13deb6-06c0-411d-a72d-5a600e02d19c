import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { PaymentSplitRefundAmountExceededRemainingAmountError } from '../errors';
import { PaymentSplit } from '../payment-split';
import { PaymentSplitRefundStatus } from '../payment-split-refund';

export class PaymentSplitRefundAmountMustNotExceedRemainingAmountRule implements IBusinessRule {
  constructor(
    private paymentSplit: PaymentSplit,
    private refundAmount: Money,
  ) {}

  getError() {
    return new PaymentSplitRefundAmountExceededRemainingAmountError();
  }

  isBroken(): boolean {
    const availableRefundableAmount = this.calculateAvailableRefundableAmount();

    return this.refundAmount.greaterThan(availableRefundableAmount);
  }

  private calculateAvailableRefundableAmount() {
    const pendingRefundsTotal = this.calculatePendingRefundTotal();

    const remainingAmount = this.paymentSplit.calculateRemainingAmount();

    return remainingAmount.sub(pendingRefundsTotal);
  }

  private calculatePendingRefundTotal() {
    const { refunds, amount } = this.paymentSplit.getProps();

    const pendingRefunds = refunds.filter(
      (refund) => refund.getProps().status === PaymentSplitRefundStatus.PENDING,
    );

    return pendingRefunds.reduce(
      (total, refund) => total.add(refund.getProps().amount),
      Money.zeroFrom(amount),
    );
  }
}
