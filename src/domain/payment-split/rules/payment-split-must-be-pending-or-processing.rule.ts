import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { PaymentSplitNotPendingOrProcessingError } from '../errors';
import { PaymentSplitStatus } from '../payment-split';

export class PaymentSplitMustBePendingOrProcessingRule implements IBusinessRule {
  constructor(private readonly paymentSplitStatus: PaymentSplitStatus) {}

  getError(): DomainError {
    return new PaymentSplitNotPendingOrProcessingError();
  }

  isBroken(): boolean {
    return (
      this.paymentSplitStatus !== PaymentSplitStatus.PENDING &&
      this.paymentSplitStatus !== PaymentSplitStatus.PROCESSING
    );
  }
}
