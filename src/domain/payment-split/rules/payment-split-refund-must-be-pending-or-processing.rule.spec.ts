import { PaymentSplitRefundNotFoundError } from '@/domain/payment-split/errors';
import { PaymentSplitRefundStatus } from '@/domain/payment-split/payment-split-refund';

import { PaymentSplitRefundMustBePendingOrProcessingRule } from '.';

describe('PaymentSplitRefundMustBePendingOrProcessingRule', () => {
  it('should succeed if payment split refund status is pending or processing and fail otherwise', () => {
    expect(
      new PaymentSplitRefundMustBePendingOrProcessingRule(PaymentSplitRefundStatus.PENDING).isBroken()
    ).toBe(false);
    expect(
      new PaymentSplitRefundMustBePendingOrProcessingRule(PaymentSplitRefundStatus.PROCESSING).isBroken()
    ).toBe(false);
    expect(
      new PaymentSplitRefundMustBePendingOrProcessingRule('other' as PaymentSplitRefundStatus).isBroken()
    ).toBe(true);
  });

  it('should return an PaymentSplitRefundNotFoundError to indicate the payment split refund status is not pending or processing', () => {
    const rule = new PaymentSplitRefundMustBePendingOrProcessingRule(PaymentSplitRefundStatus.PENDING);
    expect(rule.getError()).toBeInstanceOf(PaymentSplitRefundNotFoundError);
  });
});
