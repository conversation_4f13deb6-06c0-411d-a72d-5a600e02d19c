import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { PaymentSplitNotPendingError } from '../errors';
import { PaymentSplitStatus } from '../payment-split';

export class PaymentSplitMustBePendingRule implements IBusinessRule {
  constructor(private readonly paymentSplitStatus: PaymentSplitStatus) {}

  getError(): DomainError {
    return new PaymentSplitNotPendingError();
  }

  isBroken(): boolean {
    return this.paymentSplitStatus !== PaymentSplitStatus.PENDING;
  }
}
