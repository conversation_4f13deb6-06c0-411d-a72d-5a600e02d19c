import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { PaymentSplitNotPendingError } from '../errors';
import { PaymentSplitRefundStatus } from '../payment-split-refund';

export class PaymentSplitRefundMustBePendingRule implements IBusinessRule {
  constructor(private readonly status: PaymentSplitRefundStatus) {}
  getError() {
    return new PaymentSplitNotPendingError();
  }

  isBroken(): boolean {
    return this.status !== PaymentSplitRefundStatus.PENDING;
  }
}
