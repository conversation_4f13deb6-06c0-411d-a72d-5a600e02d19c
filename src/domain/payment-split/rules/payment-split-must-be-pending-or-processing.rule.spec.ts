import { PaymentSplitNotPendingOrProcessingError } from '@/domain/payment-split/errors';
import { PaymentSplitStatus } from '@/domain/payment-split/payment-split';

import { PaymentSplitMustBePendingOrProcessingRule } from '.';

describe('PaymentSplitMustBePendingOrProcessingRule', () => {
  it('should succeed if payment split status is pending or processing and fail otherwise', () => {
    expect(
      new PaymentSplitMustBePendingOrProcessingRule(PaymentSplitStatus.PENDING).isBroken()
    ).toBe(false);
    expect(
      new PaymentSplitMustBePendingOrProcessingRule(PaymentSplitStatus.PROCESSING).isBroken()
    ).toBe(false);
    expect(
      new PaymentSplitMustBePendingOrProcessingRule('other' as PaymentSplitStatus).isBroken()
    ).toBe(true);
  });

  it('should return an PaymentSplitNotPendingOrProcessingError to indicate the payment split status is not pending or processing', () => {
    const rule = new PaymentSplitMustBePendingOrProcessingRule(PaymentSplitStatus.PENDING);
    expect(rule.getError()).toBeInstanceOf(PaymentSplitNotPendingOrProcessingError);
  });
});
