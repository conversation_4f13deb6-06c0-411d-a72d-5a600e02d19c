import { InvalidPaymentSplitRefundStatusError } from '@/domain/payment-split/errors';
import { PaymentSplitStatus } from '@/domain/payment-split/payment-split';

import { CanRefundPaymentSplitIfSettledOrPartiallyRefundedRule } from '.';

describe('CanRefundPaymentSplitIfSettledOrPartiallyRefundedRule', () => {
  it('should succeed if payment split status is settled or partially refunded and fail otherwise', () => {
    expect(
      new CanRefundPaymentSplitIfSettledOrPartiallyRefundedRule(PaymentSplitStatus.SETTLED).isBroken()
    ).toBe(false);
    expect(
      new CanRefundPaymentSplitIfSettledOrPartiallyRefundedRule(PaymentSplitStatus.PARTIALLY_REFUNDED).isBroken()
    ).toBe(false);
    expect(
      new CanRefundPaymentSplitIfSettledOrPartiallyRefundedRule('other' as PaymentSplitStatus).isBroken()
    ).toBe(true);
  });

  it('should return an InvalidPaymentSplitRefundStatusError to indicate the payment split status is not settled or partially refunded', () => {
    const rule = new CanRefundPaymentSplitIfSettledOrPartiallyRefundedRule(PaymentSplitStatus.CANCELED);
    expect(rule.getError()).toBeInstanceOf(InvalidPaymentSplitRefundStatusError);
  });
});
