import { PaymentSplitNotPendingError } from '@/domain/payment-split/errors';
import { PaymentSplitRefundStatus } from '@/domain/payment-split/payment-split-refund';

import { PaymentSplitRefundMustBePendingRule } from '.';

describe('PaymentSplitRefundMustBePendingRule', () => {
  it('should succeed if payment split refund status is pending and fail otherwise', () => {
    expect(
      new PaymentSplitRefundMustBePendingRule(PaymentSplitRefundStatus.PENDING).isBroken()
    ).toBe(false);
    expect(
      new PaymentSplitRefundMustBePendingRule('other' as PaymentSplitRefundStatus).isBroken()
    ).toBe(true);
  });

  it('should return an PaymentSplitNotPendingError to indicate the payment split refund status is not pending', () => {
    const rule = new PaymentSplitRefundMustBePendingRule(PaymentSplitRefundStatus.PENDING);
    expect(rule.getError()).toBeInstanceOf(PaymentSplitNotPendingError);
  });
});
