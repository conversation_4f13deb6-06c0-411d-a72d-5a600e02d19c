import { AggregateRoot, idFactory } from '@/core/ddd';

import { PaymentMethodCreatedDomainEvent } from './events';
import { GatewayPaymentMethod } from './gateway-payment-method';
import { PaymentMethodType } from '../shared';

export type EntityProps = {
  type: PaymentMethodType;
  cardLast4: string | null;
  cardExpiryYear: number | null;
  cardExpiryMonth: number | null;
  gatewayPaymentMethod: GatewayPaymentMethod;
};

export class PaymentMethod extends AggregateRoot<EntityProps> {
  static create(props: EntityProps) {
    const account = new PaymentMethod({
      id: idFactory(),
      props,
    });

    account.addEvent(new PaymentMethodCreatedDomainEvent({ aggregateId: account.getId() }));

    return account;
  }
}
