import { TotalPaymentSplitsMismatchError } from '@/domain/payment-intent/errors';
import { PaymentSplit } from '@/domain/payment-intent/payment-split.value-object';
import * as Entities from '@/test/helpers/creators';

import { TotalPaymentSplitsEqualsNetAmountRule } from '.';

describe('TotalPaymentSplitsEqualsNetAmountRule', () => {
  it('should succeed if the total payment splits equals the net amount and fail otherwise', () => {
    const rule = new TotalPaymentSplitsEqualsNetAmountRule(
      [
        new PaymentSplit({ accountId: 'account-id-0', amount: Entities.money(10), }),
        new PaymentSplit({ accountId: 'account-id-1', amount: Entities.money(20), }),
        new PaymentSplit({ accountId: 'account-id-2', amount: Entities.money(30), }),
      ],
      Entities.money(90),
      Entities.money(10),
      Entities.money(10),
      Entities.money(10)
    );
    expect(rule.isBroken()).toBe(false);

    const rule2 = new TotalPaymentSplitsEqualsNetAmountRule(
      [
        new PaymentSplit({ accountId: 'account-id-0', amount: Entities.money(10) }),
        new PaymentSplit({ accountId: 'account-id-1', amount: Entities.money(20) }),
        new PaymentSplit({ accountId: 'account-id-2', amount: Entities.money(30) }),
      ],
      Entities.money(60),
      Entities.money(10),
      Entities.money(10),
      Entities.money(20)
    );
    expect(rule2.isBroken()).toBe(true);
  });

  it('should return an TotalPaymentSplitsMismatchError to indicate the total payment splits does not equal the net amount', () => {
    const rule = new TotalPaymentSplitsEqualsNetAmountRule(
      [],
      Entities.money(0),
      Entities.money(0),
      Entities.money(0),
      Entities.money(0)
    );
    expect(rule.getError()).toBeInstanceOf(TotalPaymentSplitsMismatchError);
  });
});
