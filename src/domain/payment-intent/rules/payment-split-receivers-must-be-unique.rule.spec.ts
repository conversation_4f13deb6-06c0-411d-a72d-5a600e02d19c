import { PaymentSplitReceiversNotUniqueError } from '@/domain/payment-intent/errors';
import { PaymentSplit } from '@/domain/payment-intent/payment-split.value-object';
import * as Entities from '@/test/helpers/creators';

import { PaymentSplitReceiversMustBeUniqueRule } from '.';

describe('PaymentSplitReceiversMustBeUniqueRule', () => {
  it('should succeed if the payment split receivers are unique and fail otherwise', () => {
    const rule = new PaymentSplitReceiversMustBeUniqueRule([
      new PaymentSplit({ accountId: 'account-id-0', amount: Entities.money(0) }),
      new PaymentSplit({ accountId: 'account-id-1', amount: Entities.money(0) }),
      new PaymentSplit({ accountId: 'account-id-2', amount: Entities.money(0) }),
    ]);
    expect(rule.isBroken()).toBe(false);

    const rule2 = new PaymentSplitReceiversMustBeUniqueRule([
      new PaymentSplit({ accountId: 'account-id-0', amount: Entities.money(0) }),
      new PaymentSplit({ accountId: 'account-id-1', amount: Entities.money(0) }),
      new PaymentSplit({ accountId: 'account-id-0', amount: Entities.money(0) }),
    ]);
    expect(rule2.isBroken()).toBe(true);
  });

  it('should return an PaymentSplitReceiversNotUniqueError to indicate the payment split receivers are not unique', () => {
    const rule = new PaymentSplitReceiversMustBeUniqueRule([]);
    expect(rule.getError()).toBeInstanceOf(PaymentSplitReceiversNotUniqueError);
  });
});
