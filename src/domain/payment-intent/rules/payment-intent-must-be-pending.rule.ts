import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { PaymentIntentNotPendingError } from '../errors';
import { PaymentIntentStatus } from '../payment-intent';

export class PaymentIntentMustBePendingRule implements IBusinessRule {
  constructor(private readonly status: PaymentIntentStatus) {}

  getError(): DomainError {
    return new PaymentIntentNotPendingError();
  }

  isBroken(): boolean {
    return this.status !== PaymentIntentStatus.PENDING;
  }
}
