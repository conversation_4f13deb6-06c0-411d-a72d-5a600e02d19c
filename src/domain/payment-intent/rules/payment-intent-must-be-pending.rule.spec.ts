import { PaymentIntentNotPendingError } from '@/domain/payment-intent/errors';
import { PaymentIntentStatus } from '@/domain/payment-intent/payment-intent';

import { PaymentIntentMustBePendingRule } from '.';

describe('PaymentIntentMustBePendingRule', () => {
  it('should succeed if the payment intent status is pending and fail otherwise', () => {
    expect(
      new PaymentIntentMustBePendingRule(PaymentIntentStatus.PENDING).isBroken()
    ).toBe(false);
    expect(
      new PaymentIntentMustBePendingRule('other' as PaymentIntentStatus).isBroken()
    ).toBe(true);
  });

  it('should return an PaymentIntentNotPendingError to indicate the payment intent is not pending', () => {
    const rule = new PaymentIntentMustBePendingRule(PaymentIntentStatus.PENDING);
    expect(rule.getError()).toBeInstanceOf(PaymentIntentNotPendingError);
  });
});
