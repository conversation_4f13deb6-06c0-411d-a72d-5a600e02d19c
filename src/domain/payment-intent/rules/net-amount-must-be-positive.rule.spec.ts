import { NetAmountNotPositiveError } from '@/domain/payment-intent/errors';
import * as Entities from '@/test/helpers/creators';

import { NetAmountMustBePositiveRule } from '.';

describe('NetAmountMustBePositiveRule', () => {
  it('should succeed if the net amount is positive and fail otherwise', () => {
    const rule = new NetAmountMustBePositiveRule(
      Entities.money(100),
      Entities.money(10),
      Entities.money(10),
      Entities.money(10)
    );
    expect(rule.isBroken()).toBe(false); // 70

    const rule2 = new NetAmountMustBePositiveRule(
      Entities.money(100),
      Entities.money(10),
      Entities.money(10),
      Entities.money(200)
    );
    expect(rule2.isBroken()).toBe(true); // -120

    const rule3 = new NetAmountMustBePositiveRule(
      Entities.money(100),
      Entities.money(10),
      Entities.money(10),
      Entities.money(80)
    );
    expect(rule3.isBroken()).toBe(true); // 0
  });

  it('should return an NetAmountNotPositiveError to indicate the net amount is not positive', () => {
    const rule = new NetAmountMustBePositiveRule(
      Entities.money(0),
      Entities.money(0),
      Entities.money(0),
      Entities.money(0)
    );
    expect(rule.getError()).toBeInstanceOf(NetAmountNotPositiveError);
  });
});
