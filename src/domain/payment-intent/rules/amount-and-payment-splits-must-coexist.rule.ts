import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { AmountAndPaymentSplitsRequiredError } from '../errors';
import { PaymentSplit } from '../payment-split.value-object';

export class AmountAndPaymentSplitsMustCoexistRule implements IBusinessRule {
  constructor(
    private readonly amount?: Money,
    private readonly paymentSplits?: PaymentSplit[],
  ) {}

  getError(): DomainError {
    return new AmountAndPaymentSplitsRequiredError(
      `Both 'amount' and 'paymentSplits' must be provided together, or neither should be passed`,
    );
  }

  isBroken(): boolean {
    return (!!this.amount && !this.paymentSplits) || (!this.amount && !!this.paymentSplits);
  }
}
