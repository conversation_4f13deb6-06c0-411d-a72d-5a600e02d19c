import { uniqBy } from 'lodash';

import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { PaymentSplitReceiversNotUniqueError } from '../errors';
import { PaymentSplit } from '../payment-split.value-object';

export class PaymentSplitReceiversMustBeUniqueRule implements IBusinessRule {
  constructor(private readonly paymentSplitsData: PaymentSplit[]) {}

  getError(): DomainError {
    return new PaymentSplitReceiversNotUniqueError();
  }

  isBroken(): boolean {
    const uniqueSplits = uniqBy(this.paymentSplitsData, (splitData) => splitData.getAccountId());

    return uniqueSplits.length !== this.paymentSplitsData.length;
  }
}
