import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { NetAmountNotPositiveError } from '../errors';

export class NetAmountMustBePositiveRule implements IBusinessRule {
  constructor(
    private readonly intentAmount: Money,
    private readonly marketplacePaymentFee: Money,
    private readonly marketplaceOrderFee: Money,
    private readonly platformOrderFee: Money,
  ) {}

  getError(): DomainError {
    return new NetAmountNotPositiveError();
  }

  isBroken(): boolean {
    const netAmount = this.calculateNetAmount();

    return netAmount.isNegative() || netAmount.isZero();
  }

  calculateNetAmount() {
    return this.intentAmount
      .sub(this.marketplacePaymentFee)
      .sub(this.marketplaceOrderFee)
      .sub(this.platformOrderFee);
  }
}
