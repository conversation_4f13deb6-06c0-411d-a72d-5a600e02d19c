import { AmountAndPaymentSplitsRequiredError } from '@/domain/payment-intent/errors';
import * as Entities from '@/test/helpers/creators';

import { AmountAndPaymentSplitsMustCoexistRule } from '.';

describe('AmountAndPaymentSplitsMustCoexistRule', () => {
  it('should succeed if both amount and payment splits are provided or neither is provided and fail otherwise', () => {
    expect(new AmountAndPaymentSplitsMustCoexistRule().isBroken()).toBe(false);
    // TODO: Check for payment splits length
    expect(
      new AmountAndPaymentSplitsMustCoexistRule(Entities.money(1), []).isBroken()
    ).toBe(false);
    expect(
      new AmountAndPaymentSplitsMustCoexistRule(Entities.money(1), undefined).isBroken()
    ).toBe(true);
    expect(
      new AmountAndPaymentSplitsMustCoexistRule(undefined, []).isBroken()
    ).toBe(true);
  });

  it('should return an AmountAndPaymentSplitsRequiredError to indicate that both amount and payment splits must either be provided or neither', () => {
    const rule = new AmountAndPaymentSplitsMustCoexistRule();
    expect(rule.getError()).toBeInstanceOf(AmountAndPaymentSplitsRequiredError);
  });
});
