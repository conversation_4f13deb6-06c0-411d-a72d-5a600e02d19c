import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { TotalPaymentSplitsMismatchError } from '../errors';
import { PaymentSplit } from '../payment-split.value-object';

export class TotalPaymentSplitsEqualsNetAmountRule implements IBusinessRule {
  constructor(
    private readonly paymentSplits: PaymentSplit[],
    private readonly intentAmount: Money,
    private readonly marketplacePaymentFee: Money,
    private readonly marketplaceOrderFee: Money,
    private readonly platformOrderFee: Money,
  ) {}

  getError(): DomainError {
    return new TotalPaymentSplitsMismatchError();
  }

  isBroken(): boolean {
    const paymentIntentNetAmount = this.calculateNetAmount();

    const splitsTotalAmount = this.calculatePaymentSplitsTotalAmount();

    return !splitsTotalAmount.equals(paymentIntentNetAmount);
  }

  calculatePaymentSplitsTotalAmount() {
    return this.paymentSplits.reduce(
      (accumulate: Money, split: PaymentSplit) => accumulate.add(split.getAmount()),
      Money.zeroFrom(this.intentAmount),
    );
  }

  calculateNetAmount(): Money {
    const totalFees = this.marketplacePaymentFee
      .add(this.marketplaceOrderFee)
      .add(this.platformOrderFee);

    return this.intentAmount.sub(totalFees);
  }
}
