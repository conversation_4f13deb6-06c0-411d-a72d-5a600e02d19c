import { Inject, Injectable } from '@nestjs/common';

import { OmitUnion } from '@/core/types';
import { GatewayCustomerService } from '@/domain/customer/services';
import { GATEWAY_SERVICE_FACTORY_TOKEN } from '@/domain/di-tokens';
import { Gateway } from '@/domain/gateway/gateway';
import { Money, GatewayServiceFactoryPort, Gateway as GatewayVO } from '@/domain/shared';

import {
  CreatePaymentIntentProps,
  PaymentIntent,
  PaymentIntentUpdateType,
  UpdatePaymentIntentProps,
} from '../payment-intent';

export type ConfirmPaymentIntentParams = {
  paymentIntent: PaymentIntent;
  paymentMethodId: string;
};

export type UpdatePaymentIntentParams = OmitUnion<
  UpdatePaymentIntentProps,
  'marketplacePaymentFee' | 'platformPaymentFee' | 'providerFee' | 'gateway'
> & {
  gateway: Gateway;
};

export type CreatePaymentIntentParams = Omit<
  CreatePaymentIntentProps,
  | 'marketplacePaymentFee'
  | 'platformPaymentFee'
  | 'providerFee'
  | 'gateway'
  | 'gatewayPaymentIntent'
> & {
  gateway: Gateway;
};
@Injectable()
export class PaymentIntentDomainService {
  constructor(
    @Inject(GATEWAY_SERVICE_FACTORY_TOKEN)
    private readonly gatewayServiceFactory: GatewayServiceFactoryPort,
    private readonly gatewayCustomer: GatewayCustomerService,
  ) {}

  async create(params: CreatePaymentIntentParams) {
    const { gateway, customerId, amount, paymentMethodType, statementDescriptor, metadata } =
      params;

    const gatewayValueObject = new GatewayVO(gateway.getId(), gateway.getProps().type);

    const gatewayService = this.gatewayServiceFactory.getGateway(gatewayValueObject);

    const gatewayCustomer = await this.gatewayCustomer.getGatewayCustomer(
      customerId,
      gatewayValueObject,
    );

    const gatewayPaymentIntent = await gatewayService.createPaymentIntent({
      amount,
      paymentMethodType,
      gatewayCustomerId: gatewayCustomer.getProps().idAtGateway,
      statementDescriptor,
      metadata,
    });

    const paymentIntent = PaymentIntent.create({
      ...params,
      ...this.calculateFees(params),
      gateway: gatewayValueObject,
      gatewayPaymentIntent,
    });

    return paymentIntent;
  }

  calculateFees({
    amount,
    platformPaymentFeeFixed,
    platformPaymentFeePercentage,
    marketplacePaymentFeeFixed,
    marketplacePaymentFeePercentage,
    providerFeeFixed,
    providerFeePercentage,
  }: Pick<
    CreatePaymentIntentProps,
    | 'amount'
    | 'marketplacePaymentFeeFixed'
    | 'marketplacePaymentFeePercentage'
    | 'providerFeeFixed'
    | 'providerFeePercentage'
    | 'platformPaymentFeeFixed'
    | 'platformPaymentFeePercentage'
  >) {
    return {
      platformPaymentFee: this.calculatePaymentFee(
        amount,
        platformPaymentFeeFixed,
        platformPaymentFeePercentage,
      ),
      marketplacePaymentFee: this.calculatePaymentFee(
        amount,
        marketplacePaymentFeeFixed,
        marketplacePaymentFeePercentage,
      ),
      providerFee: this.calculatePaymentFee(amount, providerFeeFixed, providerFeePercentage),
    };
  }

  private calculatePaymentFee(amount: Money, feeFixed: Money, feePercentage: number) {
    return amount.mul(feePercentage).add(feeFixed).round(); // TODO: fix this, we lose fraction of cents because of rounding
  }

  async update(paymentIntent: PaymentIntent, params: UpdatePaymentIntentParams) {
    const updateProps = this.prepareUpdateProps(params);

    paymentIntent.checkUpdateRules(updateProps);

    const { gateway, gatewayPaymentIntent } = paymentIntent.getProps();

    const gatewayService = this.gatewayServiceFactory.getGateway(gateway);

    const updatedGatewayPaymentIntent = await gatewayService.updatePaymentIntent({
      amount: params.updateType === PaymentIntentUpdateType.WITH_AMOUNT ? params.amount : undefined,
      paymentMethodType: params.paymentMethodType,
      gatewayPaymentIntent,
      metadata: params.metadata,
      statementDescriptor: params.statementDescriptor,
    });

    paymentIntent.update({
      ...updateProps,
      gatewayPaymentIntent: updatedGatewayPaymentIntent,
    });

    return paymentIntent;
  }

  private prepareUpdateProps(params: UpdatePaymentIntentParams): UpdatePaymentIntentProps {
    if (params.updateType === PaymentIntentUpdateType.WITHOUT_AMOUNT) {
      return params;
    }

    return {
      ...params,
      ...this.calculateFees(params),
    };
  }

  async confirm({ paymentIntent, paymentMethodId }: ConfirmPaymentIntentParams) {
    const { gateway } = paymentIntent.getProps();

    const gatewayService = this.gatewayServiceFactory.getGateway(gateway);

    const providerFee = await gatewayService.getProviderFee(
      paymentIntent.getProps().gatewayPaymentIntent,
    );

    paymentIntent.markAsSettled(providerFee, paymentMethodId);
  }
}
