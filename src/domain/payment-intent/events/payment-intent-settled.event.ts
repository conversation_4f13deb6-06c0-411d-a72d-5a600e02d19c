import { DomainEvent } from '@sw-web/nestjs-core/domain';

import { Gateway, Money, PaymentMethodType } from '@/domain/shared';

import { PaymentSplit } from '../payment-split.value-object';

type Props = {
  aggregateId: string;
  marketplaceId: string;
  amount: Money;
  customerId: string;
  providerFee: Money;
  gateway: Gateway;
  paymentMethodType: PaymentMethodType;
  paymentSplits: PaymentSplit[];
  paymentMethodId: string;
};

export class PaymentIntentSettledDomainEvent extends DomainEvent<Props> {}
