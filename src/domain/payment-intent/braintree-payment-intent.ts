import { idFactory } from '@/core/ddd';

import {
  GatewayPaymentIntentBase,
  GatewayPaymentIntentBaseEntityProps,
} from './gateway-payment-intent.base';

type EntityProps = GatewayPaymentIntentBaseEntityProps & {
  nonce: string;
};

export class BraintreePaymentIntent extends GatewayPaymentIntentBase<EntityProps> {
  static create(props: EntityProps) {
    return new BraintreePaymentIntent({
      id: idFactory(),
      props,
    });
  }
}
