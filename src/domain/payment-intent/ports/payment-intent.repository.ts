import { IRepository } from '@/core/ddd';
import { Gateway } from '@/domain/shared';

import { PaymentIntent } from '../payment-intent';

export interface PaymentIntentRepositoryPort extends IRepository<PaymentIntent> {
  findByIdAndMarketplaceId(id: string, marketplaceId: string): Promise<PaymentIntent>;
  findByPaymentIdAndMarketplaceId(paymentId: string, marketplaceId: string): Promise<PaymentIntent>;

  /**
   * Find payment intent by external gateway payment intent id (from stripe etc)
   * @param gatewayPaymentIntentId
   * @param getawayType
   */
  findPaymentIntentByGatewayPaymentIntentId(
    gatewayPaymentIntentId: string,
    getawayType: Gateway,
  ): Promise<PaymentIntent>;
}
