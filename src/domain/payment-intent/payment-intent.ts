import { identity, pick, pickBy } from 'lodash';

import { AggregateRoot, checkRules, idFactory } from '@/core/ddd';
import {
  Gateway,
  GatewayMetadata,
  Money,
  MoneyCurrencyMustMatchRule,
  PaymentMethodType,
} from '@/domain/shared';

import { BraintreePaymentIntent } from './braintree-payment-intent';
import {
  PaymentIntentCreatedDomainEvent,
  PaymentIntentExpiredEvent,
  PaymentIntentSettledDomainEvent,
  PaymentIntentUpdatedEvent,
} from './events';
import { PaymentSplit } from './payment-split.value-object';
import {
  NetAmountMustBePositiveRule,
  PaymentIntentMustBePendingRule,
  PaymentSplitReceiversMustBeUniqueRule,
  TotalPaymentSplitsEqualsNetAmountRule,
} from './rules';
import { StripePaymentIntent } from './stripe-payment-intent';

export enum PaymentIntentStatus {
  PENDING = 'pending',
  SETTLED = 'settled',
  FAILED = 'failed',
  EXPIRED = 'expired',
}

export type GatewayPaymentIntent = StripePaymentIntent | BraintreePaymentIntent;

interface EntityProps {
  marketplaceId: string;
  amount: Money;
  status: PaymentIntentStatus;
  paymentMethodType: PaymentMethodType;
  customerId: string;
  gateway: Gateway;
  businessEntityId: string | null;
  paymentSplits: PaymentSplit[];
  statementDescriptor: string | null;
  gatewayPaymentIntent: GatewayPaymentIntent;
  metadata: GatewayMetadata;
  marketplacePaymentFeeFixed: Money;
  marketplacePaymentFeePercentage: number;
  marketplacePaymentFee: Money;
  platformPaymentFeeFixed: Money;
  platformPaymentFeePercentage: number;
  platformPaymentFee: Money;
  providerFeeFixed: Money;
  providerFeePercentage: number;
  providerFee: Money;
  marketplaceOrderFee: Money;
  platformOrderFee: Money;
  feeSettingsId: string | null;
}

export type CreatePaymentIntentProps = Omit<EntityProps, 'status'>;

export type UpdatePaymentIntentProps =
  | UpdatePaymentIntentWithoutAmountProps
  | UpdatePaymentIntentWithAmountProps;

export enum PaymentIntentUpdateType {
  WITH_AMOUNT = 'with-amount',
  WITHOUT_AMOUNT = 'without-amount',
}

type UpdatePaymentIntentCommonProps = Partial<
  Pick<
    CreatePaymentIntentProps,
    | 'paymentMethodType'
    | 'statementDescriptor'
    | 'businessEntityId'
    | 'metadata'
    | 'gatewayPaymentIntent'
  >
> & {
  updateType: PaymentIntentUpdateType;
};

type UpdatePaymentIntentWithoutAmountProps = UpdatePaymentIntentCommonProps & {
  updateType: PaymentIntentUpdateType.WITHOUT_AMOUNT;
};

type UpdatePaymentIntentWithAmountProps = UpdatePaymentIntentCommonProps &
  Pick<
    CreatePaymentIntentProps,
    | 'amount'
    | 'paymentSplits'
    | 'platformPaymentFeeFixed'
    | 'platformPaymentFeePercentage'
    | 'platformPaymentFee'
    | 'marketplacePaymentFeeFixed'
    | 'marketplacePaymentFeePercentage'
    | 'marketplacePaymentFee'
    | 'providerFeeFixed'
    | 'providerFeePercentage'
    | 'providerFee'
    | 'marketplaceOrderFee'
    | 'platformOrderFee'
  > & {
    updateType: PaymentIntentUpdateType.WITH_AMOUNT;
  };

const ALWAYS_UPDATEABLE_FIELDS: (keyof UpdatePaymentIntentProps)[] = [
  'metadata',
  'businessEntityId',
  'gatewayPaymentIntent',
];

const UPDATEABLE_FIELDS: (keyof EntityProps)[] = [
  'metadata',
  'businessEntityId',
  'gatewayPaymentIntent',
  'paymentMethodType',
  'statementDescriptor',
  'amount',
  'paymentSplits',
  'platformPaymentFeeFixed',
  'platformPaymentFeePercentage',
  'platformPaymentFee',
  'marketplacePaymentFeeFixed',
  'marketplacePaymentFeePercentage',
  'marketplacePaymentFee',
  'providerFeeFixed',
  'providerFeePercentage',
  'providerFee',
  'marketplaceOrderFee',
  'platformOrderFee',
];

export class PaymentIntent extends AggregateRoot<EntityProps> {
  static create({ id = idFactory(), ...data }: CreatePaymentIntentProps & { id?: string }) {
    checkRules(
      new PaymentSplitReceiversMustBeUniqueRule(data.paymentSplits),
      new NetAmountMustBePositiveRule(
        data.amount,
        data.marketplacePaymentFee,
        data.marketplaceOrderFee,
        data.platformOrderFee,
      ),
      new TotalPaymentSplitsEqualsNetAmountRule(
        data.paymentSplits,
        data.amount,
        data.marketplacePaymentFee,
        data.marketplaceOrderFee,
        data.platformOrderFee,
      ),
    );

    const paymentIntent = new PaymentIntent({
      id,
      props: {
        ...data,
        status: PaymentIntentStatus.PENDING,
      },
    });

    paymentIntent.addEvent(
      new PaymentIntentCreatedDomainEvent({ aggregateId: paymentIntent.getId() }),
    );

    return paymentIntent;
  }

  update(data: UpdatePaymentIntentProps) {
    this.checkUpdateRules(data);

    this.props = {
      ...this.props,
      ...pickBy(pick(data, UPDATEABLE_FIELDS), identity), // remove undefined fields
    };

    this.addEvent(new PaymentIntentUpdatedEvent({ aggregateId: this.getId() }));
  }

  checkUpdateRules(data: UpdatePaymentIntentProps) {
    if (!this.onlyContainsAlwaysUpdateableFields(data)) {
      checkRules(new PaymentIntentMustBePendingRule(this.props.status));
    }

    if (data.updateType === PaymentIntentUpdateType.WITH_AMOUNT) {
      this.checkSplitsRules(data);
    }
  }

  checkSplitsRules(data: {
    amount: Money;
    paymentSplits: PaymentSplit[];
    marketplacePaymentFee: Money;
    marketplaceOrderFee: Money;
    platformOrderFee: Money;
  }) {
    checkRules(
      new PaymentSplitReceiversMustBeUniqueRule(data.paymentSplits),
      new NetAmountMustBePositiveRule(
        data.amount,
        data.marketplacePaymentFee,
        data.marketplaceOrderFee,
        data.platformOrderFee,
      ),
      new TotalPaymentSplitsEqualsNetAmountRule(
        data.paymentSplits,
        data.amount,
        data.marketplacePaymentFee,
        data.marketplaceOrderFee,
        data.platformOrderFee,
      ),
    );
  }

  private onlyContainsAlwaysUpdateableFields(data: UpdatePaymentIntentProps) {
    const nonUndefinedFields = pickBy(data, identity);

    return Object.keys(nonUndefinedFields).every((field) =>
      ALWAYS_UPDATEABLE_FIELDS.includes(field as keyof UpdatePaymentIntentProps),
    );
  }

  expire() {
    checkRules(new PaymentIntentMustBePendingRule(this.props.status));

    this.props.status = PaymentIntentStatus.EXPIRED;

    this.addEvent(new PaymentIntentExpiredEvent({ aggregateId: this.getId() }));
  }

  markAsSettled(providerFee: Money, paymentMethodId: string) {
    checkRules(
      new PaymentIntentMustBePendingRule(this.props.status),
      new MoneyCurrencyMustMatchRule(providerFee, this.props.amount),
    );

    this.props.status = PaymentIntentStatus.SETTLED;

    this.addEvent(
      new PaymentIntentSettledDomainEvent({
        aggregateId: this.getId(),
        amount: this.props.amount,
        customerId: this.props.customerId,
        gateway: this.props.gateway,
        providerFee,
        paymentMethodType: this.props.paymentMethodType,
        paymentSplits: this.props.paymentSplits,
        marketplaceId: this.props.marketplaceId,
        paymentMethodId,
      }),
    );
  }
}
