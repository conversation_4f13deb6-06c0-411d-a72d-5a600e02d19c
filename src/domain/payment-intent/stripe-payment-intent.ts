import { idFactory } from '@/core/ddd';

import {
  GatewayPaymentIntentBase,
  GatewayPaymentIntentBaseEntityProps,
} from './gateway-payment-intent.base';

type EntityProps = GatewayPaymentIntentBaseEntityProps & {
  stripeClientSecret: string;
};

export class StripePaymentIntent extends GatewayPaymentIntentBase<EntityProps> {
  static create(props: EntityProps) {
    return new StripePaymentIntent({
      id: idFactory(),
      props,
    });
  }

  update(props: Pick<EntityProps, 'stripeClientSecret'>) {
    this.props.stripeClientSecret = props.stripeClientSecret;
  }
}
