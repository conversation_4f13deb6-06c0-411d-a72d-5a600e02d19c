import { AggregateRoot, checkRules, idFactory } from '@/core/ddd';

import { AllowedCurrencies, Money, MoneyCurrencyMustMatchRule } from '../shared';

export type EntityProps = {
  balance: Money;
  currency: AllowedCurrencies;
  marketplaceId: string;
  gatewayId: string;
  marketplacePaymentFeeFixed: Money;
  marketplacePaymentFeePercentage: number;
  platformPaymentFeeFixed: Money;
  platformPaymentFeePercentage: number;
};

export class MarketplaceGateway extends AggregateRoot<EntityProps> {
  static create(props: EntityProps) {
    return new MarketplaceGateway({
      id: idFactory(),
      props,
    });
  }

  addToBalance(amount: Money) {
    checkRules(new MoneyCurrencyMustMatchRule(this.props.balance, amount));

    this.props.balance = this.props.balance.add(amount);
  }

  subtractFromBalance(amount: Money) {
    checkRules(new MoneyCurrencyMustMatchRule(this.props.balance, amount));

    this.props.balance = this.props.balance.sub(amount);
  }
}
