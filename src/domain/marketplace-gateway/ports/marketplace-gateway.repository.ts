import { IRepository } from '@/core/ddd';

import { MarketplaceGateway } from '../marketplace-gateway';

export type FindByMarketplaceAndGatewayIdsProps = {
  marketplaceId: string;
  gatewayId: string;
};

export interface MarketplaceGatewayRepositoryPort extends IRepository<MarketplaceGateway> {
  findByMarketplaceAndGatewayId({
    marketplaceId,
    gatewayId,
  }: FindByMarketplaceAndGatewayIdsProps): Promise<MarketplaceGateway>;
}
