export const MARKETPLACE_GATEWAY_REPOSITORY_TOKEN = Symbol('MARKETPLACE_GATEWAY_REPOSITORY_TOKEN');

export const MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN = Symbol(
  'MARKETPLACE_GATEWAY_TRANSACTION_REPOSITORY_TOKEN',
);

export const MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN = Symbol(
  'MARKETPLACE_GATEWAY_ACCOUNT_REPOSITORY_TOKEN',
);

export const TRANSACTION_REPOSITORY_TOKEN = Symbol('TRANSACTION_REPOSITORY_TOKEN');

export const DISPUTE_REPOSITORY_TOKEN = Symbol('DISPUTE_REPOSITORY_TOKEN');

export const PAYMENT_INTENT_REPOSITORY_TOKEN = Symbol('PAYMENT_INTENT_REPOSITORY_TOKEN');

export const PAYMENT_REPOSITORY_TOKEN = Symbol('PAYMENT_REPOSITORY_TOKEN');

export const PAYMENT_SPLIT_REPOSITORY_TOKEN = Symbol('PAYMENT_SPLIT_REPOSITORY_TOKEN');

export const PAYOUT_REPOSITORY_TOKEN = Symbol('PAYOUT_REPOSITORY_TOKEN');

export const GATEWAY_SERVICE_FACTORY_TOKEN = Symbol('GATEWAY_SERVICE_FACTORY_TOKEN');

export const GATEWAY_REPOSITORY_TOKEN = Symbol('GATEWAY_REPOSITORY_TOKEN');

export const GATEWAY_TRANSFER_REPOSITORY_TOKEN = Symbol('GATEWAY_TRANSFER_REPOSITORY_TOKEN');

export const WEBHOOK_REPOSITORY_TOKEN = Symbol('WEBHOOK_REPOSITORY_TOKEN');

export const WEBHOOK_SENDER_SERVICE_TOKEN = Symbol('WEBHOOK_SENDER_SERVICE_TOKEN');

export const CUSTOMER_REPOSITORY_TOKEN = Symbol('CUSTOMER_REPOSITORY_TOKEN');

export const ACCOUNT_REPOSITORY_TOKEN = Symbol('ACCOUNT_REPOSITORY_TOKEN');

export const PAYMENT_METHOD_REPOSITORY_TOKEN = Symbol('PAYMENT_METHOD_REPOSITORY_TOKEN');

export const DEBT_REPOSITORY_TOKEN = Symbol('DEBT_REPOSITORY_TOKEN');

export const MARKETPLACE_REPOSITORY_TOKEN = Symbol('MARKETPLACE_REPOSITORY_TOKEN');

export const FEE_SETTINGS_REPOSITORY_TOKEN = Symbol('FEE_SETTINGS_REPOSITORY_TOKEN');

export const API_KEY_GENERATOR_SERVICE_TOKEN = Symbol('API_KEY_GENERATOR_SERVICE_TOKEN');
