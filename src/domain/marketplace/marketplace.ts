import { AggregateRoot, idFactory } from '@/core/ddd';

import { MarketplaceCreatedDomainEvent } from './events';
import { WebhookEndpoint } from './webhook-endpoint.value-object';
import { ApiKeyPair } from '../shared';

type EntityProps = {
  name: string;
  apiKeyPair: ApiKeyPair;
  webhookEndpoint: WebhookEndpoint;
};

export class Marketplace extends AggregateRoot<EntityProps> {
  static create(props: EntityProps) {
    const marketplace = new Marketplace({
      id: idFactory(),
      props,
    });

    marketplace.addEvent(new MarketplaceCreatedDomainEvent({ aggregateId: marketplace.getId() }));

    return marketplace;
  }
}
