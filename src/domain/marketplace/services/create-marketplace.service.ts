import { Inject, Injectable } from '@nestjs/common';

import { API_KEY_GENERATOR_SERVICE_TOKEN } from '@/domain/di-tokens';
import { ApiKeyGeneratorServicePort } from '@/domain/shared';

import { Marketplace } from '../marketplace';
import { WebhookEndpoint } from '../webhook-endpoint.value-object';

export type CreateMarketplaceParams = {
  name: string;
  webhookUrl: string;
};

@Injectable()
export class CreateMarketplaceService {
  private WEBHOOK_SECRET_PREFIX = 'wh';

  constructor(
    @Inject(API_KEY_GENERATOR_SERVICE_TOKEN)
    private readonly apiKeyGenerator: ApiKeyGeneratorServicePort,
  ) {}

  createMarketplace({ name, webhookUrl }: CreateMarketplaceParams) {
    const apiKeyPair = this.apiKeyGenerator.generatePair();
    const webhookSecret = this.apiKeyGenerator.generateSecretKey(this.WEBHOOK_SECRET_PREFIX);

    const marketplace = Marketplace.create({
      name,
      apiKeyPair,
      webhookEndpoint: new WebhookEndpoint({
        webhookSecret,
        webhookUrl,
      }),
    });

    return marketplace;
  }
}
