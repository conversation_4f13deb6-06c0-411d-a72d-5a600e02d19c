import { DebtNotPendingOrPartiallyPaidError } from '@/domain/debt/errors';
import * as Entities from '@/test/helpers/creators';

import { PayOffAmountMustNotExceedDebtAmountRule } from '.';

describe('PayOffAmountMustNotExceedDebtAmountRule', () => {
  it('should succeed if payoff amount is within the debt amount and fail otherwise', () => {
    const debt = Entities.debt(100).paidAmount(30).create();

    expect(
      new PayOffAmountMustNotExceedDebtAmountRule(debt, Entities.money(70)).isBroken()
    ).toBe(false);
    expect(
      new PayOffAmountMustNotExceedDebtAmountRule(debt, Entities.money(70.1)).isBroken()
    ).toBe(true);
  });

  it('should return an DebtNotPendingOrPartiallyPaidError to indicate the payoff amount exceeded the debt amount', () => {
    const rule = new PayOffAmountMustNotExceedDebtAmountRule(
      Entities.debt(0).create(), Entities.money(0)
    );
    expect(rule.getError()).toBeInstanceOf(DebtNotPendingOrPartiallyPaidError);
  });
});
