import { DebtStatus } from '@/domain/debt/debt';
import { DebtNotPendingOrPartiallyPaidError } from '@/domain/debt/errors';

import { DebtMustBePendingOrPartiallyPaidRule } from '.';

describe('DebtMustBePendingOrPartiallyPaidRule', () => {
  it('should succeed if debt status is pending or partially paid and fail otherwise', () => {
    expect(
      new DebtMustBePendingOrPartiallyPaidRule(DebtStatus.PENDING).isBroken()
    ).toBe(false);
    expect(
      new DebtMustBePendingOrPartiallyPaidRule(DebtStatus.PARTIALLY_PAID).isBroken()
    ).toBe(false);
    expect(
      new DebtMustBePendingOrPartiallyPaidRule('other' as DebtStatus).isBroken()
    ).toBe(true);
  });

  it('should return an DebtNotPendingOrPartiallyPaidError to indicate the debt status is not pending or partially paid', () => {
    const rule = new DebtMustBePendingOrPartiallyPaidRule(DebtStatus.CANCELED);
    expect(rule.getError()).toBeInstanceOf(DebtNotPendingOrPartiallyPaidError);
  });
});
