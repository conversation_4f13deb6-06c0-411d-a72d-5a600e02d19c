import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { Debt } from '../debt';
import { DebtNotPendingOrPartiallyPaidError } from '../errors';

export class PayOffAmountMustNotExceedDebtAmountRule implements IBusinessRule {
  constructor(
    private readonly debt: Debt,
    private readonly payoffAmount: Money,
  ) {}

  getError(): DomainError {
    return new DebtNotPendingOrPartiallyPaidError();
  }

  isBroken(): boolean {
    const { paidAmount, amount } = this.debt.getProps();

    const remainingAmount = amount.sub(paidAmount);

    return this.payoffAmount.greaterThan(remainingAmount);
  }
}
