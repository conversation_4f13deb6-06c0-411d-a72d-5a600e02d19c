import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { DebtStatus } from '../debt';
import { DebtNotPendingOrPartiallyPaidError } from '../errors';

export class DebtMustBePendingOrPartiallyPaidRule implements IBusinessRule {
  constructor(private readonly debtStatus: DebtStatus) {}

  getError(): DomainError {
    return new DebtNotPendingOrPartiallyPaidError();
  }

  isBroken(): boolean {
    return this.debtStatus !== DebtStatus.PENDING && this.debtStatus !== DebtStatus.PARTIALLY_PAID;
  }
}
