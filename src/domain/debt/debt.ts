import { AggregateRoot, checkRules, idFactory } from '@/core/ddd';

import {
  DebtCreatedDomainEvent,
  DebtPartiallyPaidDomainEvent,
  DebtSettledDomainEvent,
} from './events';
import {
  DebtMustBePendingOrPartiallyPaidRule,
  PayOffAmountMustNotExceedDebtAmountRule,
} from './rules';
import { Money } from '../shared';

export enum DebtType {
  PLATFORM_TO_ACCOUNT = 'platform-to-account',
  ACCOUNT_TO_PLATFORM = 'account-to-platform',
}

export enum DebtStatus {
  PENDING = 'pending',
  CANCELED = 'canceled',
  PARTIALLY_PAID = 'partially-paid',
  SETTLED = 'settled',
}

export enum DebtSource {
  PAYMENT_SPLIT = 'payment-split',
  REFUND_SPLIT = 'refund-split',
  REFUND = 'refund',
}

export type EntityProps = {
  accountId: string;
  amount: Money;
  paidAmount: Money;
  type: DebtType;
  status: DebtStatus;
  source: DebtSource;
  sourceId: string;
};

type CreateEntityProps = Pick<EntityProps, 'accountId' | 'amount' | 'type' | 'source' | 'sourceId'>;

export class Debt extends AggregateRoot<EntityProps> {
  static create(props: CreateEntityProps) {
    const account = new Debt({
      id: idFactory(),
      props: {
        accountId: props.accountId,
        amount: props.amount,
        type: props.type,
        status: DebtStatus.PENDING,
        paidAmount: Money.zeroFrom(props.amount),
        source: props.source,
        sourceId: props.sourceId,
      },
    });

    account.addEvent(new DebtCreatedDomainEvent({ aggregateId: account.getId() }));

    return account;
  }

  payDebt(payoffAmount: Money) {
    checkRules(
      new DebtMustBePendingOrPartiallyPaidRule(this.props.status),
      new PayOffAmountMustNotExceedDebtAmountRule(this, payoffAmount),
    );

    this.props.paidAmount = this.props.paidAmount.add(payoffAmount);

    if (this.props.paidAmount.equals(this.props.amount)) {
      this.props.status = DebtStatus.SETTLED;
      this.addEvent(new DebtSettledDomainEvent({ aggregateId: this.getId() }));
    } else {
      this.props.status = DebtStatus.PARTIALLY_PAID;
      this.addEvent(new DebtPartiallyPaidDomainEvent({ aggregateId: this.getId() }));
    }
  }
}
