import { GatewayType } from '../enums';
import { Money } from '../value-objects';

export enum GatewayTransferStatus {
  PENDING = 'pending',
  CANCELED = 'canceled',
  FAILED = 'failed',
  SETTLED = 'settled',
}

export enum GatewayTransferSource {
  PAYMENT_SPLIT = 'payment-split',
  REFUND_SPLIT = 'refund-split',
  PAYOUT = 'payout',
  PAYOUT_REVERSAL = 'payout-reversal',
}

export interface GatewayTransfer {
  id: string;
  idAtGateway: string;
  amount: Money;
  status: GatewayTransferStatus;
  destinationAccountId: string;
  source: GatewayTransferSource;
  sourceId: string;
  idempotencyKey: string;
  gatewayType: GatewayType;
  version: number;
  createdAt: Date;
  updatedAt: Date;
}
