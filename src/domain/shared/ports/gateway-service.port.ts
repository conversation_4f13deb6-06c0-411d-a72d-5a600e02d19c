import { GatewayCustomer } from '@/domain/customer/gateway-customer';
import { GatewayRefund } from '@/domain/payment/gateway-refund';
import { Payment } from '@/domain/payment/payment';
import { GatewayPaymentIntent } from '@/domain/payment-intent/payment-intent';

import { PaymentMethodType } from '../enums';
import { GatewayTransfer, GatewayTransferSource } from '../interfaces';
import { Gateway, Money } from '../value-objects';

export type GatewayMetadata = Record<string | number | symbol, unknown>;

export type CreateGatewayPaymentIntentParams = {
  amount: Money;
  paymentMethodType: PaymentMethodType;
  statementDescriptor: string | null;
  metadata?: GatewayMetadata;
  gatewayCustomerId: string;
};

export type UpdateGatewayPaymentIntentParams<T = GatewayPaymentIntent> = {
  gatewayPaymentIntent: T;
  amount?: Money;
  paymentMethodType?: PaymentMethodType;
  metadata?: GatewayMetadata;
  statementDescriptor?: string;
};

export type CreateOutboundGatewayTransferParams = {
  amount: Money;
  receivingGatewayAccountId: string;
  idempotencyKey: string;
  source: GatewayTransferSource;
  sourceId: string;
  metadata?: GatewayMetadata;
  /**
   * Some providers allow passing payment ids to support extended functionality. Will pass `paymentIntentId` to be able to retrieve these params
   */
  paymentIntentId?: string;
};

export type CreateInboundGatewayTransferParams = {
  amount: Money;
  sendingGatewayAccountId: string;
  idempotencyKey: string;
  source: GatewayTransferSource;
  sourceId: string;
  metadata?: GatewayMetadata;
  /**
   * Stripe allows transfer reversal, will use it for getting original transfer
   */
  originalSourceId?: string;
};

export type CreateGatewayRefundParams = {
  payment: Payment;
  amount: Money;
  idempotencyKey: string;
  metadata?: GatewayMetadata;
};

type GatewayAccount = {
  id: string;
  email: string | null;
};

export interface GatewayServicePort {
  gateway: Gateway;

  getValidatedWebhookData(req: Request, data: object): object;
  getAccount(accountId: string): Promise<GatewayAccount>;
  createCustomer(): Promise<GatewayCustomer>;
  createPaymentIntent(data: CreateGatewayPaymentIntentParams): Promise<GatewayPaymentIntent>;
  updatePaymentIntent(data: UpdateGatewayPaymentIntentParams): Promise<GatewayPaymentIntent>;
  getProviderFee(gatewayPaymentIntent: GatewayPaymentIntent): Promise<Money>;

  createOutboundTransfer(data: CreateOutboundGatewayTransferParams): Promise<GatewayTransfer>;
  createInboundTransfer(data: CreateInboundGatewayTransferParams): Promise<GatewayTransfer>;

  createRefund(data: CreateGatewayRefundParams): Promise<GatewayRefund>;

  getFingerprintByPaymentMethodId(paymentMethodId: string): Promise<string>;
}
