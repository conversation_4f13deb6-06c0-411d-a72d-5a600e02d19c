import { GatewayTransfer, GatewayTransferSource } from '../interfaces';

export type CreateGatewayTransferRepositoryParams = Pick<
  GatewayTransfer,
  | 'destinationAccountId'
  | 'sourceId'
  | 'status'
  | 'source'
  | 'gatewayType'
  | 'idAtGateway'
  | 'idempotencyKey'
  | 'amount'
>;

export type UpdateGatewayTransferRepositoryParams = Omit<
  GatewayTransfer,
  'createdAt' | 'updatedAt'
>;

export interface GatewayTransferRepositoryPort {
  findByGatewayTransferId(gatewayTransferId: string): Promise<GatewayTransfer | null>;

  findByGatewayTransferIds(gatewayTransferIds: string[]): Promise<GatewayTransfer[]>;

  findBySource(
    sourceType: GatewayTransferSource,
    sourceId: string,
  ): Promise<GatewayTransfer | null>;

  create(params: CreateGatewayTransferRepositoryParams): Promise<GatewayTransfer>;

  update(params: UpdateGatewayTransferRepositoryParams): Promise<void>;
}
