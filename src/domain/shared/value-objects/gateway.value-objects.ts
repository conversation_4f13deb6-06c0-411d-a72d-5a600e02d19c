import { ValueObject } from '@/core/ddd';

import { GatewayType, NotSupportedGatewayTypeError } from '..';

type Props = {
  gatewayId: string;
  gatewayType: GatewayType;
};

export class Gateway extends ValueObject<Props> {
  constructor(gatewayId: string, gatewayType: string) {
    const validatedGatewayType = GatewayType[gatewayType.toUpperCase()];
    if (!validatedGatewayType) {
      throw new NotSupportedGatewayTypeError();
    }

    super({
      gatewayId,
      gatewayType: validatedGatewayType,
    });
  }

  equals(gateway: Gateway) {
    return this.props.gatewayId === gateway.getProps().gatewayId;
  }

  getType() {
    return this.props.gatewayType;
  }

  getGatewayId() {
    return this.props.gatewayId;
  }
}
