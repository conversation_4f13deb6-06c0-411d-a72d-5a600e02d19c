import Decimal from 'decimal.js';

import { ValueObject } from '@/core/ddd';

import { Currency } from './currency.value-object';
import { CurrencyMismatchError } from '../errors/currency-mismatch.error';

type Props = {
  amount: Decimal;
  currency: Currency;
};

export class Money extends ValueObject<Props> {
  constructor(amount: Decimal.Value, currency: Currency) {
    super({
      amount: new Decimal(amount),
      currency,
    });
  }

  static from(amount: Decimal.Value, currency: string) {
    return new Money(amount, new Currency(currency));
  }

  static zeroFrom(money: Money) {
    return new Money(0, money.getCurrency());
  }

  copy() {
    return new Money(this.props.amount, this.props.currency);
  }

  isPositive() {
    return this.props.amount.isPositive() && !this.isZero();
  }

  isNegative() {
    return this.props.amount.isNegative();
  }

  isZero() {
    return this.props.amount.isZero();
  }

  toFixed(decimalPlaces: number) {
    return this.props.amount.toFixed(decimalPlaces);
  }

  round() {
    return new Money(this.props.amount.round(), this.props.currency);
  }

  ceil() {
    return new Money(this.props.amount.ceil(), this.props.currency);
  }

  floor() {
    return new Money(this.props.amount.floor(), this.props.currency);
  }

  add(money: Money) {
    this.validateCurrency(money);

    return new Money(this.props.amount.add(money.props.amount), this.props.currency);
  }

  sub(money: Money) {
    this.validateCurrency(money);

    return new Money(this.props.amount.sub(money.props.amount), this.props.currency);
  }

  mul(factor: number) {
    return new Money(this.props.amount.mul(factor), this.props.currency);
  }

  div(factor: number) {
    return new Money(this.props.amount.div(factor), this.props.currency);
  }

  greaterThan(money: Money) {
    return this.props.amount.greaterThan(money.props.amount);
  }

  greaterThanOrEqualTo(money: Money) {
    return this.props.amount.greaterThanOrEqualTo(money.props.amount);
  }

  lessThan(money: Money) {
    return this.props.amount.lessThan(money.props.amount);
  }

  lessThanOrEqualTo(money: Money) {
    return this.props.amount.lessThanOrEqualTo(money.props.amount);
  }

  equals(money: Money) {
    return (
      this.props.amount.equals(money.props.amount) &&
      this.props.currency.equals(money.props.currency)
    );
  }

  getCurrency() {
    return this.props.currency;
  }

  toNumber() {
    return this.props.amount.toNumber();
  }

  toObject() {
    return {
      amount: this.props.amount.toNumber(),
      currency: this.props.currency.getValue(),
    };
  }

  negated() {
    return new Money(this.props.amount.negated(), this.props.currency);
  }

  private validateCurrency(money: Money) {
    if (!this.props.currency.equals(money.props.currency)) {
      throw new CurrencyMismatchError();
    }
  }
}
