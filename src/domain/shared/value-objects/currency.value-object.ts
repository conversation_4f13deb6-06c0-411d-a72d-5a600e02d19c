import { ValueObject } from '@/core/ddd';

import { NotSupportedCurrencyError } from '../errors/not-supported-currency.error';

export enum AllowedCurrencies {
  USD = 'USD',
}

type Props = {
  value: AllowedCurrencies;
};

export class Currency extends ValueObject<Props> {
  constructor(currency: string) {
    const allowedCurrency = AllowedCurrencies[currency.toUpperCase()];

    if (!allowedCurrency) {
      throw new NotSupportedCurrencyError();
    }

    super({ value: allowedCurrency });
  }

  getValue() {
    return this.props.value;
  }

  equals(currency: Currency) {
    return this.props.value === currency.props.value;
  }
}
