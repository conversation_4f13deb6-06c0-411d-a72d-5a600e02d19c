import { ValueObject } from '@/core/ddd';

type Props = {
  secretId: string;
  secretHash: string;
  secretValue: string;
};

export class ManagedSecret extends ValueObject<Props> {
  getSecretId(): string {
    return this.props.secretId;
  }

  getSecretHash(): string {
    return this.props.secretHash;
  }

  getValue(): string {
    return this.props.secretValue;
  }

  equals(secret: ManagedSecret) {
    const { secretHash, secretValue } = secret.getProps();

    return this.props.secretHash === secretHash && this.props.secretValue === secretValue;
  }
}
