import {
  AllowedCurrencies,
  CurrencyMismatchError,
  Money,
  MoneyCurrencyMustMatchRule,
} from '@/domain/shared';

describe('MoneyCurrencyMustMatchRule', () => {
  it('should succeed if the currencies match and fail otherwise', () => {
    const rule = new MoneyCurrencyMustMatchRule(
      Money.from(10, AllowedCurrencies.USD),
      Money.from(20, AllowedCurrencies.USD),
    );
    expect(rule.isBroken()).toBe(false);

    const otherMoney = Money.from(20, AllowedCurrencies.USD);
    // Modify the currency to make the rule fail
    (otherMoney.getCurrency() as unknown as { props: { value : string } }).props.value = 'other';
    const rule2 = new MoneyCurrencyMustMatchRule(
      Money.from(10, AllowedCurrencies.USD),
      otherMoney,
    );
    expect(rule2.isBroken()).toBe(true);
  });

  it('should return an CurrencyMismatchError to indicate the currencies do not match', () => {
    const rule = new MoneyCurrencyMustMatchRule(
      Money.from(10, AllowedCurrencies.USD),
      Money.from(20, AllowedCurrencies.USD),
    );
    expect(rule.getError()).toBeInstanceOf(CurrencyMismatchError);
  });
});
