import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { CurrencyMismatchError } from '../errors';
import { Money } from '../value-objects';

export class MoneyCurrencyMustMatchRule implements IBusinessRule {
  constructor(
    private readonly left: Money,
    private readonly right: Money,
  ) {}

  getError(): DomainError {
    return new CurrencyMismatchError();
  }

  isBroken(): boolean {
    return !this.left.getCurrency().equals(this.right.getCurrency());
  }
}
