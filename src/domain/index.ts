import { DisputeDomainService } from '@/domain/dispute/services';

import { GatewayCustomerService } from './customer/services';
import { FeeSettingsDomainService } from './fee-settings/services/fee-settings.domain-service';
import { CreateMarketplaceService } from './marketplace/services';
import { CreateRefundDomainService, RefundProcessingDomainService } from './payment/services';
import { PaymentIntentDomainService } from './payment-intent/services';
import { PaymentSplitDomainService } from './payment-split/services';
import { TransactionDomainService } from './transaction/services';

const DOMAIN_SERVICES = [
  PaymentIntentDomainService,
  GatewayCustomerService,
  CreateMarketplaceService,
  RefundProcessingDomainService,
  CreateRefundDomainService,
  PaymentSplitDomainService,
  DisputeDomainService,
  TransactionDomainService,
  FeeSettingsDomainService,
];

export const DOMAIN_PROVIDERS = [...DOMAIN_SERVICES];
