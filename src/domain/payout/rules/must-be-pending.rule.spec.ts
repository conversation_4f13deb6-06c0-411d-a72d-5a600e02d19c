import { PayoutNotPendingError } from '@/domain/payout/errors';
import { PayoutStatus } from '@/domain/payout/payout';

import { MustBePendingRule } from '.';

describe('MustBePendingRule', () => {
  it('should succeed if payout status is pending and fail otherwise', () => {
    const rule = new MustBePendingRule(PayoutStatus.PENDING);
    expect(rule.isBroken()).toBe(false);

    const rule2 = new MustBePendingRule('other' as PayoutStatus);
    expect(rule2.isBroken()).toBe(true);
  });

  it('should return an PayoutNotPendingError to indicate the payout status is not pending', () => {
    const rule = new MustBePendingRule(PayoutStatus.PENDING);
    expect(rule.getError()).toBeInstanceOf(PayoutNotPendingError);
  });
});
