import { ReversalNotPendingError } from '@/domain/payout/errors';
import { PayoutReversalStatus } from '@/domain/payout/payout-reversal';

import { ReversalMustBePendingRule } from '.';

describe('ReversalMustBePendingRule', () => {
  it('should succeed if reversal status is pending and fail otherwise', () => {
    const rule = new ReversalMustBePendingRule(PayoutReversalStatus.PENDING);
    expect(rule.isBroken()).toBe(false);

    const rule2 = new ReversalMustBePendingRule('other' as PayoutReversalStatus);
    expect(rule2.isBroken()).toBe(true);
  });

  it('should return an ReversalNotPendingError to indicate the reversal status is not pending', () => {
    const rule = new ReversalMustBePendingRule(PayoutReversalStatus.PENDING);
    expect(rule.getError()).toBeInstanceOf(ReversalNotPendingError);
  });
});
