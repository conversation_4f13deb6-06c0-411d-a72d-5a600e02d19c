import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { ReversalAmountExceededRemainingAmountError } from '../errors';
import { Payout } from '../payout';
import { PayoutReversalStatus } from '../payout-reversal';

export class ReversalAmountMustNotExceedRemainingAmountRule implements IBusinessRule {
  constructor(
    private readonly payout: Payout,
    private readonly reversalAmount: Money,
  ) {}

  getError(): DomainError {
    return new ReversalAmountExceededRemainingAmountError();
  }

  isBroken(): boolean {
    const remainingAmount = this.calculateRemainingAmount();

    return this.reversalAmount.greaterThan(remainingAmount);
  }

  calculateRemainingAmount() {
    const { reversedAmount, amount } = this.payout.getProps();

    const pendingReversalsTotal = this.calculatePendingReversalsTotal();

    return amount.sub(pendingReversalsTotal).sub(reversedAmount);
  }

  calculatePendingReversalsTotal() {
    const { reversals, amount } = this.payout.getProps();

    const pendingReversals = reversals.filter(
      (x) => x.getProps().status === PayoutReversalStatus.PENDING,
    );

    return pendingReversals.reduce(
      (total, reversal) => total.add(reversal.getProps().amount),
      Money.zeroFrom(amount),
    );
  }
}
