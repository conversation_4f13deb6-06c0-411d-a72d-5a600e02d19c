import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { PayoutNotPendingError } from '../errors';
import { PayoutStatus } from '../payout';

export class MustBePendingRule implements IBusinessRule {
  constructor(private readonly payoutStatus: PayoutStatus) {}

  getError(): DomainError {
    return new PayoutNotPendingError();
  }

  isBroken(): boolean {
    return this.payoutStatus !== PayoutStatus.PENDING;
  }
}
