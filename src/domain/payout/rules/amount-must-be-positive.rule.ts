import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';
import { Money } from '@/domain/shared';

import { AmountNotPositiveError } from '../errors';

export class AmountMustBePositiveRule implements IBusinessRule {
  constructor(private readonly amount: Money) {}

  getError(): DomainError {
    return new AmountNotPositiveError();
  }

  isBroken(): boolean {
    return !this.amount.isPositive();
  }
}
