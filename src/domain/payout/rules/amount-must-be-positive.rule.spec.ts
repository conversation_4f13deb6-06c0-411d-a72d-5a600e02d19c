import { AmountNotPositiveError } from '@/domain/payout/errors';
import * as Entities from '@/test/helpers/creators';

import { AmountMustBePositiveRule } from '.';

describe('AmountMustBePositiveRule', () => {
  it('should succeed if amount is positive and fail otherwise', () => {
    const rule = new AmountMustBePositiveRule(Entities.money(10));
    expect(rule.isBroken()).toBe(false);

    const rule2 = new AmountMustBePositiveRule(Entities.money(-10));
    expect(rule2.isBroken()).toBe(true);
  });

  it('should return an AmountNotPositiveError to indicate the amount is not positive', () => {
    const rule = new AmountMustBePositiveRule(Entities.money(-10));
    expect(rule.getError()).toBeInstanceOf(AmountNotPositiveError);
  });
});
