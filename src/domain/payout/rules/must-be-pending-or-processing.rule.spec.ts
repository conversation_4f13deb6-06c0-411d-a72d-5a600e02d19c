import { PayoutNotPendingOrProcessingError } from '@/domain/payout/errors';
import { PayoutStatus } from '@/domain/payout/payout';

import { MustBePendingOrProcessingRule } from '.';

describe('MustBePendingOrProcessingRule', () => {
  it('should succeed if payout status is pending or processing and fail otherwise', () => {
    const rule = new MustBePendingOrProcessingRule(PayoutStatus.PENDING);
    expect(rule.isBroken()).toBe(false);

    const rule2 = new MustBePendingOrProcessingRule(PayoutStatus.PROCESSING);
    expect(rule2.isBroken()).toBe(false);

    const rule3 = new MustBePendingOrProcessingRule('other' as PayoutStatus);
    expect(rule3.isBroken()).toBe(true);
  });

  it('should return an PayoutNotPendingOrProcessingError to indicate the payout status is not pending or processing', () => {
    const rule = new MustBePendingOrProcessingRule(PayoutStatus.PENDING);
    expect(rule.getError()).toBeInstanceOf(PayoutNotPendingOrProcessingError);
  });
});
