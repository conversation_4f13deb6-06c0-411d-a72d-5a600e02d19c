import { ReversalNotPendingError } from '@/domain/payout/errors';
import { PayoutReversalStatus } from '@/domain/payout/payout-reversal';

import { ReversalMustBePendingOrProcessingRule } from '.';

describe('ReversalMustBePendingOrProcessingRule', () => {
  it('should succeed if reversal status is pending or processing and fail otherwise', () => {
    const rule = new ReversalMustBePendingOrProcessingRule(PayoutReversalStatus.PENDING);
    expect(rule.isBroken()).toBe(false);

    const rule2 = new ReversalMustBePendingOrProcessingRule(PayoutReversalStatus.PROCESSING);
    expect(rule2.isBroken()).toBe(false);

    const rule3 = new ReversalMustBePendingOrProcessingRule('other' as PayoutReversalStatus);
    expect(rule3.isBroken()).toBe(true);
  });

  it('should return an ReversalNotPendingError to indicate the reversal status is not pending or processing', () => {
    const rule = new ReversalMustBePendingOrProcessingRule(PayoutReversalStatus.PENDING);
    expect(rule.getError()).toBeInstanceOf(ReversalNotPendingError);
  });
});
