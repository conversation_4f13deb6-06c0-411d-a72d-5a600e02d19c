import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { ReversalNotPendingError } from '../errors';
import { PayoutReversalStatus } from '../payout-reversal';

export class ReversalMustBePendingRule implements IBusinessRule {
  constructor(private readonly payoutReversalStatus: PayoutReversalStatus) {}

  getError(): DomainError {
    return new ReversalNotPendingError();
  }

  isBroken(): boolean {
    return this.payoutReversalStatus !== PayoutReversalStatus.PENDING;
  }
}
