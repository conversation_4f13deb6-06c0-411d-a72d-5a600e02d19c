import { ReversalAmountExceededRemainingAmountError } from '@/domain/payout/errors';
import * as Entities from '@/test/helpers/creators';

import { ReversalAmountMustNotExceedRemainingAmountRule } from '.';

describe('ReversalAmountMustNotExceedRemainingAmountRule', () => {
  it('should succeed if reversal amount does not exceed remaining amount and fail otherwise', () => {
    const payout = Entities.payout(100)
      .reversedAmount(10)
      .reversals([
        Entities.payoutReversal(20).create(),
        Entities.payoutReversal(30).create(),
      ])
      .create();

    const rule = new ReversalAmountMustNotExceedRemainingAmountRule(payout, Entities.money(40));
    expect(rule.isBroken()).toBe(false);

    const rule2 = new ReversalAmountMustNotExceedRemainingAmountRule(payout, Entities.money(40.1));
    expect(rule2.isBroken()).toBe(true);
  });

  it('should ignore non-pending reversals when calculating remaining amount', () => {
    const payout = Entities.payout(100)
      .reversedAmount(10)
      .reversals([
        Entities.payoutReversal(10).create(),
        Entities.payoutReversal(20).create(),
        Entities.payoutReversal(30).processing().create(),
        Entities.payoutReversal(30).succeeded().create(),
        Entities.payoutReversal(30).failed().create(),
      ])
      .create();

    const rule = new ReversalAmountMustNotExceedRemainingAmountRule(payout, Entities.money(60));
    expect(rule.isBroken()).toBe(false);

    const rule2 = new ReversalAmountMustNotExceedRemainingAmountRule(payout, Entities.money(60.1));
    expect(rule2.isBroken()).toBe(true);
  });

  it('should return an ReversalAmountExceededRemainingAmountError to indicate the reversal amount exceeded the remaining amount', () => {
    const rule = new ReversalAmountMustNotExceedRemainingAmountRule(
      Entities.payout(0).create(), Entities.money(0)
    );
    expect(rule.getError()).toBeInstanceOf(ReversalAmountExceededRemainingAmountError);
  });
});
