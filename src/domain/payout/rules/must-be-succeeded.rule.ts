import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { PayoutNotSucceededError } from '../errors';
import { PayoutStatus } from '../payout';

export class MustBeSucceededRule implements IBusinessRule {
  constructor(private readonly payoutStatus: PayoutStatus) {}

  getError(): DomainError {
    return new PayoutNotSucceededError();
  }

  isBroken(): boolean {
    return this.payoutStatus !== PayoutStatus.SUCCEEDED;
  }
}
