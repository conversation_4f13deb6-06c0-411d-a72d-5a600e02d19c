import { PayoutNotSucceededError } from '@/domain/payout/errors';
import { PayoutStatus } from '@/domain/payout/payout';

import { MustBeSucceededRule } from '.';

describe('MustBeSucceededRule', () => {
  it('should succeed if payout status is succeeded and fail otherwise', () => {
    const rule = new MustBeSucceededRule(PayoutStatus.SUCCEEDED);
    expect(rule.isBroken()).toBe(false);

    const rule2 = new MustBeSucceededRule('other' as PayoutStatus);
    expect(rule2.isBroken()).toBe(true);
  });

  it('should return an PayoutNotSucceededError to indicate the payout status is not succeeded', () => {
    const rule = new MustBeSucceededRule(PayoutStatus.SUCCEEDED);
    expect(rule.getError()).toBeInstanceOf(PayoutNotSucceededError);
  });
});
