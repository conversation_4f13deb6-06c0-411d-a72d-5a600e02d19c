import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { PayoutNotPendingOrProcessingError } from '../errors';
import { PayoutStatus } from '../payout';

export class MustBePendingOrProcessingRule implements IBusinessRule {
  constructor(private readonly payoutStatus: PayoutStatus) {}

  getError(): DomainError {
    return new PayoutNotPendingOrProcessingError();
  }

  isBroken(): boolean {
    return (
      this.payoutStatus !== PayoutStatus.PENDING && this.payoutStatus !== PayoutStatus.PROCESSING
    );
  }
}
