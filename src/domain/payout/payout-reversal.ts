import { Entity, idFactory } from '@/core/ddd';

import { Money } from '../shared';

export enum PayoutReversalStatus {
  SUCCEEDED = 'succeeded',
  PROCESSING = 'processing',
  FAILED = 'failed',
  PENDING = 'pending',
}

type EntityProps = {
  amount: Money;
  status: PayoutReversalStatus;
  refundId?: string;
};

export class PayoutReversal extends Entity<EntityProps> {
  static create(props: Omit<EntityProps, 'status'>) {
    return new PayoutReversal({
      id: idFactory(),
      props: {
        ...props,
        status: PayoutReversalStatus.PENDING,
      },
    });
  }

  markProcessing() {
    this.props.status = PayoutReversalStatus.PROCESSING;
  }

  markFailed() {
    this.props.status = PayoutReversalStatus.PROCESSING;
  }

  markSucceeded() {
    this.props.status = PayoutReversalStatus.SUCCEEDED;
  }
}
