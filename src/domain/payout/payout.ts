import { AggregateRoot, checkRules, idFactory } from '@/core/ddd';

import { ReversalNotFoundError } from './errors';
import {
  PayoutCreatedDomainEvent,
  PayoutFailedDomainEvent,
  PayoutProcessingDomainEvent,
  PayoutReversalCreatedDomainEvent,
  PayoutReversalFailedDomainEvent,
  PayoutReversalProcessingDomainEvent,
  PayoutReversalSucceededDomainEvent,
  PayoutSucceededDomainEvent,
} from './events';
import { PayoutReversal } from './payout-reversal';
import {
  AmountMustBePositiveRule,
  MustBePendingOrProcessingRule,
  MustBePendingRule,
  MustBeSucceededRule,
  ReversalAmountMustNotExceedRemainingAmountRule,
  ReversalMustBePendingOrProcessingRule,
  ReversalMustBePendingRule,
} from './rules';
import { Gateway, Money, MoneyCurrencyMustMatchRule } from '../shared';

export enum PayoutStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
}

export enum PayoutType {
  PAYMENT_FEE = 'payment-fee',
  ORDER_FEE = 'order-fee',
}

type EntityProps = {
  amount: Money;
  reversedAmount: Money;
  gateway: Gateway;
  marketplaceId: string;
  marketplaceGatewayAccountId: string;
  reversals: PayoutReversal[];
  status: PayoutStatus;
  type: PayoutType;
  paymentId?: string;
};

export class Payout extends AggregateRoot<EntityProps> {
  static create(props: Omit<EntityProps, 'reversals' | 'reversedAmount'>) {
    checkRules(new AmountMustBePositiveRule(props.amount));

    const payout = new Payout({
      id: idFactory(),
      props: {
        ...props,
        reversals: [],
        reversedAmount: Money.zeroFrom(props.amount),
      },
    });

    payout.addEvent(new PayoutCreatedDomainEvent({ aggregateId: payout.getId() }));

    return payout;
  }

  markProcessing() {
    checkRules(new MustBePendingRule(this.props.status));

    this.props.status = PayoutStatus.PROCESSING;

    this.addEvent(new PayoutProcessingDomainEvent({ aggregateId: this.getId() }));
  }

  markSucceeded() {
    checkRules(new MustBePendingOrProcessingRule(this.props.status));

    this.props.status = PayoutStatus.SUCCEEDED;

    this.addEvent(new PayoutSucceededDomainEvent({ aggregateId: this.getId() }));
  }

  markFailed() {
    checkRules(new MustBePendingOrProcessingRule(this.props.status));

    this.props.status = PayoutStatus.FAILED;

    this.addEvent(new PayoutFailedDomainEvent({ aggregateId: this.getId() }));
  }

  reverse(amount: Money, refundId?: string) {
    checkRules(
      new MoneyCurrencyMustMatchRule(this.props.amount, amount),
      new AmountMustBePositiveRule(amount),
      new MustBeSucceededRule(this.props.status),
      new ReversalAmountMustNotExceedRemainingAmountRule(this, amount),
    );

    const reversal = PayoutReversal.create({
      amount,
      refundId,
    });

    this.props.reversals.push(reversal);

    this.addEvent(
      new PayoutReversalCreatedDomainEvent({
        aggregateId: this.getId(),
        reversalId: reversal.getId(),
      }),
    );

    return {
      reversalId: reversal.getId(),
    };
  }

  markReversalProcessing(reversalId: string) {
    const reversal = this.getReversal(reversalId);

    checkRules(new ReversalMustBePendingRule(reversal.getProps().status));

    reversal.markProcessing();

    this.addEvent(
      new PayoutReversalProcessingDomainEvent({
        aggregateId: this.getId(),
        reversalId: reversal.getId(),
      }),
    );
  }

  markReversalFailed(reversalId: string) {
    const reversal = this.getReversal(reversalId);

    checkRules(new ReversalMustBePendingOrProcessingRule(reversal.getProps().status));

    reversal.markFailed();

    this.addEvent(
      new PayoutReversalFailedDomainEvent({
        aggregateId: this.getId(),
        reversalId: reversal.getId(),
      }),
    );
  }

  markReversalSucceeded(reversalId: string) {
    const reversal = this.getReversal(reversalId);

    checkRules(new ReversalMustBePendingOrProcessingRule(reversal.getProps().status));

    reversal.markSucceeded();

    this.props.reversedAmount = this.props.reversedAmount.add(reversal.getProps().amount);

    this.addEvent(
      new PayoutReversalSucceededDomainEvent({
        aggregateId: this.getId(),
        reversalId: reversal.getId(),
      }),
    );
  }

  getReversal(reversalId: string) {
    const reversal = this.props.reversals.find((x) => x.getProps().id === reversalId);

    if (!reversal) {
      throw new ReversalNotFoundError();
    }

    return reversal;
  }
}
