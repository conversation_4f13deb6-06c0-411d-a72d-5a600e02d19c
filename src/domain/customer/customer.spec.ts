import { idFactory } from '@/core/ddd';
import { Customer } from '@/domain/customer/customer';
import {
  CustomerCreatedDomainEvent,
  GatewayCustomerLinkedDomainEvent,
} from '@/domain/customer/events';
import { GatewayCustomer } from '@/domain/customer/gateway-customer';

import { DuplicateGatewayCustomer } from './errors';
import { Gateway, GatewayType } from '../shared';

const mockCustomer = {
  marketplaceId: idFactory(),
  name: 'Test Customer',
  email: '<EMAIL>',
};

const mockGatewayCustomer = {
  idAtGateway: 'cus_OqqbLVWZpuB0nl',
  gateway: new Gateway(idFactory(), GatewayType.STRIPE),
};

describe('Customer Aggregate Root', () => {
  describe('create method', () => {
    it('should create a new customer with default properties', () => {
      const customer = Customer.create(mockCustomer);

      expect(customer).toBeDefined();
      expect(customer.getProps().marketplaceId).toEqual(mockCustomer.marketplaceId);
      expect(customer.getProps().name).toEqual(mockCustomer.name);
      expect(customer.getProps().email).toEqual(mockCustomer.email);
      expect(customer.getProps().gatewayCustomers).toEqual([]);

      const events = customer.getEvents();
      expect(events).toHaveLength(1);
      expect(events[0]).toBeInstanceOf(CustomerCreatedDomainEvent);
      expect((events[0] as CustomerCreatedDomainEvent).getProps().aggregateId).toEqual(
        customer.getId(),
      );
    });
  });

  describe('link GatewayCustomer method', () => {
    it('should link a new GatewayCustomer to the customer', () => {
      const customer = Customer.create(mockCustomer);

      const gatewayCustomer = GatewayCustomer.create(mockGatewayCustomer);

      customer.linkGatewayCustomer(gatewayCustomer);

      expect(customer.getProps().gatewayCustomers).toContain(gatewayCustomer);

      const events = customer.getEvents();
      expect(events).toHaveLength(2);
      expect(events[1]).toBeInstanceOf(GatewayCustomerLinkedDomainEvent);
    });

    it('should throw DuplicateGatewayCustomer error', () => {
      const customer = Customer.create(mockCustomer);

      const gatewayCustomer = GatewayCustomer.create(mockGatewayCustomer);

      customer.linkGatewayCustomer(gatewayCustomer);

      expect(() => customer.linkGatewayCustomer(gatewayCustomer)).toThrow(DuplicateGatewayCustomer);

      expect(customer.getProps().gatewayCustomers.length).toBe(1);

      expect(customer.getEvents().length).toBe(2);
    });
  });
});
