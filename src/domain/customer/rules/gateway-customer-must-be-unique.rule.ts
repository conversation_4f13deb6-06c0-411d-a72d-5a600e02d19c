import { DomainError } from '@/core/ddd';
import { IBusinessRule } from '@/core/ddd/business-rule.interface';

import { Customer } from '../customer';
import { DuplicateGatewayCustomer } from '../errors';
import { GatewayCustomer } from '../gateway-customer';

export class GatewayCustomerMustBeUniqueRule implements IBusinessRule {
  constructor(
    private readonly customer: Customer,
    private readonly gatewayCustomer: GatewayCustomer,
  ) {}

  getError(): DomainError {
    return new DuplicateGatewayCustomer();
  }

  isBroken(): boolean {
    return this.customer
      .getProps()
      .gatewayCustomers.some((x) =>
        x.getProps().gateway.equals(this.gatewayCustomer.getProps().gateway),
      );
  }
}
