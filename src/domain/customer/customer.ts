import { AggregateRoot, checkRules, idFactory } from '@/core/ddd';

import { CustomerCreatedDomainEvent, GatewayCustomerLinkedDomainEvent } from './events';
import { GatewayCustomer } from './gateway-customer';
import { GatewayCustomerMustBeUniqueRule } from './rules';

type EntityProps = {
  marketplaceId: string;
  name: string | null;
  email: string | null;
  gatewayCustomers: GatewayCustomer[];
};

export class Customer extends AggregateRoot<EntityProps> {
  static create(data: Omit<EntityProps, 'gatewayCustomers'>) {
    const customer = new Customer({
      id: idFactory(),
      props: {
        ...data,
        gatewayCustomers: [],
      },
    });

    customer.addEvent(new CustomerCreatedDomainEvent({ aggregateId: customer.getId() }));

    return customer;
  }

  linkGatewayCustomer(gatewayCustomer: GatewayCustomer) {
    checkRules(new GatewayCustomerMustBeUniqueRule(this, gatewayCustomer));

    this.props.gatewayCustomers.push(gatewayCustomer);

    this.addEvent(
      new GatewayCustomerLinkedDomainEvent({
        aggregateId: this.getId(),
        gatewayCustomerId: gatewayCustomer.getId(),
      }),
    );
  }
}
