import { Inject, Injectable } from '@nestjs/common';

import { Customer } from '@/domain/customer/customer';
import { CustomerRepositoryPort } from '@/domain/customer/ports';
import { CUSTOMER_REPOSITORY_TOKEN, GATEWAY_SERVICE_FACTORY_TOKEN } from '@/domain/di-tokens';
import { GatewayServiceFactoryPort } from '@/domain/shared';
import { Gateway } from '@/domain/shared/value-objects';

@Injectable()
export class GatewayCustomerService {
  constructor(
    @Inject(CUSTOMER_REPOSITORY_TOKEN)
    private readonly customerRepository: CustomerRepositoryPort,
    @Inject(GATEWAY_SERVICE_FACTORY_TOKEN)
    private readonly gatewayServiceFactory: GatewayServiceFactoryPort,
  ) {}

  async getGatewayCustomer(customerId: string, gateway: Gateway) {
    const customer = await this.customerRepository.findById(customerId);

    if (!customer) {
      throw new Error('Customer not found'); // todo: custom error
    }

    let gatewayCustomer = this.getExternalGatewayCustomer(customer, gateway);

    const gatewayService = this.gatewayServiceFactory.getGateway(gateway);

    if (!gatewayCustomer) {
      gatewayCustomer = await gatewayService.createCustomer();

      customer.linkGatewayCustomer(gatewayCustomer);

      await this.customerRepository.update(customer);
    }

    return gatewayCustomer;
  }

  private getExternalGatewayCustomer(customer: Customer, gateway: Gateway) {
    const gatewayCustomer = customer
      .getProps()
      .gatewayCustomers.find((x) => x.getProps().gateway.equals(gateway));

    return gatewayCustomer;
  }
}
