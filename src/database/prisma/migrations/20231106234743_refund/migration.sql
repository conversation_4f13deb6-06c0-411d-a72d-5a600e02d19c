/*
  Warnings:

  - You are about to drop the column `receivingAccountId` on the `GatewayTransfer` table. All the data in the column will be lost.
  - Added the required column `amount` to the `GatewayTransfer` table without a default value. This is not possible if the table is not empty.
  - Added the required column `currency` to the `GatewayTransfer` table without a default value. This is not possible if the table is not empty.
  - Added the required column `destinationAccountId` to the `GatewayTransfer` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "GatewayTransfer" DROP COLUMN "receivingAccountId",
ADD COLUMN     "amount" DECIMAL(10,2) NOT NULL,
ADD COLUMN     "currency" VARCHAR NOT NULL,
ADD COLUMN     "destinationAccountId" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "GatewayRefund" (
    "id" UUID NOT NULL,
    "idAtGateway" TEXT NOT NULL,
    "version" INTEGER NOT NULL DEFAULT 1,
    "idempotencyKey" TEXT NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "currency" VARCHAR NOT NULL,
    "paymentId" UUID NOT NULL,
    "status" TEXT NOT NULL,
    "gatewayType" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "GatewayRefund_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "GatewayRefund_idAtGateway_key" ON "GatewayRefund"("idAtGateway");

-- AddForeignKey
ALTER TABLE "GatewayRefund" ADD CONSTRAINT "GatewayRefund_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE CASCADE ON UPDATE CASCADE;
