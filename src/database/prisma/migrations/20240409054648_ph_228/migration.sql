/*
  Warnings:

  - Made the column `feeFixed` on table `Gateway` required. This step will fail if there are existing NULL values in that column.
  - Made the column `feePercentage` on table `Gateway` required. This step will fail if there are existing NULL values in that column.
  - Made the column `marketplacePaymentFee` on table `Payment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `marketplacePaymentFeeFixed` on table `Payment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `marketplacePaymentFeePercentage` on table `Payment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `platformPaymentFee` on table `Payment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `platformPaymentFeeFixed` on table `Payment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `platformPaymentFeePercentage` on table `Payment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `providerFeeFixed` on table `Payment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `providerFeePercentage` on table `Payment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `marketplacePaymentFee` on table `PaymentIntent` required. This step will fail if there are existing NULL values in that column.
  - Made the column `marketplacePaymentFeeFixed` on table `PaymentIntent` required. This step will fail if there are existing NULL values in that column.
  - Made the column `marketplacePaymentFeePercentage` on table `PaymentIntent` required. This step will fail if there are existing NULL values in that column.
  - Made the column `platformPaymentFee` on table `PaymentIntent` required. This step will fail if there are existing NULL values in that column.
  - Made the column `platformPaymentFeeFixed` on table `PaymentIntent` required. This step will fail if there are existing NULL values in that column.
  - Made the column `platformPaymentFeePercentage` on table `PaymentIntent` required. This step will fail if there are existing NULL values in that column.
  - Made the column `providerFee` on table `PaymentIntent` required. This step will fail if there are existing NULL values in that column.
  - Made the column `providerFeeFixed` on table `PaymentIntent` required. This step will fail if there are existing NULL values in that column.
  - Made the column `providerFeePercentage` on table `PaymentIntent` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Gateway" ALTER COLUMN "feeFixed" SET NOT NULL,
ALTER COLUMN "feePercentage" SET NOT NULL;

-- AlterTable
ALTER TABLE "Payment" ALTER COLUMN "marketplacePaymentFee" SET NOT NULL,
ALTER COLUMN "marketplacePaymentFeeFixed" SET NOT NULL,
ALTER COLUMN "marketplacePaymentFeePercentage" SET NOT NULL,
ALTER COLUMN "platformPaymentFee" SET NOT NULL,
ALTER COLUMN "platformPaymentFeeFixed" SET NOT NULL,
ALTER COLUMN "platformPaymentFeePercentage" SET NOT NULL,
ALTER COLUMN "providerFeeFixed" SET NOT NULL,
ALTER COLUMN "providerFeePercentage" SET NOT NULL;

-- AlterTable
ALTER TABLE "PaymentIntent" ALTER COLUMN "marketplacePaymentFee" SET NOT NULL,
ALTER COLUMN "marketplacePaymentFeeFixed" SET NOT NULL,
ALTER COLUMN "marketplacePaymentFeePercentage" SET NOT NULL,
ALTER COLUMN "platformPaymentFee" SET NOT NULL,
ALTER COLUMN "platformPaymentFeeFixed" SET NOT NULL,
ALTER COLUMN "platformPaymentFeePercentage" SET NOT NULL,
ALTER COLUMN "providerFee" SET NOT NULL,
ALTER COLUMN "providerFeeFixed" SET NOT NULL,
ALTER COLUMN "providerFeePercentage" SET NOT NULL;
