-- CreateTable
CREATE TABLE "PayoutReversal" (
    "id" UUID NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "currency" VARCHAR NOT NULL,
    "payoutId" UUID NOT NULL,
    "status" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PayoutReversal_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "PayoutReversal" ADD CONSTRAINT "PayoutReversal_payoutId_fkey" FOREIGN KEY ("payoutId") REFERENCES "Payout"("id") ON DELETE CASCADE ON UPDATE CASCADE;
