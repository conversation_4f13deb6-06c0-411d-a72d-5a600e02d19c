/*
  Warnings:

  - Added the required column `marketplaceOrderFeeRefunded` to the `Refund` table without a default value. This is not possible if the table is not empty.
  - Added the required column `marketplacePaymentFeeRefunded` to the `Refund` table without a default value. This is not possible if the table is not empty.
  - Added the required column `paymentHubOrderFeeRefunded` to the `Refund` table without a default value. This is not possible if the table is not empty.
  - Added the required column `platformPaymentFeeRefunded` to the `Refund` table without a default value. This is not possible if the table is not empty.
  - Added the required column `providerPaymentFeeRefunded` to the `Refund` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Payment" ADD COLUMN     "marketplaceOrderFeeRefunded" DECIMAL(10,2) NOT NULL DEFAULT 0,
ADD COLUMN     "marketplacePaymentFeeRefunded" DECIMAL(10,2) NOT NULL DEFAULT 0,
ADD COLUMN     "paymentHubOrderFeeRefunded" DECIMAL(10,2) NOT NULL DEFAULT 0,
ADD COLUMN     "platformPaymentFeeRefunded" DECIMAL(10,2) NOT NULL DEFAULT 0,
ADD COLUMN     "providerPaymentFeeRefunded" DECIMAL(10,2) NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Refund" ADD COLUMN     "marketplaceOrderFeeRefunded" DECIMAL(10,2) NOT NULL,
ADD COLUMN     "marketplacePaymentFeeRefunded" DECIMAL(10,2) NOT NULL,
ADD COLUMN     "paymentHubOrderFeeRefunded" DECIMAL(10,2) NOT NULL,
ADD COLUMN     "platformPaymentFeeRefunded" DECIMAL(10,2) NOT NULL,
ADD COLUMN     "providerPaymentFeeRefunded" DECIMAL(10,2) NOT NULL;
