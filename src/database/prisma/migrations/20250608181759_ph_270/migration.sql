-- CreateTable
CREATE TABLE "FeeSettings" (
    "id" TEXT NOT NULL,
    "marketplaceFeeFixed" DECIMAL(10,2) NOT NULL,
    "marketplaceFeePercentage" DECIMAL(5,4) NOT NULL,
    "platformFeeFixed" DECIMAL(10,2) NOT NULL,
    "platformFeePercentage" DECIMAL(5,4) NOT NULL,
    "isActive" BOOLEAN NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "marketplaceGatewayId" TEXT NOT NULL,

    CONSTRAINT "FeeSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "FeeSettings_marketplaceGatewayId_isActive_idx" ON "FeeSettings"("marketplaceGatewayId", "isActive");

-- AddFore<PERSON>Key
ALTER TABLE "FeeSettings" ADD CONSTRAINT "FeeSettings_marketplaceGatewayId_fkey" FOREIGN KEY ("marketplaceGatewayId") REFERENCES "MarketplaceGateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;
