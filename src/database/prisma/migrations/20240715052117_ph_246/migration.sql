/*
  Warnings:

  - The primary key for the `Account` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Customer` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Debt` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Dispute` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Gateway` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `GatewayAccount` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `GatewayCustomer` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `GatewayRefund` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `GatewayTransfer` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `ManagedSecret` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Marketplace` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `MarketplaceGateway` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `MarketplaceGatewayAccount` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `MarketplaceGatewayTransaction` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `transactionId` column on the `MarketplaceGatewayTransaction` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `Payment` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `PaymentIntent` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `PaymentIntentDistribution` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `PaymentMethod` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `PaymentSplit` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `gatewayId` column on the `PaymentSplit` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `PaymentSplitRefund` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `refundId` column on the `PaymentSplitRefund` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `disputeId` column on the `PaymentSplitRefund` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `Payout` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `paymentId` column on the `Payout` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `PayoutReversal` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Refund` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `StripeDispute` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `StripeEvent` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `StripePaymentIntent` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `StripeRefund` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Transaction` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `paymentId` column on the `Transaction` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `accountId` column on the `Transaction` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `refundId` column on the `Transaction` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `paymentSplitId` column on the `Transaction` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `paymentSplitRefundId` column on the `Transaction` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `disputeId` column on the `Transaction` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `Webhook` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - Changed the type of `id` on the `Account` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `marketplaceId` on the `Account` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `Customer` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `marketplaceId` on the `Customer` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `Debt` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `accountId` on the `Debt` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `sourceId` on the `Debt` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `Dispute` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `paymentIntentId` on the `Dispute` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `gatewayId` on the `Dispute` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `Gateway` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `GatewayAccount` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `accountId` on the `GatewayAccount` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `gatewayId` on the `GatewayAccount` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `GatewayCustomer` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `customerId` on the `GatewayCustomer` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `gatewayId` on the `GatewayCustomer` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `GatewayRefund` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `paymentId` on the `GatewayRefund` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `refundId` on the `GatewayRefund` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `GatewayTransfer` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `sourceId` on the `GatewayTransfer` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `destinationAccountId` on the `GatewayTransfer` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `ManagedSecret` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `Marketplace` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `webhookSecretId` on the `Marketplace` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `secretKeyId` on the `Marketplace` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `MarketplaceGateway` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `marketplaceId` on the `MarketplaceGateway` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `gatewayId` on the `MarketplaceGateway` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `MarketplaceGatewayAccount` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `marketplaceGatewayId` on the `MarketplaceGatewayAccount` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `MarketplaceGatewayTransaction` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `marketplaceGatewayId` on the `MarketplaceGatewayTransaction` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `Payment` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `marketplaceId` on the `Payment` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `customerId` on the `Payment` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `gatewayId` on the `Payment` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `paymentIntentId` on the `Payment` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `PaymentIntent` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `marketplaceId` on the `PaymentIntent` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `customerId` on the `PaymentIntent` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `gatewayId` on the `PaymentIntent` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `PaymentIntentDistribution` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `paymentIntentId` on the `PaymentIntentDistribution` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `accountId` on the `PaymentIntentDistribution` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `PaymentMethod` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `gatewayCustomerId` on the `PaymentMethod` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `PaymentSplit` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `accountId` on the `PaymentSplit` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `paymentId` on the `PaymentSplit` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `PaymentSplitRefund` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `accountId` on the `PaymentSplitRefund` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `paymentSplitId` on the `PaymentSplitRefund` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `Payout` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `marketplaceGatewayAccountId` on the `Payout` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `gatewayId` on the `Payout` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `marketplaceId` on the `Payout` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `PayoutReversal` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `payoutId` on the `PayoutReversal` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `Refund` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `paymentId` on the `Refund` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `StripeDispute` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `disputeId` on the `StripeDispute` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `StripeEvent` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `StripePaymentIntent` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `paymentIntentId` on the `StripePaymentIntent` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `StripeRefund` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `refundId` on the `StripeRefund` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `id` on the `Transaction` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `marketplaceId` on the `Webhook` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `gatewayId` on the `Webhook` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- DropForeignKey
ALTER TABLE "Account" DROP CONSTRAINT "Account_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "Customer" DROP CONSTRAINT "Customer_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "Dispute" DROP CONSTRAINT "Dispute_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "Dispute" DROP CONSTRAINT "Dispute_paymentIntentId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayAccount" DROP CONSTRAINT "GatewayAccount_accountId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayAccount" DROP CONSTRAINT "GatewayAccount_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayCustomer" DROP CONSTRAINT "GatewayCustomer_customerId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayCustomer" DROP CONSTRAINT "GatewayCustomer_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayRefund" DROP CONSTRAINT "GatewayRefund_paymentId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayRefund" DROP CONSTRAINT "GatewayRefund_refundId_fkey";

-- DropForeignKey
ALTER TABLE "Marketplace" DROP CONSTRAINT "Marketplace_secretKeyId_fkey";

-- DropForeignKey
ALTER TABLE "Marketplace" DROP CONSTRAINT "Marketplace_webhookSecretId_fkey";

-- DropForeignKey
ALTER TABLE "MarketplaceGateway" DROP CONSTRAINT "MarketplaceGateway_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "MarketplaceGateway" DROP CONSTRAINT "MarketplaceGateway_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "MarketplaceGatewayAccount" DROP CONSTRAINT "MarketplaceGatewayAccount_marketplaceGatewayId_fkey";

-- DropForeignKey
ALTER TABLE "MarketplaceGatewayTransaction" DROP CONSTRAINT "MarketplaceGatewayTransaction_marketplaceGatewayId_fkey";

-- DropForeignKey
ALTER TABLE "MarketplaceGatewayTransaction" DROP CONSTRAINT "MarketplaceGatewayTransaction_transactionId_fkey";

-- DropForeignKey
ALTER TABLE "Payment" DROP CONSTRAINT "Payment_customerId_fkey";

-- DropForeignKey
ALTER TABLE "Payment" DROP CONSTRAINT "Payment_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "Payment" DROP CONSTRAINT "Payment_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "Payment" DROP CONSTRAINT "Payment_paymentIntentId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentIntent" DROP CONSTRAINT "PaymentIntent_customerId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentIntent" DROP CONSTRAINT "PaymentIntent_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentIntent" DROP CONSTRAINT "PaymentIntent_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentIntentDistribution" DROP CONSTRAINT "PaymentIntentDistribution_accountId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentIntentDistribution" DROP CONSTRAINT "PaymentIntentDistribution_paymentIntentId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentMethod" DROP CONSTRAINT "PaymentMethod_gatewayCustomerId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplit" DROP CONSTRAINT "PaymentSplit_accountId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplit" DROP CONSTRAINT "PaymentSplit_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplit" DROP CONSTRAINT "PaymentSplit_paymentId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplitRefund" DROP CONSTRAINT "PaymentSplitRefund_accountId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplitRefund" DROP CONSTRAINT "PaymentSplitRefund_disputeId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplitRefund" DROP CONSTRAINT "PaymentSplitRefund_paymentSplitId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplitRefund" DROP CONSTRAINT "PaymentSplitRefund_refundId_fkey";

-- DropForeignKey
ALTER TABLE "Payout" DROP CONSTRAINT "Payout_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "Payout" DROP CONSTRAINT "Payout_marketplaceGatewayAccountId_fkey";

-- DropForeignKey
ALTER TABLE "Payout" DROP CONSTRAINT "Payout_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "Payout" DROP CONSTRAINT "Payout_paymentId_fkey";

-- DropForeignKey
ALTER TABLE "PayoutReversal" DROP CONSTRAINT "PayoutReversal_payoutId_fkey";

-- DropForeignKey
ALTER TABLE "Refund" DROP CONSTRAINT "Refund_paymentId_fkey";

-- DropForeignKey
ALTER TABLE "StripeDispute" DROP CONSTRAINT "StripeDispute_disputeId_fkey";

-- DropForeignKey
ALTER TABLE "StripePaymentIntent" DROP CONSTRAINT "StripePaymentIntent_paymentIntentId_fkey";

-- DropForeignKey
ALTER TABLE "StripeRefund" DROP CONSTRAINT "StripeRefund_refundId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_accountId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_disputeId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_paymentId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_paymentSplitId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_paymentSplitRefundId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_refundId_fkey";

-- DropForeignKey
ALTER TABLE "Webhook" DROP CONSTRAINT "Webhook_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "Webhook" DROP CONSTRAINT "Webhook_marketplaceId_fkey";

-- AlterTable
ALTER TABLE "Account" DROP CONSTRAINT "Account_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "marketplaceId",
ADD COLUMN     "marketplaceId" BYTEA NOT NULL,
ADD CONSTRAINT "Account_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Customer" DROP CONSTRAINT "Customer_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "marketplaceId",
ADD COLUMN     "marketplaceId" BYTEA NOT NULL,
ADD CONSTRAINT "Customer_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Debt" DROP CONSTRAINT "Debt_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "accountId",
ADD COLUMN     "accountId" BYTEA NOT NULL,
DROP COLUMN "sourceId",
ADD COLUMN     "sourceId" BYTEA NOT NULL,
ADD CONSTRAINT "Debt_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Dispute" DROP CONSTRAINT "Dispute_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "paymentIntentId",
ADD COLUMN     "paymentIntentId" BYTEA NOT NULL,
DROP COLUMN "gatewayId",
ADD COLUMN     "gatewayId" BYTEA NOT NULL,
ADD CONSTRAINT "Dispute_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Gateway" DROP CONSTRAINT "Gateway_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
ADD CONSTRAINT "Gateway_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "GatewayAccount" DROP CONSTRAINT "GatewayAccount_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "accountId",
ADD COLUMN     "accountId" BYTEA NOT NULL,
DROP COLUMN "gatewayId",
ADD COLUMN     "gatewayId" BYTEA NOT NULL,
ADD CONSTRAINT "GatewayAccount_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "GatewayCustomer" DROP CONSTRAINT "GatewayCustomer_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "customerId",
ADD COLUMN     "customerId" BYTEA NOT NULL,
DROP COLUMN "gatewayId",
ADD COLUMN     "gatewayId" BYTEA NOT NULL,
ADD CONSTRAINT "GatewayCustomer_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "GatewayRefund" DROP CONSTRAINT "GatewayRefund_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "paymentId",
ADD COLUMN     "paymentId" BYTEA NOT NULL,
DROP COLUMN "refundId",
ADD COLUMN     "refundId" BYTEA NOT NULL,
ADD CONSTRAINT "GatewayRefund_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "GatewayTransfer" DROP CONSTRAINT "GatewayTransfer_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "sourceId",
ADD COLUMN     "sourceId" BYTEA NOT NULL,
DROP COLUMN "destinationAccountId",
ADD COLUMN     "destinationAccountId" BYTEA NOT NULL,
ADD CONSTRAINT "GatewayTransfer_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "ManagedSecret" DROP CONSTRAINT "ManagedSecret_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
ADD CONSTRAINT "ManagedSecret_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Marketplace" DROP CONSTRAINT "Marketplace_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "webhookSecretId",
ADD COLUMN     "webhookSecretId" BYTEA NOT NULL,
DROP COLUMN "secretKeyId",
ADD COLUMN     "secretKeyId" BYTEA NOT NULL,
ADD CONSTRAINT "Marketplace_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "MarketplaceGateway" DROP CONSTRAINT "MarketplaceGateway_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "marketplaceId",
ADD COLUMN     "marketplaceId" BYTEA NOT NULL,
DROP COLUMN "gatewayId",
ADD COLUMN     "gatewayId" BYTEA NOT NULL,
ADD CONSTRAINT "MarketplaceGateway_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "MarketplaceGatewayAccount" DROP CONSTRAINT "MarketplaceGatewayAccount_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "marketplaceGatewayId",
ADD COLUMN     "marketplaceGatewayId" BYTEA NOT NULL,
ADD CONSTRAINT "MarketplaceGatewayAccount_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "MarketplaceGatewayTransaction" DROP CONSTRAINT "MarketplaceGatewayTransaction_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "marketplaceGatewayId",
ADD COLUMN     "marketplaceGatewayId" BYTEA NOT NULL,
DROP COLUMN "transactionId",
ADD COLUMN     "transactionId" BYTEA,
ADD CONSTRAINT "MarketplaceGatewayTransaction_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Payment" DROP CONSTRAINT "Payment_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "marketplaceId",
ADD COLUMN     "marketplaceId" BYTEA NOT NULL,
DROP COLUMN "customerId",
ADD COLUMN     "customerId" BYTEA NOT NULL,
DROP COLUMN "gatewayId",
ADD COLUMN     "gatewayId" BYTEA NOT NULL,
DROP COLUMN "paymentIntentId",
ADD COLUMN     "paymentIntentId" BYTEA NOT NULL,
ADD CONSTRAINT "Payment_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "PaymentIntent" DROP CONSTRAINT "PaymentIntent_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "marketplaceId",
ADD COLUMN     "marketplaceId" BYTEA NOT NULL,
DROP COLUMN "customerId",
ADD COLUMN     "customerId" BYTEA NOT NULL,
DROP COLUMN "gatewayId",
ADD COLUMN     "gatewayId" BYTEA NOT NULL,
ADD CONSTRAINT "PaymentIntent_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "PaymentIntentDistribution" DROP CONSTRAINT "PaymentIntentDistribution_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "paymentIntentId",
ADD COLUMN     "paymentIntentId" BYTEA NOT NULL,
DROP COLUMN "accountId",
ADD COLUMN     "accountId" BYTEA NOT NULL,
ADD CONSTRAINT "PaymentIntentDistribution_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "PaymentMethod" DROP CONSTRAINT "PaymentMethod_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "gatewayCustomerId",
ADD COLUMN     "gatewayCustomerId" BYTEA NOT NULL,
ADD CONSTRAINT "PaymentMethod_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "PaymentSplit" DROP CONSTRAINT "PaymentSplit_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "accountId",
ADD COLUMN     "accountId" BYTEA NOT NULL,
DROP COLUMN "paymentId",
ADD COLUMN     "paymentId" BYTEA NOT NULL,
DROP COLUMN "gatewayId",
ADD COLUMN     "gatewayId" BYTEA,
ADD CONSTRAINT "PaymentSplit_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "PaymentSplitRefund" DROP CONSTRAINT "PaymentSplitRefund_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "accountId",
ADD COLUMN     "accountId" BYTEA NOT NULL,
DROP COLUMN "refundId",
ADD COLUMN     "refundId" BYTEA,
DROP COLUMN "paymentSplitId",
ADD COLUMN     "paymentSplitId" BYTEA NOT NULL,
DROP COLUMN "disputeId",
ADD COLUMN     "disputeId" BYTEA,
ADD CONSTRAINT "PaymentSplitRefund_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Payout" DROP CONSTRAINT "Payout_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "marketplaceGatewayAccountId",
ADD COLUMN     "marketplaceGatewayAccountId" BYTEA NOT NULL,
DROP COLUMN "gatewayId",
ADD COLUMN     "gatewayId" BYTEA NOT NULL,
DROP COLUMN "marketplaceId",
ADD COLUMN     "marketplaceId" BYTEA NOT NULL,
DROP COLUMN "paymentId",
ADD COLUMN     "paymentId" BYTEA,
ADD CONSTRAINT "Payout_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "PayoutReversal" DROP CONSTRAINT "PayoutReversal_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "payoutId",
ADD COLUMN     "payoutId" BYTEA NOT NULL,
ADD CONSTRAINT "PayoutReversal_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Refund" DROP CONSTRAINT "Refund_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "paymentId",
ADD COLUMN     "paymentId" BYTEA NOT NULL,
ADD CONSTRAINT "Refund_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "StripeDispute" DROP CONSTRAINT "StripeDispute_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "disputeId",
ADD COLUMN     "disputeId" BYTEA NOT NULL,
ADD CONSTRAINT "StripeDispute_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "StripeEvent" DROP CONSTRAINT "StripeEvent_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
ADD CONSTRAINT "StripeEvent_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "StripePaymentIntent" DROP CONSTRAINT "StripePaymentIntent_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "paymentIntentId",
ADD COLUMN     "paymentIntentId" BYTEA NOT NULL,
ADD CONSTRAINT "StripePaymentIntent_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "StripeRefund" DROP CONSTRAINT "StripeRefund_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "refundId",
ADD COLUMN     "refundId" BYTEA NOT NULL,
ADD CONSTRAINT "StripeRefund_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" BYTEA NOT NULL,
DROP COLUMN "paymentId",
ADD COLUMN     "paymentId" BYTEA,
DROP COLUMN "accountId",
ADD COLUMN     "accountId" BYTEA,
DROP COLUMN "refundId",
ADD COLUMN     "refundId" BYTEA,
DROP COLUMN "paymentSplitId",
ADD COLUMN     "paymentSplitId" BYTEA,
DROP COLUMN "paymentSplitRefundId",
ADD COLUMN     "paymentSplitRefundId" BYTEA,
DROP COLUMN "disputeId",
ADD COLUMN     "disputeId" BYTEA,
ADD CONSTRAINT "Transaction_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Webhook" DROP CONSTRAINT "Webhook_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
DROP COLUMN "marketplaceId",
ADD COLUMN     "marketplaceId" BYTEA NOT NULL,
DROP COLUMN "gatewayId",
ADD COLUMN     "gatewayId" BYTEA NOT NULL,
ADD CONSTRAINT "Webhook_pkey" PRIMARY KEY ("id");

-- CreateIndex
CREATE UNIQUE INDEX "GatewayAccount_accountId_gatewayId_idAtGateway_idx" ON "GatewayAccount"("accountId", "gatewayId", "idAtGateway");

-- CreateIndex
CREATE UNIQUE INDEX "GatewayCustomer_customerId_gatewayId_idx" ON "GatewayCustomer"("customerId", "gatewayId");

-- CreateIndex
CREATE UNIQUE INDEX "GatewayRefund_refundId_key" ON "GatewayRefund"("refundId");

-- CreateIndex
CREATE UNIQUE INDEX "Payment_paymentIntentId_key" ON "Payment"("paymentIntentId");

-- CreateIndex
CREATE UNIQUE INDEX "PaymentIntentDistribution_paymentIntentId_accountId_idx" ON "PaymentIntentDistribution"("paymentIntentId", "accountId");

-- CreateIndex
CREATE UNIQUE INDEX "StripeDispute_disputeId_key" ON "StripeDispute"("disputeId");

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Customer" ADD CONSTRAINT "Customer_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayAccount" ADD CONSTRAINT "GatewayAccount_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayAccount" ADD CONSTRAINT "GatewayAccount_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayCustomer" ADD CONSTRAINT "GatewayCustomer_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayCustomer" ADD CONSTRAINT "GatewayCustomer_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Marketplace" ADD CONSTRAINT "Marketplace_webhookSecretId_fkey" FOREIGN KEY ("webhookSecretId") REFERENCES "ManagedSecret"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Marketplace" ADD CONSTRAINT "Marketplace_secretKeyId_fkey" FOREIGN KEY ("secretKeyId") REFERENCES "ManagedSecret"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGateway" ADD CONSTRAINT "MarketplaceGateway_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGateway" ADD CONSTRAINT "MarketplaceGateway_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGatewayTransaction" ADD CONSTRAINT "MarketplaceGatewayTransaction_marketplaceGatewayId_fkey" FOREIGN KEY ("marketplaceGatewayId") REFERENCES "MarketplaceGateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGatewayTransaction" ADD CONSTRAINT "MarketplaceGatewayTransaction_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "Transaction"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_paymentIntentId_fkey" FOREIGN KEY ("paymentIntentId") REFERENCES "PaymentIntent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentIntent" ADD CONSTRAINT "PaymentIntent_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentIntent" ADD CONSTRAINT "PaymentIntent_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentIntent" ADD CONSTRAINT "PaymentIntent_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentIntentDistribution" ADD CONSTRAINT "PaymentIntentDistribution_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentIntentDistribution" ADD CONSTRAINT "PaymentIntentDistribution_paymentIntentId_fkey" FOREIGN KEY ("paymentIntentId") REFERENCES "PaymentIntent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentMethod" ADD CONSTRAINT "PaymentMethod_gatewayCustomerId_fkey" FOREIGN KEY ("gatewayCustomerId") REFERENCES "GatewayCustomer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplit" ADD CONSTRAINT "PaymentSplit_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplit" ADD CONSTRAINT "PaymentSplit_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplit" ADD CONSTRAINT "PaymentSplit_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplitRefund" ADD CONSTRAINT "PaymentSplitRefund_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplitRefund" ADD CONSTRAINT "PaymentSplitRefund_paymentSplitId_fkey" FOREIGN KEY ("paymentSplitId") REFERENCES "PaymentSplit"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplitRefund" ADD CONSTRAINT "PaymentSplitRefund_refundId_fkey" FOREIGN KEY ("refundId") REFERENCES "Refund"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplitRefund" ADD CONSTRAINT "PaymentSplitRefund_disputeId_fkey" FOREIGN KEY ("disputeId") REFERENCES "Dispute"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Refund" ADD CONSTRAINT "Refund_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StripePaymentIntent" ADD CONSTRAINT "StripePaymentIntent_paymentIntentId_fkey" FOREIGN KEY ("paymentIntentId") REFERENCES "PaymentIntent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StripeRefund" ADD CONSTRAINT "StripeRefund_refundId_fkey" FOREIGN KEY ("refundId") REFERENCES "Refund"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_disputeId_fkey" FOREIGN KEY ("disputeId") REFERENCES "Dispute"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_paymentSplitId_fkey" FOREIGN KEY ("paymentSplitId") REFERENCES "PaymentSplit"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_paymentSplitRefundId_fkey" FOREIGN KEY ("paymentSplitRefundId") REFERENCES "PaymentSplitRefund"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_refundId_fkey" FOREIGN KEY ("refundId") REFERENCES "Refund"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Webhook" ADD CONSTRAINT "Webhook_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Webhook" ADD CONSTRAINT "Webhook_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayRefund" ADD CONSTRAINT "GatewayRefund_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayRefund" ADD CONSTRAINT "GatewayRefund_refundId_fkey" FOREIGN KEY ("refundId") REFERENCES "Refund"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StripeDispute" ADD CONSTRAINT "StripeDispute_disputeId_fkey" FOREIGN KEY ("disputeId") REFERENCES "Dispute"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Dispute" ADD CONSTRAINT "Dispute_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Dispute" ADD CONSTRAINT "Dispute_paymentIntentId_fkey" FOREIGN KEY ("paymentIntentId") REFERENCES "PaymentIntent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGatewayAccount" ADD CONSTRAINT "MarketplaceGatewayAccount_marketplaceGatewayId_fkey" FOREIGN KEY ("marketplaceGatewayId") REFERENCES "MarketplaceGateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payout" ADD CONSTRAINT "Payout_marketplaceGatewayAccountId_fkey" FOREIGN KEY ("marketplaceGatewayAccountId") REFERENCES "MarketplaceGatewayAccount"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payout" ADD CONSTRAINT "Payout_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payout" ADD CONSTRAINT "Payout_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payout" ADD CONSTRAINT "Payout_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PayoutReversal" ADD CONSTRAINT "PayoutReversal_payoutId_fkey" FOREIGN KEY ("payoutId") REFERENCES "Payout"("id") ON DELETE CASCADE ON UPDATE CASCADE;
