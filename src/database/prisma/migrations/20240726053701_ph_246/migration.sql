/*
  Warnings:

  - The primary key for the `Account` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Customer` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Debt` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Dispute` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Gateway` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `GatewayAccount` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `GatewayCustomer` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `GatewayRefund` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `GatewayTransfer` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `ManagedSecret` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Marketplace` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `MarketplaceGateway` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `MarketplaceGatewayAccount` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `MarketplaceGatewayTransaction` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Payment` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `PaymentIntent` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `PaymentIntentDistribution` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `PaymentMethod` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `PaymentSplit` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `PaymentSplitRefund` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Payout` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `PayoutReversal` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Refund` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `StripeDispute` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `StripePaymentIntent` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `StripeRefund` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Transaction` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- DropForeignKey
ALTER TABLE "Account" DROP CONSTRAINT "Account_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "Customer" DROP CONSTRAINT "Customer_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "Dispute" DROP CONSTRAINT "Dispute_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "Dispute" DROP CONSTRAINT "Dispute_paymentIntentId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayAccount" DROP CONSTRAINT "GatewayAccount_accountId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayAccount" DROP CONSTRAINT "GatewayAccount_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayCustomer" DROP CONSTRAINT "GatewayCustomer_customerId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayCustomer" DROP CONSTRAINT "GatewayCustomer_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayRefund" DROP CONSTRAINT "GatewayRefund_paymentId_fkey";

-- DropForeignKey
ALTER TABLE "GatewayRefund" DROP CONSTRAINT "GatewayRefund_refundId_fkey";

-- DropForeignKey
ALTER TABLE "Marketplace" DROP CONSTRAINT "Marketplace_secretKeyId_fkey";

-- DropForeignKey
ALTER TABLE "Marketplace" DROP CONSTRAINT "Marketplace_webhookSecretId_fkey";

-- DropForeignKey
ALTER TABLE "MarketplaceGateway" DROP CONSTRAINT "MarketplaceGateway_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "MarketplaceGateway" DROP CONSTRAINT "MarketplaceGateway_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "MarketplaceGatewayAccount" DROP CONSTRAINT "MarketplaceGatewayAccount_marketplaceGatewayId_fkey";

-- DropForeignKey
ALTER TABLE "MarketplaceGatewayTransaction" DROP CONSTRAINT "MarketplaceGatewayTransaction_marketplaceGatewayId_fkey";

-- DropForeignKey
ALTER TABLE "MarketplaceGatewayTransaction" DROP CONSTRAINT "MarketplaceGatewayTransaction_transactionId_fkey";

-- DropForeignKey
ALTER TABLE "Payment" DROP CONSTRAINT "Payment_customerId_fkey";

-- DropForeignKey
ALTER TABLE "Payment" DROP CONSTRAINT "Payment_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "Payment" DROP CONSTRAINT "Payment_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "Payment" DROP CONSTRAINT "Payment_paymentIntentId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentIntent" DROP CONSTRAINT "PaymentIntent_customerId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentIntent" DROP CONSTRAINT "PaymentIntent_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentIntent" DROP CONSTRAINT "PaymentIntent_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentIntentDistribution" DROP CONSTRAINT "PaymentIntentDistribution_accountId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentIntentDistribution" DROP CONSTRAINT "PaymentIntentDistribution_paymentIntentId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentMethod" DROP CONSTRAINT "PaymentMethod_gatewayCustomerId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplit" DROP CONSTRAINT "PaymentSplit_accountId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplit" DROP CONSTRAINT "PaymentSplit_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplit" DROP CONSTRAINT "PaymentSplit_paymentId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplitRefund" DROP CONSTRAINT "PaymentSplitRefund_accountId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplitRefund" DROP CONSTRAINT "PaymentSplitRefund_disputeId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplitRefund" DROP CONSTRAINT "PaymentSplitRefund_paymentSplitId_fkey";

-- DropForeignKey
ALTER TABLE "PaymentSplitRefund" DROP CONSTRAINT "PaymentSplitRefund_refundId_fkey";

-- DropForeignKey
ALTER TABLE "Payout" DROP CONSTRAINT "Payout_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "Payout" DROP CONSTRAINT "Payout_marketplaceGatewayAccountId_fkey";

-- DropForeignKey
ALTER TABLE "Payout" DROP CONSTRAINT "Payout_marketplaceId_fkey";

-- DropForeignKey
ALTER TABLE "Payout" DROP CONSTRAINT "Payout_paymentId_fkey";

-- DropForeignKey
ALTER TABLE "PayoutReversal" DROP CONSTRAINT "PayoutReversal_payoutId_fkey";

-- DropForeignKey
ALTER TABLE "Refund" DROP CONSTRAINT "Refund_paymentId_fkey";

-- DropForeignKey
ALTER TABLE "StripeDispute" DROP CONSTRAINT "StripeDispute_disputeId_fkey";

-- DropForeignKey
ALTER TABLE "StripePaymentIntent" DROP CONSTRAINT "StripePaymentIntent_paymentIntentId_fkey";

-- DropForeignKey
ALTER TABLE "StripeRefund" DROP CONSTRAINT "StripeRefund_refundId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_accountId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_disputeId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_paymentId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_paymentSplitId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_paymentSplitRefundId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_refundId_fkey";

-- DropForeignKey
ALTER TABLE "Webhook" DROP CONSTRAINT "Webhook_gatewayId_fkey";

-- DropForeignKey
ALTER TABLE "Webhook" DROP CONSTRAINT "Webhook_marketplaceId_fkey";

-- AlterTable
ALTER TABLE "Account" DROP CONSTRAINT "Account_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "marketplaceId" SET DATA TYPE TEXT,
ADD CONSTRAINT "Account_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Customer" DROP CONSTRAINT "Customer_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "marketplaceId" SET DATA TYPE TEXT,
ADD CONSTRAINT "Customer_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Debt" DROP CONSTRAINT "Debt_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "accountId" SET DATA TYPE TEXT,
ALTER COLUMN "sourceId" SET DATA TYPE TEXT,
ADD CONSTRAINT "Debt_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Dispute" DROP CONSTRAINT "Dispute_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "paymentIntentId" SET DATA TYPE TEXT,
ALTER COLUMN "gatewayId" SET DATA TYPE TEXT,
ADD CONSTRAINT "Dispute_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Gateway" DROP CONSTRAINT "Gateway_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "Gateway_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "GatewayAccount" DROP CONSTRAINT "GatewayAccount_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "accountId" SET DATA TYPE TEXT,
ALTER COLUMN "gatewayId" SET DATA TYPE TEXT,
ADD CONSTRAINT "GatewayAccount_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "GatewayCustomer" DROP CONSTRAINT "GatewayCustomer_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "customerId" SET DATA TYPE TEXT,
ALTER COLUMN "gatewayId" SET DATA TYPE TEXT,
ADD CONSTRAINT "GatewayCustomer_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "GatewayRefund" DROP CONSTRAINT "GatewayRefund_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "paymentId" SET DATA TYPE TEXT,
ALTER COLUMN "refundId" SET DATA TYPE TEXT,
ADD CONSTRAINT "GatewayRefund_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "GatewayTransfer" DROP CONSTRAINT "GatewayTransfer_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "sourceId" SET DATA TYPE TEXT,
ALTER COLUMN "destinationAccountId" SET DATA TYPE TEXT,
ADD CONSTRAINT "GatewayTransfer_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "ManagedSecret" DROP CONSTRAINT "ManagedSecret_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "ManagedSecret_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Marketplace" DROP CONSTRAINT "Marketplace_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "webhookSecretId" SET DATA TYPE TEXT,
ALTER COLUMN "secretKeyId" SET DATA TYPE TEXT,
ADD CONSTRAINT "Marketplace_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "MarketplaceGateway" DROP CONSTRAINT "MarketplaceGateway_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "marketplaceId" SET DATA TYPE TEXT,
ALTER COLUMN "gatewayId" SET DATA TYPE TEXT,
ADD CONSTRAINT "MarketplaceGateway_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "MarketplaceGatewayAccount" DROP CONSTRAINT "MarketplaceGatewayAccount_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "marketplaceGatewayId" SET DATA TYPE TEXT,
ADD CONSTRAINT "MarketplaceGatewayAccount_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "MarketplaceGatewayTransaction" DROP CONSTRAINT "MarketplaceGatewayTransaction_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "marketplaceGatewayId" SET DATA TYPE TEXT,
ALTER COLUMN "transactionId" SET DATA TYPE TEXT,
ADD CONSTRAINT "MarketplaceGatewayTransaction_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Payment" DROP CONSTRAINT "Payment_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "marketplaceId" SET DATA TYPE TEXT,
ALTER COLUMN "customerId" SET DATA TYPE TEXT,
ALTER COLUMN "gatewayId" SET DATA TYPE TEXT,
ALTER COLUMN "paymentIntentId" SET DATA TYPE TEXT,
ADD CONSTRAINT "Payment_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "PaymentIntent" DROP CONSTRAINT "PaymentIntent_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "marketplaceId" SET DATA TYPE TEXT,
ALTER COLUMN "customerId" SET DATA TYPE TEXT,
ALTER COLUMN "gatewayId" SET DATA TYPE TEXT,
ADD CONSTRAINT "PaymentIntent_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "PaymentIntentDistribution" DROP CONSTRAINT "PaymentIntentDistribution_pkey",
ALTER COLUMN "paymentIntentId" SET DATA TYPE TEXT,
ALTER COLUMN "accountId" SET DATA TYPE TEXT,
ADD CONSTRAINT "PaymentIntentDistribution_pkey" PRIMARY KEY ("paymentIntentId", "accountId");

-- AlterTable
ALTER TABLE "PaymentMethod" DROP CONSTRAINT "PaymentMethod_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "gatewayCustomerId" SET DATA TYPE TEXT,
ADD CONSTRAINT "PaymentMethod_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "PaymentSplit" DROP CONSTRAINT "PaymentSplit_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "accountId" SET DATA TYPE TEXT,
ALTER COLUMN "paymentId" SET DATA TYPE TEXT,
ALTER COLUMN "gatewayId" SET DATA TYPE TEXT,
ADD CONSTRAINT "PaymentSplit_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "PaymentSplitRefund" DROP CONSTRAINT "PaymentSplitRefund_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "accountId" SET DATA TYPE TEXT,
ALTER COLUMN "refundId" SET DATA TYPE TEXT,
ALTER COLUMN "paymentSplitId" SET DATA TYPE TEXT,
ALTER COLUMN "disputeId" SET DATA TYPE TEXT,
ADD CONSTRAINT "PaymentSplitRefund_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Payout" DROP CONSTRAINT "Payout_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "marketplaceGatewayAccountId" SET DATA TYPE TEXT,
ALTER COLUMN "gatewayId" SET DATA TYPE TEXT,
ALTER COLUMN "marketplaceId" SET DATA TYPE TEXT,
ALTER COLUMN "paymentId" SET DATA TYPE TEXT,
ADD CONSTRAINT "Payout_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "PayoutReversal" DROP CONSTRAINT "PayoutReversal_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "payoutId" SET DATA TYPE TEXT,
ADD CONSTRAINT "PayoutReversal_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Refund" DROP CONSTRAINT "Refund_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "paymentId" SET DATA TYPE TEXT,
ADD CONSTRAINT "Refund_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "StripeDispute" DROP CONSTRAINT "StripeDispute_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "disputeId" SET DATA TYPE TEXT,
ADD CONSTRAINT "StripeDispute_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "StripePaymentIntent" DROP CONSTRAINT "StripePaymentIntent_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "paymentIntentId" SET DATA TYPE TEXT,
ADD CONSTRAINT "StripePaymentIntent_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "StripeRefund" DROP CONSTRAINT "StripeRefund_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "refundId" SET DATA TYPE TEXT,
ADD CONSTRAINT "StripeRefund_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "paymentId" SET DATA TYPE TEXT,
ALTER COLUMN "accountId" SET DATA TYPE TEXT,
ALTER COLUMN "refundId" SET DATA TYPE TEXT,
ALTER COLUMN "paymentSplitId" SET DATA TYPE TEXT,
ALTER COLUMN "paymentSplitRefundId" SET DATA TYPE TEXT,
ALTER COLUMN "disputeId" SET DATA TYPE TEXT,
ADD CONSTRAINT "Transaction_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Webhook" ALTER COLUMN "marketplaceId" SET DATA TYPE TEXT,
ALTER COLUMN "gatewayId" SET DATA TYPE TEXT;

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Customer" ADD CONSTRAINT "Customer_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayAccount" ADD CONSTRAINT "GatewayAccount_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayAccount" ADD CONSTRAINT "GatewayAccount_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayCustomer" ADD CONSTRAINT "GatewayCustomer_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayCustomer" ADD CONSTRAINT "GatewayCustomer_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Marketplace" ADD CONSTRAINT "Marketplace_webhookSecretId_fkey" FOREIGN KEY ("webhookSecretId") REFERENCES "ManagedSecret"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Marketplace" ADD CONSTRAINT "Marketplace_secretKeyId_fkey" FOREIGN KEY ("secretKeyId") REFERENCES "ManagedSecret"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGateway" ADD CONSTRAINT "MarketplaceGateway_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGateway" ADD CONSTRAINT "MarketplaceGateway_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGatewayTransaction" ADD CONSTRAINT "MarketplaceGatewayTransaction_marketplaceGatewayId_fkey" FOREIGN KEY ("marketplaceGatewayId") REFERENCES "MarketplaceGateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGatewayTransaction" ADD CONSTRAINT "MarketplaceGatewayTransaction_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "Transaction"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_paymentIntentId_fkey" FOREIGN KEY ("paymentIntentId") REFERENCES "PaymentIntent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentIntent" ADD CONSTRAINT "PaymentIntent_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentIntent" ADD CONSTRAINT "PaymentIntent_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentIntent" ADD CONSTRAINT "PaymentIntent_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentIntentDistribution" ADD CONSTRAINT "PaymentIntentDistribution_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentIntentDistribution" ADD CONSTRAINT "PaymentIntentDistribution_paymentIntentId_fkey" FOREIGN KEY ("paymentIntentId") REFERENCES "PaymentIntent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentMethod" ADD CONSTRAINT "PaymentMethod_gatewayCustomerId_fkey" FOREIGN KEY ("gatewayCustomerId") REFERENCES "GatewayCustomer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplit" ADD CONSTRAINT "PaymentSplit_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplit" ADD CONSTRAINT "PaymentSplit_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplit" ADD CONSTRAINT "PaymentSplit_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplitRefund" ADD CONSTRAINT "PaymentSplitRefund_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplitRefund" ADD CONSTRAINT "PaymentSplitRefund_paymentSplitId_fkey" FOREIGN KEY ("paymentSplitId") REFERENCES "PaymentSplit"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplitRefund" ADD CONSTRAINT "PaymentSplitRefund_refundId_fkey" FOREIGN KEY ("refundId") REFERENCES "Refund"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentSplitRefund" ADD CONSTRAINT "PaymentSplitRefund_disputeId_fkey" FOREIGN KEY ("disputeId") REFERENCES "Dispute"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Refund" ADD CONSTRAINT "Refund_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StripePaymentIntent" ADD CONSTRAINT "StripePaymentIntent_paymentIntentId_fkey" FOREIGN KEY ("paymentIntentId") REFERENCES "PaymentIntent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StripeRefund" ADD CONSTRAINT "StripeRefund_refundId_fkey" FOREIGN KEY ("refundId") REFERENCES "Refund"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_disputeId_fkey" FOREIGN KEY ("disputeId") REFERENCES "Dispute"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_paymentSplitId_fkey" FOREIGN KEY ("paymentSplitId") REFERENCES "PaymentSplit"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_paymentSplitRefundId_fkey" FOREIGN KEY ("paymentSplitRefundId") REFERENCES "PaymentSplitRefund"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_refundId_fkey" FOREIGN KEY ("refundId") REFERENCES "Refund"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Webhook" ADD CONSTRAINT "Webhook_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Webhook" ADD CONSTRAINT "Webhook_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayRefund" ADD CONSTRAINT "GatewayRefund_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GatewayRefund" ADD CONSTRAINT "GatewayRefund_refundId_fkey" FOREIGN KEY ("refundId") REFERENCES "Refund"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StripeDispute" ADD CONSTRAINT "StripeDispute_disputeId_fkey" FOREIGN KEY ("disputeId") REFERENCES "Dispute"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Dispute" ADD CONSTRAINT "Dispute_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Dispute" ADD CONSTRAINT "Dispute_paymentIntentId_fkey" FOREIGN KEY ("paymentIntentId") REFERENCES "PaymentIntent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGatewayAccount" ADD CONSTRAINT "MarketplaceGatewayAccount_marketplaceGatewayId_fkey" FOREIGN KEY ("marketplaceGatewayId") REFERENCES "MarketplaceGateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payout" ADD CONSTRAINT "Payout_marketplaceGatewayAccountId_fkey" FOREIGN KEY ("marketplaceGatewayAccountId") REFERENCES "MarketplaceGatewayAccount"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payout" ADD CONSTRAINT "Payout_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payout" ADD CONSTRAINT "Payout_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payout" ADD CONSTRAINT "Payout_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PayoutReversal" ADD CONSTRAINT "PayoutReversal_payoutId_fkey" FOREIGN KEY ("payoutId") REFERENCES "Payout"("id") ON DELETE CASCADE ON UPDATE CASCADE;
