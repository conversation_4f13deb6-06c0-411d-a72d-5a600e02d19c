-- CreateTable
CREATE TABLE "StripeDispute" (
    "id" UUID NOT NULL,
    "disputeId" VARCHAR NOT NULL,
    "paymentIntentId" UUID NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "currency" VARCHAR NOT NULL,
    "status" VARCHAR NOT NULL,
    "reason" VARCHAR NOT NULL,
    "evidence" JSONB NOT NULL,
    "evidenceDetails" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "StripeDispute_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "StripeDispute_disputeId_key" ON "StripeDispute"("disputeId");

-- AddForeignKey
ALTER TABLE "StripeDispute" ADD CONSTRAINT "StripeDispute_paymentIntentId_fkey" FOREIGN KEY ("paymentIntentId") REFERENCES "StripePaymentIntent"("id") ON DELETE CASCADE ON UPDATE CASCADE;
