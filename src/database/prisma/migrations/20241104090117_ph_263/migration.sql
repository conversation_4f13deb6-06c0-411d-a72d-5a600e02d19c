/*
  Warnings:

  - You are about to drop the column `cardExpiryDate` on the `PaymentMethod` table. All the data in the column will be lost.
  - You are about to drop the column `cardLastFour` on the `PaymentMethod` table. All the data in the column will be lost.
  - You are about to drop the column `gatewayCustomerId` on the `PaymentMethod` table. All the data in the column will be lost.
  - You are about to drop the column `gatewayToken` on the `PaymentMethod` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "PaymentMethod" DROP CONSTRAINT "PaymentMethod_gatewayCustomerId_fkey";

-- AlterTable
ALTER TABLE "PaymentMethod" DROP COLUMN "cardExpiryDate",
DROP COLUMN "cardLastFour",
DROP COLUMN "gatewayCustomerId",
DROP COLUMN "gatewayToken",
ADD COLUMN     "cardLast4" VARCHAR;

-- CreateTable
CREATE TABLE "GatewayPaymentMethod" (
    "id" TEXT NOT NULL,
    "idAtGateway" TEXT NOT NULL,
    "paymentMethodId" TEXT NOT NULL,
    "fingerprint" TEXT,

    CONSTRAINT "GatewayPaymentMethod_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "GatewayPaymentMethod_paymentMethodId_key" ON "GatewayPaymentMethod"("paymentMethodId");

-- AddForeignKey
ALTER TABLE "GatewayPaymentMethod" ADD CONSTRAINT "GatewayPaymentMethod_paymentMethodId_fkey" FOREIGN KEY ("paymentMethodId") REFERENCES "PaymentMethod"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
