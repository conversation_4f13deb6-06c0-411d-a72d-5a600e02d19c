/*
  Warnings:

  - You are about to drop the column `isActive` on the `FeeSettings` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[marketplaceGatewayId,isDefault]` on the table `FeeSettings` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `isDefault` to the `FeeSettings` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "FeeSettings_marketplaceGatewayId_isActive_idx";

-- AlterTable
ALTER TABLE "FeeSettings" DROP COLUMN "isActive",
ADD COLUMN     "isDefault" BOOLEAN NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "FeeSettings_marketplaceGatewayId_isDefault_idx" ON "FeeSettings"("marketplaceGatewayId", "isDefault");
