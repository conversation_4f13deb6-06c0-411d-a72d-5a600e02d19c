-- CreateTable
CREATE TABLE "MarketplaceGateway" (
    "id" UUID NOT NULL,
    "balance" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "currency" VARCHAR NOT NULL,
    "marketplaceId" UUID NOT NULL,
    "gatewayId" UUID NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MarketplaceGateway_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarketplaceGatewayTransaction" (
    "id" UUID NOT NULL,
    "marketplaceGatewayId" UUID NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "currency" VARCHAR NOT NULL,
    "transactionId" UUID,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MarketplaceGatewayTransaction_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "MarketplaceGateway" ADD CONSTRAINT "MarketplaceGateway_marketplaceId_fkey" FOREIGN KEY ("marketplaceId") REFERENCES "Marketplace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGateway" ADD CONSTRAINT "MarketplaceGateway_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGatewayTransaction" ADD CONSTRAINT "MarketplaceGatewayTransaction_marketplaceGatewayId_fkey" FOREIGN KEY ("marketplaceGatewayId") REFERENCES "MarketplaceGateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceGatewayTransaction" ADD CONSTRAINT "MarketplaceGatewayTransaction_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "Transaction"("id") ON DELETE CASCADE ON UPDATE CASCADE;
