-- CreateTable
CREATE TABLE "MarketplaceGatewayAccount" (
    "id" UUID NOT NULL,
    "name" VARCHAR,
    "idAtGateway" TEXT NOT NULL,
    "marketplaceGatewayId" UUID NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MarketplaceGatewayAccount_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "MarketplaceGatewayAccount" ADD CONSTRAINT "MarketplaceGatewayAccount_marketplaceGatewayId_fkey" FOREIGN KEY ("marketplaceGatewayId") REFERENCES "MarketplaceGateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;
