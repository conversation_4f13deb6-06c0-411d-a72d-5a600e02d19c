/*
  Warnings:

  - You are about to drop the column `amount` on the `StripeDispute` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `StripeDispute` table. All the data in the column will be lost.
  - You are about to drop the column `currency` on the `StripeDispute` table. All the data in the column will be lost.
  - You are about to drop the column `evidence` on the `StripeDispute` table. All the data in the column will be lost.
  - You are about to drop the column `evidenceDetails` on the `StripeDispute` table. All the data in the column will be lost.
  - You are about to drop the column `paymentIntentId` on the `StripeDispute` table. All the data in the column will be lost.
  - You are about to drop the column `reason` on the `StripeDispute` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `StripeDispute` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `StripeDispute` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[stripeDisputeId]` on the table `StripeDispute` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `stripeDisputeId` to the `StripeDispute` table without a default value. This is not possible if the table is not empty.
  - Added the required column `stripePaymentIntentId` to the `StripeDispute` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `disputeId` on the `StripeDispute` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- DropForeignKey
ALTER TABLE "StripeDispute" DROP CONSTRAINT "StripeDispute_paymentIntentId_fkey";

-- AlterTable
ALTER TABLE "StripeDispute" DROP COLUMN "amount",
DROP COLUMN "createdAt",
DROP COLUMN "currency",
DROP COLUMN "evidence",
DROP COLUMN "evidenceDetails",
DROP COLUMN "paymentIntentId",
DROP COLUMN "reason",
DROP COLUMN "status",
DROP COLUMN "updatedAt",
ADD COLUMN     "stripeDisputeId" VARCHAR NOT NULL,
ADD COLUMN     "stripePaymentIntentId" VARCHAR NOT NULL,
DROP COLUMN "disputeId",
ADD COLUMN     "disputeId" UUID NOT NULL;

-- CreateTable
CREATE TABLE "Dispute" (
    "id" UUID NOT NULL,
    "paymentIntentId" UUID NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "currency" VARCHAR NOT NULL,
    "status" VARCHAR NOT NULL,
    "reason" VARCHAR NOT NULL,
    "gatewayId" UUID NOT NULL,
    "evidence" JSONB NOT NULL,
    "evidenceDetails" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Dispute_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "StripeDispute_disputeId_key" ON "StripeDispute"("disputeId");

-- CreateIndex
CREATE UNIQUE INDEX "StripeDispute_stripeDisputeId_key" ON "StripeDispute"("stripeDisputeId");

-- AddForeignKey
ALTER TABLE "StripeDispute" ADD CONSTRAINT "StripeDispute_disputeId_fkey" FOREIGN KEY ("disputeId") REFERENCES "Dispute"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Dispute" ADD CONSTRAINT "Dispute_gatewayId_fkey" FOREIGN KEY ("gatewayId") REFERENCES "Gateway"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Dispute" ADD CONSTRAINT "Dispute_paymentIntentId_fkey" FOREIGN KEY ("paymentIntentId") REFERENCES "PaymentIntent"("id") ON DELETE CASCADE ON UPDATE CASCADE;
