// /* eslint-disable max-len */
// import { Prism<PERSON>, PrismaClient } from '@prisma/client';

// import { DatabaseConfig } from '@/config/database.config';
// import { AllowedCurrencies, GatewayType } from '@/domain/shared';
// import { ManagedSecretProviderType } from '@/infrastructure/shared/enums';

// const prisma = new PrismaClient({ datasourceUrl: DatabaseConfig.databaseUrl });

// const secretKeyData = {
//   id: '3a959a96-504d-4f8d-85e6-61036b8e5e1f',
//   hash: 'aa00c23e10ec04ef31f84fb8de6ca388a19da3089e9afc3007a45a49186fce6f',
//   idAtProvider:
//     'arn:aws:secretsmanager:us-east-1:930397445582:secret:02d1a1cf-dc01-41b2-8616-d157e137b868-fWU79s',
//   providerType: 'aws',
// } satisfies Prisma.ManagedSecretUncheckedCreateInput;

// const webhookSecretData = {
//   id: '74393160-397d-43e0-93f7-5785cc658468',
//   hash: 'f25438535a6bcbbead4f29c00127126bf5e10db873af7e9fa50b1d6d4ec9bddd',
//   idAtProvider:
//     'arn:aws:secretsmanager:us-east-1:930397445582:secret:68d34e18-e69a-491c-9809-5b3839beaec0-oQK3Qq',
//   providerType: ManagedSecretProviderType.AWS,
// } satisfies Prisma.ManagedSecretUncheckedCreateInput;

// const marketplaceData = {
//   id: '03ebb84c-e5f3-4fe6-9769-3623d19b2419',
//   webhookUrl: 'https://my.dev.swstage.com/api/payment-hub/webhook',
//   publishableKey:
//     'pk_5c15a8e9ba48e9bcd481c76cc6fd8434c83539a4af5173fbb3c89fd8a69858723f61144088a057de1aae2d8bb7e9fd8a',
//   name: 'Yevhenii Platform',
//   secretKeyId: secretKeyData.id,
//   webhookSecretId: webhookSecretData.id,
// } satisfies Prisma.MarketplaceUncheckedCreateInput;

// const accounts = [
//   {
//     id: '0d346630-5317-42a1-82ff-a885e44c95fa',
//     marketplaceId: marketplaceData.id,
//     currency: AllowedCurrencies.USD,
//   },
// ] satisfies Prisma.AccountUncheckedCreateInput[];

// const gateway = {
//   id: '32b110dc-575f-4f4b-8504-8499ff6f20bd',
//   name: 'stripe',
//   type: GatewayType.STRIPE,
//   feeFixed: 30,
//   feePercentage: 0.0205,
// } as Prisma.GatewayUncheckedCreateInput;

// const marketplaceGateway = {
//   id: '16aaaf0c-9df3-4878-883b-e4786bb5ca89',
//   balance: 0,
//   currency: AllowedCurrencies.USD,
//   marketplaceId: marketplaceData.id,
//   gatewayId: gateway.id,
//   platformPaymentFeeFixed: 30,
//   platformPaymentFeePercentage: 0.026,
//   marketplacePaymentFeeFixed: 30,
//   marketplacePaymentFeePercentage: 0.029,
// } as Prisma.MarketplaceGatewayUncheckedCreateInput;

// const marketplaceGatewayAccount = {
//   id: '0f208194-8c68-40ef-aa8d-54676e4b9952',
//   idAtGateway: 'acct_1KPRxDHiLj7a1UJR',
//   marketplaceGatewayId: marketplaceGateway.id,
// } as Prisma.MarketplaceGatewayAccountUncheckedCreateInput;

// const gatewayAccounts = [
//   {
//     id: '32b110dc-575f-4f4b-8504-8499ff6f20bd',
//     accountId: accounts[0].id,
//     idAtGateway: 'acct_1KPRxDHiLj7a1UJR',
//     gatewayId: gateway.id,
//   },
// ] satisfies Prisma.GatewayAccountUncheckedCreateInput[];

// async function main() {
//   await prisma.managedSecret.upsert({
//     create: secretKeyData,
//     update: secretKeyData,
//     where: {
//       id: secretKeyData.id,
//     },
//   });

//   await prisma.managedSecret.upsert({
//     create: webhookSecretData,
//     update: webhookSecretData,
//     where: {
//       id: webhookSecretData.id,
//     },
//   });

//   await prisma.marketplace.upsert({
//     create: marketplaceData,
//     update: marketplaceData,
//     where: {
//       id: marketplaceData.id,
//     },
//   });

//   await prisma.gateway.upsert({
//     create: gateway,
//     update: gateway,
//     where: {
//       id: gateway.id,
//     },
//   });

//   await prisma.marketplaceGateway.upsert({
//     create: marketplaceGateway,
//     update: marketplaceGateway,
//     where: {
//       id: marketplaceGateway.id,
//     },
//   });

//   await prisma.marketplaceGatewayAccount.upsert({
//     create: marketplaceGatewayAccount,
//     update: marketplaceGatewayAccount,
//     where: {
//       id: marketplaceGatewayAccount.id,
//     },
//   });

//   await Promise.all(
//     accounts.map(async (account) =>
//       prisma.account.upsert({
//         create: account,
//         update: account,
//         where: {
//           id: account.id,
//         },
//       }),
//     ),
//   );

//   await Promise.all(
//     gatewayAccounts.map(async (gatewayAccount) =>
//       prisma.gatewayAccount.upsert({
//         create: gatewayAccount,
//         update: gatewayAccount,
//         where: {
//           id: gatewayAccount.id,
//         },
//       }),
//     ),
//   );
// }

// main()
//   .then(async () => {
//     await prisma.$disconnect();
//   })
//   .catch(async (e) => {
//     console.error(e);
//     await prisma.$disconnect();
//     process.exit(1);
//   });
