/* eslint-disable max-len */
import { Prisma, PrismaClient } from '@prisma/client';

import { DatabaseConfig } from '@/config/database.config';
import { AllowedCurrencies, GatewayType } from '@/domain/shared';
import { ManagedSecretProviderType } from '@/infrastructure/shared/enums';

const prisma = new PrismaClient({ datasourceUrl: DatabaseConfig.databaseUrl });

const gateway = {
  id: '01J2TDBGMSE3F30ZS0K255K6XS',
  name: 'stripe',
  type: GatewayType.STRIPE,
  feeFixed: 30,
  feePercentage: 0.0205,
} as Prisma.GatewayUncheckedCreateInput;

const secretKeyData = {
  id: '01J2TD9M3P2WK270WNN72DQ6ZZ',
  hash: '42d420609743860edb2fa306a2b97ceab22e854d1add39512bd24e8ab7d03c43',
  idAtProvider:
    'arn:aws:secretsmanager:us-east-1:930397445582:secret:fd26d2c4-cc77-4c19-a6bd-4280a974d2b9-dizVb3',
  providerType: 'aws',
} satisfies Prisma.ManagedSecretUncheckedCreateInput;

const webhookSecretData = {
  id: '01J2TDA2Z3A55E90HPBRYWGBMQ',
  hash: 'ff6939fc6d0385f2aaff5de66dd1e8f7aa8e36a54e12c35e9a81b8e2a897de1a',
  idAtProvider:
    'arn:aws:secretsmanager:us-east-1:930397445582:secret:4e15afa0-f954-4ff4-b57f-c6ab8c04e059-pkrfRK',
  providerType: ManagedSecretProviderType.AWS,
} satisfies Prisma.ManagedSecretUncheckedCreateInput;

const marketplaceData = {
  id: '01J2TDA99C15ZCKQXXRV5HCR4R',
  webhookUrl: 'https://my.dev.swstage.com/api/payment-hub/webhook',
  publishableKey:
    'pk_5afe945d238ec8f5d5df6d388b23dfe3b5dc4e31a730af8e5ee873817f01be28812e0643bc71dfe4d201082c71ecc2dd',
  name: 'SW',
  secretKeyId: secretKeyData.id,
  webhookSecretId: webhookSecretData.id,
} satisfies Prisma.MarketplaceUncheckedCreateInput;

const marketplaceGateway = {
  id: '01J2TDAGEKZN58BAF7VZB2H467',
  balance: 0,
  currency: AllowedCurrencies.USD,
  marketplaceId: marketplaceData.id,
  gatewayId: gateway.id,
  platformPaymentFeeFixed: 30,
  platformPaymentFeePercentage: 0.026,
  marketplacePaymentFeeFixed: 30,
  marketplacePaymentFeePercentage: 0.029,
} as Prisma.MarketplaceGatewayUncheckedCreateInput;

const accounts = [
  {
    id: '01J2TDAQJ2ZQ5Z7SBTSDFK6FHY',
    marketplaceId: marketplaceData.id,
    currency: AllowedCurrencies.USD,
  },
  {
    id: '01J2TDAYZ4Z0M8W9JK32PRK31M',
    marketplaceId: marketplaceData.id,
    currency: AllowedCurrencies.USD,
  },
  {
    id: '01J2TDB4S42RG9WPK4WHEM5WP6',
    marketplaceId: marketplaceData.id,
    currency: AllowedCurrencies.USD,
  },
] satisfies Prisma.AccountUncheckedCreateInput[];

const gatewayAccounts = [
  {
    id: '01J2TDBRRS9R5RM22D833BXPEG',
    accountId: accounts[0].id,
    // idAtGateway: 'acct_1H1ygCKfBxc9fIGA',
    idAtGateway: 'acct_1O2YIbJqzlSpNBAh', // LEV
    gatewayId: gateway.id,
  },
  {
    id: '01J2TDBYKN7SMKPQRZH5FM9YV0',
    accountId: accounts[1].id,
    idAtGateway: 'acct_1P1ovPP86xqqy066', // Tanya
    gatewayId: gateway.id,
  },
  {
    id: '01J2TDC5Q50WW2NE7RW7TE3HZY',
    accountId: accounts[2].id,
    idAtGateway: 'acct_1KgsHkLDYnTA1fGW',
    gatewayId: gateway.id,
  },
] satisfies Prisma.GatewayAccountUncheckedCreateInput[];

const marketplaceGatewayAccount = {
  id: '01J2TDCDJR6YFR4FAQBQBFBFF8',
  idAtGateway: 'acct_1KgsHkLDYnTA1fGW',
  marketplaceGatewayId: marketplaceGateway.id,
} as Prisma.MarketplaceGatewayAccountUncheckedCreateInput;

async function main() {
  await prisma.managedSecret.upsert({
    create: secretKeyData,
    update: secretKeyData,
    where: {
      id: secretKeyData.id,
    },
  });

  await prisma.managedSecret.upsert({
    create: webhookSecretData,
    update: webhookSecretData,
    where: {
      id: webhookSecretData.id,
    },
  });

  await prisma.marketplace.upsert({
    create: marketplaceData,
    update: marketplaceData,
    where: {
      id: marketplaceData.id,
    },
  });

  await prisma.gateway.upsert({
    create: gateway,
    update: gateway,
    where: {
      id: gateway.id,
    },
  });

  await prisma.marketplaceGateway.upsert({
    create: marketplaceGateway,
    update: marketplaceGateway,
    where: {
      id: marketplaceGateway.id,
    },
  });

  await prisma.marketplaceGatewayAccount.upsert({
    create: marketplaceGatewayAccount,
    update: marketplaceGatewayAccount,
    where: {
      id: marketplaceGatewayAccount.id,
    },
  });

  await Promise.all(
    accounts.map(async (account) =>
      prisma.account.upsert({
        create: account,
        update: account,
        where: {
          id: account.id,
        },
      }),
    ),
  );

  await Promise.all(
    gatewayAccounts.map(async (gatewayAccount) =>
      prisma.gatewayAccount.upsert({
        create: gatewayAccount,
        update: gatewayAccount,
        where: {
          id: gatewayAccount.id,
        },
      }),
    ),
  );
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
