import { ConflictException } from '@nestjs/common';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { ILogger } from '@sw-web/nestjs-core/logger';

import { DatabaseService, isUniqueConstraintError } from '@/database';

import { AggregateRoot } from '../ddd/aggregate-root.base';
import { Paginated, PaginatedQueryParams, IRepository } from '../ddd/repository.interface';

export abstract class SqlRepositoryBase<Aggregate extends AggregateRoot<unknown>>
  implements IRepository<Aggregate>
{
  protected constructor(
    protected readonly db: DatabaseService,
    protected readonly eventBus: EventBus,
    protected readonly logger: ILogger,
  ) {}

  protected abstract createImpl(entity: Aggregate): Promise<void>;

  async create(entity: Aggregate): Promise<void> {
    try {
      await this.createImpl(entity);
      await entity.publishEvents(this.logger, this.eventBus);
    } catch (error) {
      if (isUniqueConstraintError(error)) {
        this.logger.debug(error);
        throw new ConflictException('Record already exists', error);
      }
      throw error;
    }
  }

  protected abstract updateImpl(entity: Aggregate): Promise<void>;

  async update(entity: Aggregate): Promise<void> {
    await this.updateImpl(entity);
    await entity.publishEvents(this.logger, this.eventBus);
  }

  protected abstract deleteImpl(entity: Aggregate): Promise<boolean>;

  async delete(entity: Aggregate): Promise<boolean> {
    const isDeleted = await this.deleteImpl(entity);

    if (isDeleted) {
      await entity.publishEvents(this.logger, this.eventBus);
    }

    return isDeleted;
  }

  abstract findById(id: string): Promise<Aggregate>;

  abstract findAll(): Promise<Aggregate[]>;

  abstract findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<Aggregate>>;

  async transaction<T>(handler: () => Promise<T>): Promise<T> {
    return this.db.$transaction(handler);
  }
}
