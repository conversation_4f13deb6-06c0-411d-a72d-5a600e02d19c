export enum QueueName {
  WEBHOOK_SENDER = 'webhook-sender',
  STRIPE_WEBHOOK_HANDLER = 'stripe-webhook-handler',
}

type RabbitMQRoutingKey = {
  name: string;
  queues?: Record<string, { name: string }>;
};

type RabbitMQExchange = Record<
  string,
  {
    name: string;
    type: 'direct' | 'topic' | 'fanout';
    routingKeys?: Record<string, RabbitMQRoutingKey>;
  }
>;

export const RABBIT_MQ_EXCHANGES = {
  webhookSender: {
    name: 'webhook.sender',
    type: 'topic',
    routingKeys: {
      webhookSend: {
        name: 'webhook.send',
        queues: {
          webhookSender: {
            name: 'webhook.sender',
          },
        },
      },
    },
  },
  payment: {
    name: 'payment',
    type: 'topic',
    routingKeys: {
      paymentSettled: {
        name: 'payment.settled',
        queues: {
          processPaymentSplits: {
            name: 'process.payment.splits',
          },
        },
      },
      refundIssued: {
        name: 'refund.issued',
        queues: {
          processPaymentSplitRefunds: {
            name: 'process.payment.split.refunds',
          },
          processDisputePaymentSplitRefunds: {
            name: 'process.dispute.payment.split.refunds',
          },
          createPayoutRefundReversals: {
            name: 'create.payout.refund.reversals',
          },
        },
      },
      refundFailed: {
        name: 'refund.failed',
        queues: {
          processRefundFailure: {
            name: 'process.refund.failure',
          },
        },
      },
      refundCanceled: {
        name: 'refund.canceled',
        queues: {
          refundCanceled: {
            name: 'refund.canceled',
          },
        },
      },
    },
  },
  payout: {
    name: 'payout',
    type: 'topic',
    routingKeys: {
      payoutCreated: {
        name: 'payout.created',
        queues: {
          processPayout: {
            name: 'process.payout',
          },
        },
      },
      payoutReversalCreated: {
        name: 'payout.reversal.created',
        queues: {
          processPayoutReversal: {
            name: 'process.payout.reversal',
          },
        },
      },
    },
  },
  gatewayWebhook: {
    name: 'gateway.webhook',
    type: 'topic',
    routingKeys: {
      stripe: {
        name: 'stripe',
        queues: {
          stripeWebhook: {
            name: 'stripe.webhook',
          },
        },
      },
    },
  },
} as const satisfies RabbitMQExchange;
