{"compilerOptions": {"module": "Node16", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "moduleResolution": "node16", "esModuleInterop": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "strict": false, "paths": {"@/*": ["src/*"], "@/libs/stripe": ["libs/stripe/src"], "@/libs/stripe/*": ["libs/stripe/src/*"], "@/test/*": ["test/*"]}}, "include": ["src/**/*.ts", "libs/**/*.ts", "test/**/*.ts", "scripts/**/*.ts"], "exclude": ["node_modules", "dist"]}